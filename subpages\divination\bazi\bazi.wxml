<!--subpages/divination/bazi/bazi.wxml-->
<view class="bazi-container">
  <!-- 页面头部 -->
  <view class="bazi-header">
    <image class="header-bg" src="/images/bazi-bg.jpg" mode="aspectFill"></image>
    <view class="header-content">
      <text class="header-title">八字分析</text>
      <text class="header-subtitle">解析您的生辰八字，洞悉命运玄机</text>
    </view>
  </view>

  <!-- 输入表单 -->
  <view class="bazi-form">
    <view class="form-card">
      <view class="form-title">
        <text class="title-text">请输入您的出生信息</text>
        <text class="title-tips">精确的时间有助于更准确的分析</text>
      </view>

      <view class="form-group">
        <text class="form-label">姓名</text>
        <input class="form-input" placeholder="请输入您的姓名" bindinput="onNameInput" value="{{name}}" />
      </view>

      <view class="form-group">
        <text class="form-label">性别</text>
        <view class="gender-select">
          <view class="gender-option {{gender === 'male' ? 'active' : ''}}" bindtap="selectGender" data-gender="male">
            <text class="gender-icon">♂</text>
            <text>男</text>
          </view>
          <view class="gender-option {{gender === 'female' ? 'active' : ''}}" bindtap="selectGender" data-gender="female">
            <text class="gender-icon">♀</text>
            <text>女</text>
          </view>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">出生日期</text>
        <picker mode="date" value="{{birthDate}}" bindchange="onDateChange">
          <view class="picker-display">
            <text class="picker-value">{{birthDate || '请选择出生日期'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">出生时间</text>
        <picker mode="time" value="{{birthTime}}" bindchange="onTimeChange">
          <view class="picker-display">
            <text class="picker-value">{{birthTime || '请选择出生时间'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">出生地点</text>
        <input class="form-input" placeholder="请输入出生地点（选填）" bindinput="onLocationInput" value="{{location}}" />
      </view>

      <button class="analyze-btn" bindtap="analyzeBazi" loading="{{analyzing}}" disabled="{{analyzing}}">
        {{analyzing ? '分析中...' : '开始分析'}}
      </button>
    </view>
  </view>

  <!-- 分析结果 -->
  <view class="bazi-result" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="result-header">
        <text class="result-title">八字命盘</text>
        <text class="result-name">{{resultData.name}} {{resultData.gender === 'male' ? '先生' : '女士'}}</text>
      </view>

      <!-- 八字展示 -->
      <view class="bazi-display">
        <view class="bazi-row">
          <view class="bazi-item" wx:for="{{resultData.tianGan}}" wx:key="index">
            <text class="bazi-label">{{item.label}}</text>
            <text class="bazi-char tian">{{item.value}}</text>
          </view>
        </view>
        <view class="bazi-row">
          <view class="bazi-item" wx:for="{{resultData.diZhi}}" wx:key="index">
            <text class="bazi-char di">{{item.value}}</text>
          </view>
        </view>
      </view>

      <!-- 五行分析 -->
      <view class="wuxing-section">
        <text class="section-title">五行分析</text>
        <view class="wuxing-chart">
          <view class="wuxing-item" wx:for="{{resultData.wuxing}}" wx:key="name">
            <view class="wuxing-bar" style="height: {{item.percentage}}%">
              <text class="wuxing-value">{{item.count}}</text>
            </view>
            <text class="wuxing-name">{{item.name}}</text>
          </view>
        </view>
        <text class="wuxing-desc">{{resultData.wuxingDesc}}</text>
      </view>

      <!-- 命理解析 -->
      <view class="analysis-section">
        <text class="section-title">命理解析</text>
        
        <view class="analysis-item">
          <text class="analysis-label">性格特征</text>
          <text class="analysis-content">{{resultData.personality}}</text>
        </view>

        <view class="analysis-item">
          <text class="analysis-label">事业运势</text>
          <text class="analysis-content">{{resultData.career}}</text>
        </view>

        <view class="analysis-item">
          <text class="analysis-label">财运分析</text>
          <text class="analysis-content">{{resultData.wealth}}</text>
        </view>

        <view class="analysis-item">
          <text class="analysis-label">感情婚姻</text>
          <text class="analysis-content">{{resultData.love}}</text>
        </view>

        <view class="analysis-item">
          <text class="analysis-label">健康建议</text>
          <text class="analysis-content">{{resultData.health}}</text>
        </view>
      </view>

      <!-- 运势趋势 -->
      <view class="fortune-section">
        <text class="section-title">近期运势</text>
        <view class="fortune-timeline">
          <view class="fortune-item" wx:for="{{resultData.fortune}}" wx:key="year">
            <text class="fortune-year">{{item.year}}</text>
            <view class="fortune-bar">
              <view class="fortune-fill" style="width: {{item.score}}%"></view>
            </view>
            <text class="fortune-score">{{item.score}}分</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="result-actions">
        <button class="action-btn secondary" bindtap="saveResult">保存结果</button>
        <button class="action-btn primary" bindtap="shareResult">分享结果</button>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{historyList.length > 0}}">
    <view class="section-header">
      <text class="section-title">历史记录</text>
      <text class="section-more" bindtap="viewAllHistory">查看全部</text>
    </view>
    <view class="history-list">
      <view class="history-item" wx:for="{{historyList}}" wx:key="id" bindtap="viewHistory" data-id="{{item.id}}">
        <view class="history-info">
          <text class="history-name">{{item.name}}</text>
          <text class="history-date">{{item.date}}</text>
        </view>
        <text class="history-arrow">›</text>
      </view>
    </view>
  </view>
</view> 