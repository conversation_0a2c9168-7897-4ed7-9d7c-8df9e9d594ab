// 八卦方位数据
const BAGUA_DATA = {
  '乾': { direction: '西北', element: '金', nature: '天' },
  '兑': { direction: '西', element: '金', nature: '泽' },
  '离': { direction: '南', element: '火', nature: '火' },
  '震': { direction: '东', element: '木', nature: '雷' },
  '巽': { direction: '东南', element: '木', nature: '风' },
  '坎': { direction: '北', element: '水', nature: '水' },
  '艮': { direction: '东北', element: '土', nature: '山' },
  '坤': { direction: '西南', element: '土', nature: '地' }
};

// 五行相生相克关系
const WUXING_RELATIONS = {
  metal: { generates: 'water', restricts: 'wood' },
  water: { generates: 'wood', restricts: 'fire' },
  wood: { generates: 'fire', restricts: 'earth' },
  fire: { generates: 'earth', restricts: 'metal' },
  earth: { generates: 'metal', restricts: 'water' }
};

// 房间功能建议
const ROOM_SUGGESTIONS = {
  bedroom: {
    auspicious: ['西南', '东北', '西北'],
    inauspicious: ['南', '东南']
  },
  livingroom: {
    auspicious: ['南', '东'],
    inauspicious: ['北', '西']
  },
  kitchen: {
    auspicious: ['东', '南'],
    inauspicious: ['西', '北']
  },
  bathroom: {
    auspicious: ['西', '北'],
    inauspicious: ['东', '南']
  },
  study: {
    auspicious: ['东北', '西南'],
    inauspicious: ['西北', '东南']
  }
};

class FengshuiCalculator {
  constructor() {
    this.baguaData = BAGUA_DATA;
    this.wuxingRelations = WUXING_RELATIONS;
    this.roomSuggestions = ROOM_SUGGESTIONS;
  }

  async calculate(params) {
    const { direction, houseType, buildYear, area } = params;

    try {
      // 计算八卦方位
      const bagua = this.calculateBagua(direction);

      // 计算吉凶方位
      const positions = this.calculatePositions(direction, houseType);

      // 生成布局建议
      const layout = this.generateLayoutAdvice(positions.auspicious, area);

      // 生成化解方案
      const solutions = this.generateSolutions(positions.inauspicious);

      return {
        bagua,
        positions,
        layout,
        solutions
      };
    } catch (error) {
      console.error('风水计算错误:', error);
      throw new Error('风水计算失败');
    }
  }

  calculateBagua(direction) {
    const bagua = [];
    const mainDirection = this.getMainDirection(direction);
    
    for (const [name, data] of Object.entries(this.baguaData)) {
      bagua.push({
        name,
        direction: data.direction,
        element: data.element,
        isActive: data.direction === mainDirection
      });
    }

    return bagua;
  }

  getMainDirection(direction) {
    // 从"坐X朝Y"格式中提取主要朝向
    const match = direction.match(/朝(.*)/);
    return match ? match[1] : '';
  }

  calculatePositions(direction, houseType) {
    const mainDirection = this.getMainDirection(direction);
    const positions = {
      auspicious: [],
      inauspicious: []
    };

    // 根据房屋类型和方向计算吉凶方位
    switch (houseType) {
      case '住宅':
        this.calculateResidentialPositions(mainDirection, positions);
        break;
      case '办公':
        this.calculateOfficePositions(mainDirection, positions);
        break;
      case '商铺':
        this.calculateShopPositions(mainDirection, positions);
        break;
      case '厂房':
        this.calculateFactoryPositions(mainDirection, positions);
        break;
    }

    return positions;
  }

  calculateResidentialPositions(mainDirection, positions) {
    // 住宅吉凶方位计算
    const auspiciousDirections = {
      '南': ['客厅', '餐厅'],
      '东': ['书房', '儿童房'],
      '北': ['主卧', '次卧'],
      '西': ['厨房', '储藏室']
    };

    const inauspiciousDirections = {
      '东南': ['厕所', '垃圾房'],
      '西北': ['厨房', '明火位置'],
      '西南': ['病房', '暗房'],
      '东北': ['厕所', '污水管道']
    };

    for (const [direction, usage] of Object.entries(auspiciousDirections)) {
      if (this.isCompatibleDirection(mainDirection, direction)) {
        positions.auspicious.push({
          direction,
          recommendedUsage: usage.join('、'),
          description: `此方位利于${usage.join('、')}的设置，可增进家人运势。`
        });
      }
    }

    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {
      if (!this.isCompatibleDirection(mainDirection, direction)) {
        positions.inauspicious.push({
          direction,
          avoidance: `避免设置${usage.join('、')}`,
          description: `此方位不宜设置${usage.join('、')}，以免影响家运。`
        });
      }
    }
  }

  calculateOfficePositions(mainDirection, positions) {
    // 办公场所吉凶方位计算
    const auspiciousDirections = {
      '南': ['会议室', '接待区'],
      '东': ['主管办公室', '创意区'],
      '北': ['财务室', '档案室'],
      '西': ['员工工位', '休息区']
    };

    const inauspiciousDirections = {
      '东南': ['卫生间', '杂物间'],
      '西北': ['打印室', '设备间'],
      '西南': ['走廊', '通道'],
      '东北': ['储藏室', '管道间']
    };

    for (const [direction, usage] of Object.entries(auspiciousDirections)) {
      if (this.isCompatibleDirection(mainDirection, direction)) {
        positions.auspicious.push({
          direction,
          recommendedUsage: usage.join('、'),
          description: `此方位适合设置${usage.join('、')}，有助于提升工作效率。`
        });
      }
    }

    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {
      if (!this.isCompatibleDirection(mainDirection, direction)) {
        positions.inauspicious.push({
          direction,
          avoidance: `避免设置${usage.join('、')}`,
          description: `此方位不宜设置${usage.join('、')}，以免影响办公环境。`
        });
      }
    }
  }

  calculateShopPositions(mainDirection, positions) {
    // 商铺吉凶方位计算
    const auspiciousDirections = {
      '南': ['收银台', '展示区'],
      '东': ['主要商品区', '品牌展示'],
      '北': ['仓储区', '办公区'],
      '西': ['休息区', '试衣间']
    };

    const inauspiciousDirections = {
      '东南': ['卫生间', '垃圾间'],
      '西北': ['仓库', '杂物间'],
      '西南': ['员工通道', '后门'],
      '东北': ['设备间', '管道间']
    };

    for (const [direction, usage] of Object.entries(auspiciousDirections)) {
      if (this.isCompatibleDirection(mainDirection, direction)) {
        positions.auspicious.push({
          direction,
          recommendedUsage: usage.join('、'),
          description: `此方位适合设置${usage.join('、')}，有利于招财进宝。`
        });
      }
    }

    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {
      if (!this.isCompatibleDirection(mainDirection, direction)) {
        positions.inauspicious.push({
          direction,
          avoidance: `避免设置${usage.join('、')}`,
          description: `此方位不宜设置${usage.join('、')}，以免影响生意。`
        });
      }
    }
  }

  calculateFactoryPositions(mainDirection, positions) {
    // 厂房吉凶方位计算
    const auspiciousDirections = {
      '南': ['成品区', '展示区'],
      '东': ['生产线', '加工区'],
      '北': ['原料区', '仓储区'],
      '西': ['办公区', '休息区']
    };

    const inauspiciousDirections = {
      '东南': ['危险品存放', '废料区'],
      '西北': ['污水处理', '垃圾站'],
      '西南': ['设备间', '变电室'],
      '东北': ['化学品', '易燃物']
    };

    for (const [direction, usage] of Object.entries(auspiciousDirections)) {
      if (this.isCompatibleDirection(mainDirection, direction)) {
        positions.auspicious.push({
          direction,
          recommendedUsage: usage.join('、'),
          description: `此方位适合设置${usage.join('、')}，有利于生产效率。`
        });
      }
    }

    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {
      if (!this.isCompatibleDirection(mainDirection, direction)) {
        positions.inauspicious.push({
          direction,
          avoidance: `避免设置${usage.join('、')}`,
          description: `此方位不宜设置${usage.join('、')}，以免影响安全生产。`
        });
      }
    }
  }

  isCompatibleDirection(mainDirection, direction) {
    // 判断方位是否相容
    const compatibleMap = {
      '南': ['东', '西'],
      '北': ['东', '西'],
      '东': ['南', '北'],
      '西': ['南', '北'],
      '东南': ['东北', '西南'],
      '西北': ['东北', '西南'],
      '东北': ['东南', '西北'],
      '西南': ['东南', '西北']
    };

    return compatibleMap[mainDirection]?.includes(direction) || mainDirection === direction;
  }

  generateLayoutAdvice(auspiciousPositions, area) {
    const layout = [];
    const roomTypes = {
      small: ['卧室', '书房', '卫生间'],
      medium: ['客厅', '餐厅', '厨房'],
      large: ['主卧', '活动室']
    };

    // 根据面积分配房间
    if (area < 60) {
      this.addRoomAdvice(layout, roomTypes.small, auspiciousPositions);
    } else if (area < 100) {
      this.addRoomAdvice(layout, [...roomTypes.small, ...roomTypes.medium], auspiciousPositions);
    } else {
      this.addRoomAdvice(layout, [...roomTypes.small, ...roomTypes.medium, ...roomTypes.large], auspiciousPositions);
    }

    return layout;
  }

  addRoomAdvice(layout, rooms, auspiciousPositions) {
    rooms.forEach((room, index) => {
      const position = auspiciousPositions[index % auspiciousPositions.length];
      if (position) {
        layout.push({
          room,
          suggestion: `建议将${room}设置在${position.direction}方向，${position.description}`
        });
      }
    });
  }

  generateSolutions(inauspiciousPositions) {
    const solutions = [];

    inauspiciousPositions.forEach(position => {
      solutions.push({
        type: '方位调整',
        description: `对于${position.direction}方向的${position.avoidance}，建议调整到其他方位或采用以下化解方法：`
      });

      // 添加具体的化解方案
      solutions.push({
        type: '装饰化解',
        description: `在${position.direction}方向可以摆放五行相生的物品，如金属饰品、水晶、植物等，以调和不利因素。`
      });

      solutions.push({
        type: '功能改造',
        description: `可以考虑将${position.direction}方向的空间改造为储物间或过渡空间，减少人员在此停留时间。`
      });
    });

    return solutions;
  }
}

module.exports = {
  FengshuiCalculator
}; 