# 后台管理系统UI组件规范

## 📋 概述

本文档定义了后台管理系统中所有可复用的UI组件规范，包括组件的属性、样式、交互行为和使用方法。

## 🎨 设计系统

### 色彩规范
```javascript
{
  "colors": {
    "primary": "#667eea",
    "secondary": "#764ba2", 
    "success": "#10b981",
    "warning": "#f59e0b",
    "error": "#ef4444",
    "info": "#3b82f6",
    "text": {
      "primary": "#111827",
      "secondary": "#6b7280",
      "disabled": "#9ca3af"
    },
    "background": {
      "primary": "#ffffff",
      "secondary": "#f9fafb",
      "tertiary": "#f3f4f6"
    },
    "border": {
      "light": "#e5e7eb",
      "medium": "#d1d5db",
      "dark": "#9ca3af"
    }
  }
}
```

### 字体规范
```javascript
{
  "typography": {
    "fontFamily": {
      "sans": ["Inter", "system-ui", "sans-serif"],
      "mono": ["JetBrains Mono", "monospace"]
    },
    "fontSize": {
      "xs": "12px",
      "sm": "14px",
      "base": "16px",
      "lg": "18px",
      "xl": "20px",
      "2xl": "24px",
      "3xl": "30px"
    },
    "fontWeight": {
      "normal": 400,
      "medium": 500,
      "semibold": 600,
      "bold": 700
    }
  }
}
```

## 📊 数据展示组件

### 1. 统计卡片 (StatCard)
```javascript
{
  "component": "StatCard",
  "props": {
    "title": {
      "type": "string",
      "required": true,
      "description": "卡片标题"
    },
    "value": {
      "type": "string|number",
      "required": true,
      "description": "主要数值"
    },
    "trend": {
      "type": "object",
      "properties": {
        "value": "number",
        "type": "up|down|stable",
        "period": "string"
      },
      "description": "趋势信息"
    },
    "icon": {
      "type": "string",
      "description": "图标名称"
    },
    "color": {
      "type": "string",
      "enum": ["blue", "green", "purple", "orange", "red"],
      "default": "blue"
    },
    "loading": {
      "type": "boolean",
      "default": false
    },
    "onClick": {
      "type": "function",
      "description": "点击事件"
    }
  },
  "example": {
    "title": "总用户数",
    "value": "12,345",
    "trend": {
      "value": 12.5,
      "type": "up",
      "period": "较上月"
    },
    "icon": "users",
    "color": "blue",
    "onClick": "() => navigate('/users')"
  }
}
```

### 2. 数据表格 (DataTable)
```javascript
{
  "component": "DataTable",
  "props": {
    "columns": {
      "type": "array",
      "required": true,
      "items": {
        "key": "string",
        "title": "string",
        "dataIndex": "string",
        "width": "number",
        "render": "string|function",
        "sorter": "boolean",
        "filters": "array",
        "fixed": "left|right"
      }
    },
    "dataSource": {
      "type": "array",
      "description": "数据源"
    },
    "api": {
      "type": "string",
      "description": "API接口地址"
    },
    "rowKey": {
      "type": "string",
      "default": "id"
    },
    "selection": {
      "type": "boolean|object",
      "description": "是否支持选择"
    },
    "pagination": {
      "type": "object",
      "properties": {
        "pageSize": "number",
        "showSizeChanger": "boolean",
        "showQuickJumper": "boolean"
      }
    },
    "loading": {
      "type": "boolean",
      "default": false
    },
    "expandable": {
      "type": "boolean",
      "description": "是否支持展开"
    }
  }
}
```

### 3. 图表组件 (Chart)
```javascript
{
  "component": "Chart",
  "props": {
    "type": {
      "type": "string",
      "enum": ["line", "bar", "pie", "area", "gauge", "funnel", "map"],
      "required": true
    },
    "data": {
      "type": "array|object",
      "description": "图表数据"
    },
    "api": {
      "type": "string",
      "description": "数据API接口"
    },
    "config": {
      "type": "object",
      "description": "图表配置"
    },
    "height": {
      "type": "number",
      "default": 300
    },
    "loading": {
      "type": "boolean",
      "default": false
    },
    "realtime": {
      "type": "boolean",
      "description": "是否实时更新"
    },
    "refreshInterval": {
      "type": "number",
      "description": "刷新间隔(ms)"
    }
  }
}
```

## 📝 表单组件

### 4. 动态表单 (DynamicForm)
```javascript
{
  "component": "DynamicForm",
  "props": {
    "fields": {
      "type": "array",
      "required": true,
      "items": {
        "name": "string",
        "label": "string",
        "type": "string",
        "required": "boolean",
        "rules": "array",
        "options": "array",
        "dependency": "string",
        "help": "string"
      }
    },
    "initialValues": {
      "type": "object",
      "description": "初始值"
    },
    "layout": {
      "type": "string",
      "enum": ["horizontal", "vertical", "inline"],
      "default": "horizontal"
    },
    "onSubmit": {
      "type": "function",
      "required": true
    },
    "loading": {
      "type": "boolean",
      "default": false
    }
  }
}
```

### 5. 搜索栏 (SearchBar)
```javascript
{
  "component": "SearchBar",
  "props": {
    "placeholder": {
      "type": "string",
      "default": "请输入搜索关键词"
    },
    "fields": {
      "type": "array",
      "description": "搜索字段"
    },
    "realtime": {
      "type": "boolean",
      "description": "是否实时搜索"
    },
    "onSearch": {
      "type": "function",
      "required": true
    },
    "onClear": {
      "type": "function"
    }
  }
}
```

### 6. 过滤器 (FilterBar)
```javascript
{
  "component": "FilterBar",
  "props": {
    "filters": {
      "type": "array",
      "items": {
        "field": "string",
        "type": "select|daterange|slider|input",
        "label": "string",
        "options": "array",
        "api": "string",
        "multiple": "boolean"
      }
    },
    "onFilter": {
      "type": "function",
      "required": true
    },
    "onReset": {
      "type": "function"
    }
  }
}
```

## 🎯 操作组件

### 7. 操作按钮组 (ActionButtons)
```javascript
{
  "component": "ActionButtons",
  "props": {
    "actions": {
      "type": "array",
      "items": {
        "text": "string",
        "icon": "string",
        "action": "string|function",
        "style": "primary|secondary|danger",
        "confirm": "boolean",
        "loading": "boolean",
        "disabled": "boolean"
      }
    },
    "size": {
      "type": "string",
      "enum": ["small", "medium", "large"],
      "default": "medium"
    }
  }
}
```

### 8. 批量操作 (BatchActions)
```javascript
{
  "component": "BatchActions",
  "props": {
    "selectedCount": {
      "type": "number",
      "required": true
    },
    "actions": {
      "type": "array",
      "items": {
        "text": "string",
        "action": "function",
        "confirm": "boolean",
        "danger": "boolean"
      }
    },
    "onClear": {
      "type": "function"
    }
  }
}
```

## 📱 布局组件

### 9. 页面容器 (PageContainer)
```javascript
{
  "component": "PageContainer",
  "props": {
    "title": {
      "type": "string",
      "required": true
    },
    "subtitle": {
      "type": "string"
    },
    "breadcrumb": {
      "type": "array",
      "items": "string"
    },
    "extra": {
      "type": "ReactNode",
      "description": "额外操作区域"
    },
    "loading": {
      "type": "boolean",
      "default": false
    }
  }
}
```

### 10. 工具栏 (Toolbar)
```javascript
{
  "component": "Toolbar",
  "props": {
    "search": {
      "type": "object",
      "description": "搜索配置"
    },
    "filters": {
      "type": "array",
      "description": "过滤器配置"
    },
    "actions": {
      "type": "array",
      "description": "操作按钮配置"
    },
    "onSearch": {
      "type": "function"
    },
    "onFilter": {
      "type": "function"
    }
  }
}
```

## 🎨 渲染器组件

### 11. 用户信息渲染器 (UserInfoRenderer)
```javascript
{
  "component": "UserInfoRenderer",
  "props": {
    "user": {
      "type": "object",
      "properties": {
        "id": "string",
        "nickname": "string",
        "avatar_url": "string",
        "phone": "string",
        "email": "string"
      }
    },
    "showAvatar": {
      "type": "boolean",
      "default": true
    },
    "showContact": {
      "type": "boolean",
      "default": false
    },
    "linkable": {
      "type": "boolean",
      "default": true
    }
  }
}
```

### 12. 状态徽章渲染器 (StatusBadgeRenderer)
```javascript
{
  "component": "StatusBadgeRenderer",
  "props": {
    "status": {
      "type": "string|number",
      "required": true
    },
    "statusMap": {
      "type": "object",
      "description": "状态映射配置"
    },
    "colorMap": {
      "type": "object",
      "description": "颜色映射配置"
    }
  },
  "example": {
    "status": "active",
    "statusMap": {
      "active": "活跃",
      "inactive": "非活跃",
      "banned": "已禁用"
    },
    "colorMap": {
      "active": "success",
      "inactive": "warning",
      "banned": "error"
    }
  }
}
```

### 13. 数值渲染器 (NumberRenderer)
```javascript
{
  "component": "NumberRenderer",
  "props": {
    "value": {
      "type": "number",
      "required": true
    },
    "format": {
      "type": "string",
      "enum": ["number", "currency", "percentage", "duration"],
      "default": "number"
    },
    "precision": {
      "type": "number",
      "default": 0
    },
    "prefix": {
      "type": "string"
    },
    "suffix": {
      "type": "string"
    },
    "color": {
      "type": "string",
      "description": "数值颜色"
    }
  }
}
```

### 14. 时间渲染器 (TimeRenderer)
```javascript
{
  "component": "TimeRenderer",
  "props": {
    "time": {
      "type": "string|Date",
      "required": true
    },
    "format": {
      "type": "string",
      "enum": ["datetime", "date", "time", "relative"],
      "default": "datetime"
    },
    "pattern": {
      "type": "string",
      "description": "自定义格式"
    }
  }
}
```

## 🔧 功能组件

### 15. 文件上传 (FileUpload)
```javascript
{
  "component": "FileUpload",
  "props": {
    "accept": {
      "type": "string",
      "description": "接受的文件类型"
    },
    "maxSize": {
      "type": "string",
      "description": "最大文件大小"
    },
    "multiple": {
      "type": "boolean",
      "default": false
    },
    "api": {
      "type": "string",
      "required": true
    },
    "onSuccess": {
      "type": "function"
    },
    "onError": {
      "type": "function"
    }
  }
}
```

### 16. 富文本编辑器 (RichEditor)
```javascript
{
  "component": "RichEditor",
  "props": {
    "value": {
      "type": "string"
    },
    "onChange": {
      "type": "function",
      "required": true
    },
    "toolbar": {
      "type": "array",
      "description": "工具栏配置"
    },
    "plugins": {
      "type": "array",
      "description": "插件配置"
    },
    "upload": {
      "type": "object",
      "description": "上传配置"
    },
    "height": {
      "type": "number",
      "default": 400
    }
  }
}
```

### 17. 代码编辑器 (CodeEditor)
```javascript
{
  "component": "CodeEditor",
  "props": {
    "value": {
      "type": "string"
    },
    "onChange": {
      "type": "function"
    },
    "language": {
      "type": "string",
      "default": "javascript"
    },
    "theme": {
      "type": "string",
      "default": "vs-dark"
    },
    "height": {
      "type": "number",
      "default": 300
    },
    "readOnly": {
      "type": "boolean",
      "default": false
    }
  }
}
```

## 📋 使用示例

### 完整页面示例
```javascript
{
  "page_example": {
    "component": "PageContainer",
    "props": {
      "title": "用户管理",
      "subtitle": "管理所有注册用户",
      "breadcrumb": ["首页", "用户管理"]
    },
    "children": [
      {
        "component": "Toolbar",
        "props": {
          "search": {
            "placeholder": "搜索用户昵称、手机号",
            "fields": ["nickname", "phone"]
          },
          "filters": [
            {
              "field": "status",
              "type": "select",
              "label": "状态",
              "options": [
                {"value": "", "label": "全部"},
                {"value": "0", "label": "正常"},
                {"value": "1", "label": "禁用"}
              ]
            }
          ],
          "actions": [
            {
              "text": "添加用户",
              "icon": "plus",
              "action": "create_modal",
              "style": "primary"
            }
          ]
        }
      },
      {
        "component": "DataTable",
        "props": {
          "api": "/api/admin/users",
          "columns": [
            {
              "key": "user_info",
              "title": "用户信息",
              "render": "UserInfoRenderer"
            },
            {
              "key": "status",
              "title": "状态",
              "render": "StatusBadgeRenderer"
            },
            {
              "key": "created_at",
              "title": "注册时间",
              "render": "TimeRenderer"
            }
          ]
        }
      }
    ]
  }
}
```

## 🎨 主题定制

### 主题配置
```javascript
{
  "theme": {
    "token": {
      "colorPrimary": "#667eea",
      "borderRadius": 8,
      "wireframe": false
    },
    "components": {
      "Button": {
        "borderRadius": 6
      },
      "Table": {
        "borderRadius": 8,
        "headerBg": "#fafafa"
      },
      "Card": {
        "borderRadius": 12
      }
    }
  }
}
```

---

## 📝 总结

这份UI组件规范文档提供了：

### 🎯 **完整的组件体系**
- **17个核心组件** - 覆盖所有界面需求
- **标准化属性定义** - 统一的组件接口
- **灵活的配置选项** - 支持多种使用场景
- **完整的使用示例** - 快速上手指南

### 🔧 **技术特点**
- **类型安全** - 完整的TypeScript类型定义
- **高度可复用** - 组件化设计思想
- **主题定制** - 支持品牌定制
- **响应式设计** - 适配多种设备

这份规范可以直接用于前端开发团队进行组件库开发，确保界面的一致性和可维护性！
