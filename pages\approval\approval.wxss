/* pages/approval/approval.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #8a2be2;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #8a2be2;
  border-radius: 2rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.filter-item icon {
  margin-left: 10rpx;
}

/* 审批列表样式 */
.approval-list {
  height: calc(100vh - 180rpx);
  padding: 20rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading icon {
  margin-bottom: 20rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-btn {
  background: #8a2be2;
  color: #fff;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 审批项目样式 */
.approval-item {
  background: #fff;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.approval-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.approval-type {
  font-size: 26rpx;
  color: #8a2be2;
  font-weight: 500;
}

.approval-status {
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  color: #fff;
}

.approval-status.pending {
  background: #ff9500;
}

.approval-status.approved {
  background: #52c41a;
}

.approval-status.rejected {
  background: #ff4d4f;
}

.approval-status.cancelled {
  background: #d9d9d9;
  color: #666;
}

.approval-content {
  padding: 30rpx;
}

.approval-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.approval-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.applicant-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.applicant-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: #f5f5f5;
}

.applicant-detail {
  flex: 1;
}

.applicant-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.apply-time {
  font-size: 22rpx;
  color: #999;
}

.approval-amount {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
  padding: 15rpx;
  background: #fef3e7;
  border-radius: 10rpx;
}

.amount-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.amount-value {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

/* 审批操作样式 */
.approval-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 30rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 25rpx;
  font-size: 26rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.reject-btn {
  background: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.approve-btn {
  background: #52c41a;
  color: #fff;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 审批流程样式 */
.approval-flow {
  border-top: 1rpx solid #f5f5f5;
  padding: 30rpx;
}

.flow-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.flow-nodes {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.flow-node {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
}

.flow-node.approved {
  background: #f6ffed;
  border-left: 4rpx solid #52c41a;
}

.flow-node.rejected {
  background: #fff2f0;
  border-left: 4rpx solid #ff4d4f;
}

.flow-node.pending {
  background: #fff7e6;
  border-left: 4rpx solid #ff9500;
}

.node-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666;
  overflow: hidden;
}

.node-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.node-info {
  flex: 1;
}

.node-name {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.node-time {
  font-size: 20rpx;
  color: #999;
}

.node-status {
  margin-left: 15rpx;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.load-more icon {
  margin-right: 10rpx;
}

/* 浮动按钮样式 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: #8a2be2;
  border-radius: 50%;
  box-shadow: 0 8rpx 20rpx rgba(138, 43, 226, 0.3);
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.9);
}

/* 审批操作弹窗样式 */
.approval-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.approval-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #fff;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.modal-close {
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 26rpx;
  color: #333;
  background: #f9f9f9;
  box-sizing: border-box;
}

.comment-count {
  text-align: right;
  margin-top: 10rpx;
  font-size: 22rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}

.modal-btn {
  flex: 1;
  padding: 30rpx 0;
  font-size: 28rpx;
  border: none;
  background: #fff;
  color: #333;
}

.modal-btn:first-child {
  border-right: 1rpx solid #f5f5f5;
}

.confirm-btn {
  color: #8a2be2;
  font-weight: 500;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
} 