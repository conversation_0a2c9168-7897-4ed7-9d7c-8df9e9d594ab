// scripts/check-completeness.js
// 小程序项目完整性检查脚本

const fs = require('fs')
const path = require('path')

// 读取app.json获取页面列表
function getPageList() {
  try {
    const appJsonPath = path.join(__dirname, '../app.json')
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'))
    return appJson.pages || []
  } catch (error) {
    console.error('读取app.json失败:', error)
    return []
  }
}

// 检查页面文件是否完整
function checkPageFiles(pagePath) {
  const basePath = path.join(__dirname, '../')
  const extensions = ['.js', '.json', '.wxml', '.wxss']
  const missing = []
  
  extensions.forEach(ext => {
    const filePath = path.join(basePath, pagePath + ext)
    if (!fs.existsSync(filePath)) {
      missing.push(pagePath + ext)
    }
  })
  
  return missing
}

// 检查组件文件
function checkComponents() {
  const componentsDir = path.join(__dirname, '../components')
  const missing = []
  
  if (!fs.existsSync(componentsDir)) {
    return ['components目录不存在']
  }
  
  const components = fs.readdirSync(componentsDir)
  components.forEach(component => {
    const componentPath = path.join(componentsDir, component)
    if (fs.statSync(componentPath).isDirectory()) {
      const extensions = ['.js', '.json', '.wxml', '.wxss']
      extensions.forEach(ext => {
        const filePath = path.join(componentPath, component + ext)
        if (!fs.existsSync(filePath)) {
          missing.push(`components/${component}/${component}${ext}`)
        }
      })
    }
  })
  
  return missing
}

// 检查utils文件
function checkUtils() {
  const utilsDir = path.join(__dirname, '../utils')
  const requiredUtils = [
    'api.js',
    'common.js',
    'points.js',
    'update-helper.js',
    'api-service.js',
    'util.js',
    'userState.js'
  ]
  
  const missing = []
  
  requiredUtils.forEach(file => {
    const filePath = path.join(utilsDir, file)
    if (!fs.existsSync(filePath)) {
      missing.push(`utils/${file}`)
    }
  })
  
  return missing
}

// 主检查函数
function checkCompleteness() {
  console.log('🔍 开始检查小程序项目完整性...\n')
  
  const pages = getPageList()
  let totalMissing = 0
  
  // 检查页面文件
  console.log('📄 检查页面文件:')
  pages.forEach(page => {
    const missing = checkPageFiles(page)
    if (missing.length > 0) {
      console.log(`❌ ${page}: 缺失 ${missing.join(', ')}`)
      totalMissing += missing.length
    } else {
      console.log(`✅ ${page}: 完整`)
    }
  })
  
  // 检查组件文件
  console.log('\n🧩 检查组件文件:')
  const missingComponents = checkComponents()
  if (missingComponents.length > 0) {
    console.log(`❌ 缺失组件文件: ${missingComponents.join(', ')}`)
    totalMissing += missingComponents.length
  } else {
    console.log('✅ 所有组件文件完整')
  }
  
  // 检查工具文件
  console.log('\n🔧 检查工具文件:')
  const missingUtils = checkUtils()
  if (missingUtils.length > 0) {
    console.log(`❌ 缺失工具文件: ${missingUtils.join(', ')}`)
    totalMissing += missingUtils.length
  } else {
    console.log('✅ 所有工具文件完整')
  }
  
  // 检查配置文件
  console.log('\n⚙️ 检查配置文件:')
  const configFiles = [
    'app.js',
    'app.json',
    'app.wxss',
    'project.config.json',
    'sitemap.json'
  ]
  
  let missingConfigs = 0
  configFiles.forEach(file => {
    const filePath = path.join(__dirname, '../', file)
    if (!fs.existsSync(filePath)) {
      console.log(`❌ 缺失: ${file}`)
      missingConfigs++
    } else {
      console.log(`✅ ${file}: 存在`)
    }
  })
  
  totalMissing += missingConfigs
  
  // 总结
  console.log('\n📊 检查结果:')
  if (totalMissing === 0) {
    console.log('🎉 项目完整，所有必要文件都存在！')
  } else {
    console.log(`⚠️ 发现 ${totalMissing} 个缺失文件，需要补充。`)
  }
  
  return totalMissing === 0
}

// 如果直接运行此脚本
if (require.main === module) {
  checkCompleteness()
}

module.exports = {
  checkCompleteness,
  getPageList,
  checkPageFiles,
  checkComponents,
  checkUtils
} 