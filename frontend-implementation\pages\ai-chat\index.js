// AI聊天页面
import { sendAIMessage, getChatHistory, createChatSession, getQuickActions, executeQuickAction, submitMessageFeedback } from '../../api/aiChat'
import { showToast, showLoading, hideLoading, showModal } from '../../utils/wx'

Page({
  data: {
    // 聊天数据
    messages: [],
    currentSessionId: null,
    inputText: '',
    
    // UI状态
    loading: false,
    sending: false,
    showQuickActions: true,
    scrollToView: '',
    
    // 快捷操作
    quickActions: [],
    
    // 聊天配置
    maxInputLength: 500,
    
    // 键盘高度
    keyboardHeight: 0,
    
    // 消息类型
    messageTypes: {
      USER: 'user',
      AI: 'ai',
      SYSTEM: 'system'
    }
  },

  onLoad(options) {
    // 如果传入了会话ID，加载历史消息
    if (options.sessionId) {
      this.setData({ currentSessionId: options.sessionId })
      this.loadChatHistory()
    } else {
      // 新会话，显示欢迎消息
      this.showWelcomeMessage()
    }
    
    // 加载快捷操作
    this.loadQuickActions()
  },

  onShow() {
    // 滚动到底部
    this.scrollToBottom()
  },

  /**
   * 显示欢迎消息
   */
  showWelcomeMessage() {
    const welcomeMessage = {
      id: 'welcome',
      type: this.data.messageTypes.SYSTEM,
      content: '您好！我是您的专属命理顾问，可以为您提供八字分析、易经占卜、风水指导等服务。请问有什么可以帮助您的吗？',
      timestamp: new Date().toISOString(),
      suggestions: [
        '我想了解我的八字',
        '帮我占卜一下',
        '分析一下我的运势',
        '风水布局建议'
      ]
    }
    
    this.setData({
      messages: [welcomeMessage]
    })
  },

  /**
   * 加载快捷操作
   */
  async loadQuickActions() {
    try {
      const result = await getQuickActions()
      if (result.status === 'success') {
        this.setData({
          quickActions: result.data.actions || []
        })
      }
    } catch (error) {
      console.error('加载快捷操作失败:', error)
    }
  },

  /**
   * 加载聊天历史
   */
  async loadChatHistory() {
    try {
      showLoading({ title: '加载中...' })
      
      const result = await getChatHistory({
        session_id: this.data.currentSessionId,
        limit: 50
      })
      
      if (result.status === 'success') {
        this.setData({
          messages: result.data.messages || [],
          showQuickActions: false
        })
        this.scrollToBottom()
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error)
      showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  /**
   * 键盘高度变化
   */
  onKeyboardHeightChange(e) {
    this.setData({
      keyboardHeight: e.detail.height
    })
    // 键盘弹起时滚动到底部
    if (e.detail.height > 0) {
      setTimeout(() => {
        this.scrollToBottom()
      }, 100)
    }
  },

  /**
   * 发送消息
   */
  async sendMessage(content = null) {
    const message = content || this.data.inputText.trim()
    
    if (!message) {
      showToast({
        title: '请输入消息',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({ sending: true })
      
      // 添加用户消息到界面
      const userMessage = {
        id: `user_${Date.now()}`,
        type: this.data.messageTypes.USER,
        content: message,
        timestamp: new Date().toISOString()
      }
      
      this.addMessage(userMessage)
      this.setData({ inputText: '', showQuickActions: false })
      
      // 发送到服务器
      const result = await sendAIMessage({
        message,
        session_id: this.data.currentSessionId
      })
      
      if (result.status === 'success') {
        // 更新会话ID
        if (!this.data.currentSessionId) {
          this.setData({ currentSessionId: result.data.session_id })
        }
        
        // 添加AI回复
        const aiMessage = {
          id: result.data.message_id,
          type: this.data.messageTypes.AI,
          content: result.data.response.content,
          timestamp: new Date().toISOString(),
          intent: result.data.response.intent,
          confidence: result.data.response.confidence,
          suggestions: result.data.response.suggestions || [],
          actions: result.data.response.actions || [],
          analysis: result.data.analysis
        }
        
        this.addMessage(aiMessage)
      } else {
        throw new Error(result.message || '发送失败')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      showToast({
        title: error.message || '发送失败',
        icon: 'none'
      })
      
      // 添加错误消息
      const errorMessage = {
        id: `error_${Date.now()}`,
        type: this.data.messageTypes.SYSTEM,
        content: '抱歉，消息发送失败，请稍后重试。',
        timestamp: new Date().toISOString(),
        isError: true
      }
      this.addMessage(errorMessage)
    } finally {
      this.setData({ sending: false })
    }
  },

  /**
   * 添加消息到列表
   */
  addMessage(message) {
    const messages = [...this.data.messages, message]
    this.setData({
      messages,
      scrollToView: `msg_${message.id}`
    })
    
    // 延迟滚动确保DOM更新
    setTimeout(() => {
      this.scrollToBottom()
    }, 100)
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    if (this.data.messages.length > 0) {
      const lastMessage = this.data.messages[this.data.messages.length - 1]
      this.setData({
        scrollToView: `msg_${lastMessage.id}`
      })
    }
  },

  /**
   * 点击快捷操作
   */
  async onQuickActionTap(e) {
    const { action } = e.currentTarget.dataset
    
    try {
      if (action.type === 'message') {
        // 直接发送消息
        this.sendMessage(action.content)
      } else if (action.type === 'action') {
        // 执行特定操作
        const result = await executeQuickAction(action.id)
        if (result.status === 'success' && result.data.message) {
          this.sendMessage(result.data.message)
        }
      } else if (action.type === 'navigate') {
        // 页面跳转
        wx.navigateTo({
          url: action.url
        })
      }
    } catch (error) {
      console.error('执行快捷操作失败:', error)
      showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  /**
   * 点击建议问题
   */
  onSuggestionTap(e) {
    const { suggestion } = e.currentTarget.dataset
    this.sendMessage(suggestion)
  },

  /**
   * 点击操作按钮
   */
  onActionTap(e) {
    const { action } = e.currentTarget.dataset
    
    if (action.type === 'navigate') {
      wx.navigateTo({
        url: action.url
      })
    } else if (action.type === 'analysis') {
      // 跳转到分析页面
      wx.navigateTo({
        url: `/pages/analysis/index?type=${action.analysis_type}`
      })
    }
  },

  /**
   * 消息反馈
   */
  async onMessageFeedback(e) {
    const { messageId, type } = e.currentTarget.dataset
    
    try {
      await submitMessageFeedback(messageId, {
        feedback_type: type,
        rating: type === 'like' ? 5 : 1
      })
      
      showToast({
        title: '反馈成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('提交反馈失败:', error)
    }
  },

  /**
   * 长按消息
   */
  onMessageLongPress(e) {
    const { message } = e.currentTarget.dataset
    
    wx.showActionSheet({
      itemList: ['复制', '删除'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 复制消息
          wx.setClipboardData({
            data: message.content,
            success: () => {
              showToast({
                title: '已复制',
                icon: 'success'
              })
            }
          })
        } else if (res.tapIndex === 1) {
          // 删除消息
          this.deleteMessage(message.id)
        }
      }
    })
  },

  /**
   * 删除消息
   */
  deleteMessage(messageId) {
    showModal({
      title: '确认删除',
      content: '确定要删除这条消息吗？',
      success: (res) => {
        if (res.confirm) {
          const messages = this.data.messages.filter(msg => msg.id !== messageId)
          this.setData({ messages })
        }
      }
    })
  },

  /**
   * 清空聊天记录
   */
  clearChat() {
    showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            messages: [],
            currentSessionId: null,
            showQuickActions: true
          })
          this.showWelcomeMessage()
        }
      }
    })
  }
})
