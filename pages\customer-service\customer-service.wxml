<!--客服页面-->
<view class="page-container">
  <view class="header">
    <image class="header-bg" src="/assets/images/customer-service-bg.png" mode="aspectFill" />
    <view class="header-content">
      <view class="title">客服中心</view>
      <view class="subtitle">7×24小时为您服务</view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-title">联系我们</view>
    <view class="contact-grid">
      <view class="contact-item" bindtap="contactWechat">
        <view class="contact-icon wechat">💬</view>
        <view class="contact-info">
          <text class="contact-title">微信客服</text>
          <text class="contact-desc">在线咨询</text>
        </view>
        <view class="contact-status online">在线</view>
      </view>
      
      <view class="contact-item" bindtap="contactPhone">
        <view class="contact-icon phone">📞</view>
        <view class="contact-info">
          <text class="contact-title">电话客服</text>
          <text class="contact-desc">************</text>
        </view>
        <view class="contact-arrow">></view>
      </view>
      
      <view class="contact-item" bindtap="contactEmail">
        <view class="contact-icon email">📧</view>
        <view class="contact-info">
          <text class="contact-title">邮件客服</text>
          <text class="contact-desc"><EMAIL></text>
        </view>
        <view class="contact-arrow">></view>
      </view>
      
      <view class="contact-item" bindtap="goToFeedback">
        <view class="contact-icon feedback">📝</view>
        <view class="contact-info">
          <text class="contact-title">意见反馈</text>
          <text class="contact-desc">问题建议</text>
        </view>
        <view class="contact-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 服务时间 -->
  <view class="service-time">
    <view class="time-card">
      <view class="time-title">客服时间</view>
      <view class="time-list">
        <view class="time-item">
          <text class="time-label">在线客服</text>
          <text class="time-value">24小时</text>
        </view>
        <view class="time-item">
          <text class="time-label">电话客服</text>
          <text class="time-value">9:00-18:00</text>
        </view>
        <view class="time-item">
          <text class="time-label">邮件回复</text>
          <text class="time-value">24小时内</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="faq-section">
    <view class="section-title">
      <text>常见问题</text>
      <text class="section-more" bindtap="goToFAQ">查看更多</text>
    </view>
    <view class="faq-list">
      <view 
        class="faq-item" 
        wx:for="{{faqList}}" 
        wx:key="id"
        bindtap="toggleFAQ"
        data-index="{{index}}"
      >
        <view class="faq-question">
          <text class="question-text">{{item.question}}</text>
          <view class="expand-icon {{item.expanded ? 'expanded' : ''}}">▼</view>
        </view>
        <view class="faq-answer {{item.expanded ? 'show' : ''}}">
          <text>{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">快速操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="goToUserGuide">
        <view class="action-icon">📖</view>
        <text>使用指南</text>
      </view>
      <view class="action-item" bindtap="goToAccountSecurity">
        <view class="action-icon">🔒</view>
        <text>账号安全</text>
      </view>
      <view class="action-item" bindtap="goToPrivacyPolicy">
        <view class="action-icon">📄</view>
        <text>隐私政策</text>
      </view>
      <view class="action-item" bindtap="goToTermsOfService">
        <view class="action-icon">📋</view>
        <text>服务条款</text>
      </view>
    </view>
  </view>

  <!-- 联系客服浮动按钮 -->
  <view class="floating-contact" bindtap="openQuickContact">
    <view class="floating-icon">💬</view>
    <text class="floating-text">联系客服</text>
  </view>

  <!-- 快速联系弹窗 -->
  <view class="modal" wx:if="{{showContactModal}}">
    <view class="modal-mask" bindtap="closeContactModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择联系方式</text>
        <view class="close-btn" bindtap="closeContactModal">×</view>
      </view>
      <view class="modal-body">
        <view class="contact-option" bindtap="startWechatChat">
          <view class="option-icon">💬</view>
          <view class="option-info">
            <text class="option-title">微信在线客服</text>
            <text class="option-desc">即时回复，快速解决问题</text>
          </view>
        </view>
        <view class="contact-option" bindtap="callCustomerService">
          <view class="option-icon">📞</view>
          <view class="option-info">
            <text class="option-title">电话客服</text>
            <text class="option-desc">************</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 