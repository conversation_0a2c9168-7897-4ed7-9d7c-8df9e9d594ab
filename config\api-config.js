/**
 * API配置文件
 * 统一管理小程序与后端API的连接配置
 */

// 环境配置
const ENV_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:8000',
    timeout: 10000,
    debug: true
  },
  
  // 测试环境
  testing: {
    baseURL: 'http://test-api.yourcompany.com',
    timeout: 15000,
    debug: true
  },
  
  // 阿里云ECS生产环境
  aliyun_ecs: {
    baseURL: 'https://api.yourcompany.com',  // 替换为您的阿里云ECS域名
    timeout: 20000,
    debug: false
  },
  
  // 阿里云函数计算环境
  aliyun_fc: {
    baseURL: 'https://your-fc-domain.cn-hangzhou.fc.aliyuncs.com',  // 替换为函数计算触发器地址
    timeout: 30000,  // 函数计算冷启动需要更长时间
    debug: false
  },
  
  // 生产环境（默认使用阿里云ECS）
  production: {
    baseURL: 'https://api.yourcompany.com',
    timeout: 20000,
    debug: false
  }
}

// 当前环境（可以通过构建工具或配置动态设置）
const CURRENT_ENV = 'development' // development | testing | aliyun_ecs | aliyun_fc | production

// 当前环境配置
const API_CONFIG = ENV_CONFIG[CURRENT_ENV]

// API端点配置
const API_ENDPOINTS = {
  // 企业微信相关
  WXWORK: {
    CHAT: '/api/wxwork/chat',
    CONTACTS: '/api/wxwork/contacts',
    DEPARTMENTS: '/api/wxwork/departments',
    USER_DETAIL: '/api/wxwork/user',
    USER_PROFILE: '/api/wxwork/user-profile',
    SEND_MESSAGE: '/api/wxwork/send-message'
  },
  
  // 命理分析相关
  FORTUNE: {
    CHAT: '/api/chat',
    FORTUNE_TELLING: '/api/fortune',
    YIJING: '/api/yijing/divine',
    FENGSHUI: '/api/fengshui/analyze',
    WUXING: '/api/wuxing/analyze'
  },
  
  // 用户相关
  USER: {
    LOGIN: '/api/users/login',
    REGISTER: '/api/users/register',
    PROFILE: '/api/users/me',
    WX_LOGIN: '/api/users/wx-login'
  },
  
  // 社区相关
  COMMUNITY: {
    POSTS: '/api/posts',
    COMMENTS: '/api/posts/{id}/comments'
  },
  
  // 其他
  HEALTH: '/health',
  FEEDBACK: '/api/feedback'
}

// 请求配置
const REQUEST_CONFIG = {
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    duration: 5 * 60 * 1000 // 5分钟
  }
}

// 错误码映射
const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  SERVER_ERROR: 'SERVER_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  FC_COLD_START: 'FC_COLD_START'  // 函数计算冷启动
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT]: '请求超时，请稍后重试',
  [ERROR_CODES.SERVER_ERROR]: '服务器错误，请稍后重试',
  [ERROR_CODES.AUTH_ERROR]: '身份验证失败，请重新登录',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足，无法执行此操作',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.VALIDATION_ERROR]: '请求参数错误',
  [ERROR_CODES.FC_COLD_START]: '服务启动中，请稍后重试'
}

// 阿里云相关配置
const ALIYUN_CONFIG = {
  // 阿里云地域
  region: 'cn-hangzhou',
  
  // 函数计算配置
  fc: {
    serviceName: 'chatbot-api-service',
    functionName: 'wxwork-api'
  },
  
  // ECS配置
  ecs: {
    instanceId: 'your-ecs-instance-id',
    publicIP: 'your-ecs-public-ip'
  }
}

// 环境切换辅助函数
const ENV_UTILS = {
  /**
   * 切换到阿里云ECS环境
   */
  switchToAliyunECS() {
    Object.assign(API_CONFIG, ENV_CONFIG.aliyun_ecs)
    console.log('已切换到阿里云ECS环境:', API_CONFIG.baseURL)
  },
  
  /**
   * 切换到阿里云函数计算环境
   */
  switchToAliyunFC() {
    Object.assign(API_CONFIG, ENV_CONFIG.aliyun_fc)
    console.log('已切换到阿里云函数计算环境:', API_CONFIG.baseURL)
  },
  
  /**
   * 获取当前环境信息
   */
  getCurrentEnv() {
    return {
      env: CURRENT_ENV,
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      debug: API_CONFIG.debug
    }
  },
  
  /**
   * 检查是否为阿里云环境
   */
  isAliyunEnv() {
    return CURRENT_ENV === 'aliyun_ecs' || CURRENT_ENV === 'aliyun_fc'
  },
  
  /**
   * 检查是否为函数计算环境
   */
  isFunctionCompute() {
    return CURRENT_ENV === 'aliyun_fc'
  }
}

// 导出配置
module.exports = {
  API_CONFIG,
  API_ENDPOINTS,
  REQUEST_CONFIG,
  ERROR_CODES,
  ERROR_MESSAGES,
  ALIYUN_CONFIG,
  ENV_UTILS,
  CURRENT_ENV
} 