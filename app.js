// app.js
const util = require('./utils/util')
const wxworkUtil = require('./utils/wxwork-util')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检测是否为企业微信环境
    this.checkWxWorkEnvironment()
    
    // 企业微信登录
    this.wxworkLogin()
  },

  // 检测企业微信环境
  checkWxWorkEnvironment() {
    wx.getSystemInfo({
      success: (res) => {
        if (res.environment === 'wxwork') {
          this.globalData.isWxWork = true
          console.log('运行在企业微信环境')
        } else {
          this.globalData.isWxWork = false
          console.log('运行在普通微信环境')
        }
      }
    })
  },

  // 企业微信登录
  wxworkLogin() {
    if (this.globalData.isWxWork) {
      // 企业微信登录
      wx.qy.login({
        success: (res) => {
          console.log('企业微信登录成功', res)
          // 获取用户信息
          this.getWxWorkUserInfo(res.code)
        },
        fail: (err) => {
          console.error('企业微信登录失败', err)
          // 降级到普通登录
          this.normalLogin()
        }
      })
    } else {
      // 普通微信登录
      this.normalLogin()
    }
  },

  // 普通微信登录
  normalLogin() {
    wx.login({
      success: res => {
        console.log('普通微信登录成功', res)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },

  // 获取企业微信用户信息
  getWxWorkUserInfo(code) {
    // 调用云函数获取用户信息
    wx.cloud.callFunction({
      name: 'getWxWorkUserInfo',
      data: { code },
      success: (res) => {
        console.log('获取企业微信用户信息成功', res)
        this.globalData.userInfo = res.result.userInfo
        this.globalData.wxworkUserInfo = res.result.wxworkUserInfo
      },
      fail: (err) => {
        console.error('获取企业微信用户信息失败', err)
      }
    })
  },

  // 添加图片加载失败处理
  onError(err) {
    console.error('小程序错误：', err);
    
    // 图片加载失败时使用默认图片
    if (err.includes('Failed to load local image resource')) {
      const defaultImages = {
        '/assets/icons/home/<USER>': '/assets/icons/home/<USER>',
        '/assets/icons/home/<USER>': '/assets/icons/home/<USER>',
        '/assets/images/article1.jpg': '/assets/images/banner1.jpg',
        '/assets/images/article2.jpg': '/assets/images/banner2.jpg',
        '/assets/images/article3.jpg': '/assets/images/banner3.jpg',
        '/assets/images/empty-contacts.png': '/assets/images/default-avatar.png'
      };
      
      // 处理profile目录下的图片
      if (err.includes('/assets/icons/profile/')) {
        return '/assets/images/default-avatar.png';
      }
      
      // 返回对应的默认图片
      for (const [errorPath, defaultPath] of Object.entries(defaultImages)) {
        if (err.includes(errorPath)) {
          return defaultPath;
        }
      }
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    wxworkUserInfo: null,
    isWxWork: false,
    util: util,
    wxworkUtil: wxworkUtil,
    corpId: '', // 企业ID
    agentId: '', // 应用ID
    permissions: {
      readContacts: false,
      readDepartments: false,
      sendMessages: false,
      createMeeting: false,
      admin: false
    }
  }
})
