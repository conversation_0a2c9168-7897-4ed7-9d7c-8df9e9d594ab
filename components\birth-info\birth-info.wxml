<!--components/birth-info/birth-info.wxml-->
<view class="birth-info-container">
  <view class="form-section">
    <view class="form-item">
      <text class="form-label">出生日期</text>
      <picker mode="date" value="{{birthDate}}" bindchange="onDateChange">
        <view class="picker-value">{{birthDate || '请选择出生日期'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">出生时间</text>
      <picker mode="time" value="{{birthTime}}" bindchange="onTimeChange">
        <view class="picker-value">{{birthTime || '请选择出生时间'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">性别</text>
      <radio-group bindchange="onGenderChange">
        <label class="radio-item">
          <radio value="male" checked="{{gender === 'male'}}"/>
          <text>男</text>
        </label>
        <label class="radio-item">
          <radio value="female" checked="{{gender === 'female'}}"/>
          <text>女</text>
        </label>
      </radio-group>
    </view>
  </view>
  
  <view class="button-section">
    <button class="confirm-btn" bindtap="onConfirm" disabled="{{!isValid}}">
      确认保存
    </button>
  </view>
</view> 