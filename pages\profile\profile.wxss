/* pages/profile/profile.wxss */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}

/* 顶部背景 */
.profile-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
}

/* 用户信息卡片 */
.user-card {
  position: absolute;
  bottom: -80rpx;
  left: 30rpx;
  right: 30rpx;
  background: white;
  border-radius: 30rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 40rpx rgba(149, 117, 205, 0.15);
}

.user-main {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 6rpx solid #f5f0ff;
  box-shadow: 0 8rpx 24rpx rgba(149, 117, 205, 0.2);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.edit-icon {
  margin-left: 10rpx;
  font-size: 24rpx;
}

.user-desc {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-id {
  font-size: 26rpx;
  color: #999;
  padding: 4rpx 16rpx;
  background: #f5f0ff;
  border-radius: 20rpx;
}

.user-level {
  font-size: 26rpx;
  color: #9575cd;
  padding: 4rpx 16rpx;
  background: linear-gradient(135deg, #f5f0ff 0%, #ece7f5 100%);
  border-radius: 20rpx;
}

.login-btn {
  margin-top: 20rpx;
  padding: 16rpx 40rpx;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.3);
}

/* 用户统计 */
.user-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #9575cd;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: #eee;
}

/* 签到卡片 */
.sign-card {
  margin: 120rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #fff 0%, #f8f5ff 100%);
  border-radius: 25rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  transition: all 0.3s ease;
}

.sign-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.15);
}

.sign-icon {
  font-size: 60rpx;
  margin-right: 25rpx;
}

.sign-content {
  flex: 1;
}

.sign-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.sign-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.sign-action {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  color: white;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 常用功能 */
.quick-section {
  margin: 30rpx;
}

.section-header {
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 15rpx;
  background: white;
  border-radius: 25rpx;
  position: relative;
  transition: all 0.3s ease;
}

.quick-item:active {
  transform: scale(0.95);
}

.quick-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 15rpx;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.quick-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

.quick-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  min-width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  padding: 0 8rpx;
  background: #ff6b6b;
  color: white;
  font-size: 22rpx;
  border-radius: 18rpx;
  text-align: center;
}

/* 功能列表 */
.menu-list {
  margin: 30rpx;
}

.menu-group {
  background: white;
  border-radius: 25rpx;
  margin-bottom: 25rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.group-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #999;
  padding: 25rpx 30rpx 15rpx;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
  transition: all 0.3s ease;
}

.menu-item:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:active {
  background: #f8f5ff;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
  width: 60rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-value {
  font-size: 28rpx;
  color: #999;
  margin-right: 15rpx;
}

.menu-badge {
  min-width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  padding: 0 10rpx;
  background: #ff6b6b;
  color: white;
  font-size: 24rpx;
  border-radius: 18rpx;
  text-align: center;
  margin-right: 15rpx;
}

.menu-arrow {
  font-size: 40rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin: 50rpx 30rpx;
}

.logout-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: white;
  color: #ff6b6b;
  border: 2rpx solid #ff6b6b;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background: #ff6b6b;
  color: white;
}

/* 底部空白 */
.bottom-space {
  height: 40rpx;
}