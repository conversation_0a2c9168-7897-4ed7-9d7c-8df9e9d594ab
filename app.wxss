/**app.wxss**/
/* 全局样式变量 */
:root {
  --gradient-purple: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);
  --gradient-purple-light: linear-gradient(135deg, #faf7ff 0%, #f0ebf8 100%);
  --gradient-purple-dark: linear-gradient(135deg, #e8dff5 0%, #d4c5e8 100%);
  --primary-color: #9575cd;
  --primary-light: #b39ddb;
  --primary-dark: #7e57c2;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-radius: 20rpx;
  --shadow-light: 0 4rpx 12rpx rgba(149, 117, 205, 0.1);
  --shadow-medium: 0 6rpx 20rpx rgba(149, 117, 205, 0.15);
  --shadow-heavy: 0 8rpx 24rpx rgba(149, 117, 205, 0.2);
  
  /* 主色调 - 优雅渐变紫色系 */
  --primary-color: #7928ca;
  --primary-light: #9b4dca;
  --primary-dark: #5a21b6;
  --primary-lighter: #e0c3fc;
  --primary-lightest: #f3e8ff;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #7928ca 0%, #9b4dca 100%);
  --gradient-secondary: linear-gradient(135deg, #f3e8ff 0%, #e0c3fc 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
  
  /* 辅助色 */
  --secondary-color: #e0c3fc;
  --accent-color: #a855f7;
  
  /* 文字颜色 */
  --text-primary: #2d1b69;
  --text-secondary: #6b5b95;
  --text-light: #9d92b2;
  --text-muted: #b8b0cc;
  
  /* 背景和边框 */
  --background-color: #fafbff;
  --card-background: #ffffff;
  --border-color: #e8e5ff;
  --shadow-color: rgba(121, 40, 202, 0.08);
  --shadow-hover: rgba(121, 40, 202, 0.15);
  
  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
}

/* 页面通用样式 */
page {
  background: var(--gradient-purple);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  /* 基础样式 */
  background-color: var(--background-color);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 容器通用样式 */
.container {
  min-height: 100vh;
  background: transparent;
  padding: 32rpx 24rpx;
  box-sizing: border-box;
  background-color: var(--background-color);
}

/* 卡片样式 - 增强版 */
.card {
  background: var(--gradient-card);
  border-radius: 24rpx;
  padding: 36rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 10rpx 30rpx var(--shadow-color);
  border: 1rpx solid rgba(232, 229, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, var(--primary-lightest) 50%, transparent 100%);
  opacity: 0.5;
}

.card:active {
  transform: translateY(2rpx) scale(0.99);
  box-shadow: 0 5rpx 15rpx var(--shadow-color);
}

/* 标题样式 - 渐变装饰 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin: 40rpx 0 28rpx;
  padding-left: 24rpx;
  position: relative;
  letter-spacing: 0.5rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 80%;
  background: var(--gradient-primary);
  border-radius: 8rpx;
}

/* 按钮样式 - 渐变增强 */
.btn-primary {
  background: var(--gradient-primary);
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  padding: 26rpx 52rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 10rpx 25rpx var(--shadow-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-primary:active::after {
  width: 300rpx;
  height: 300rpx;
}

.btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 5rpx 12rpx var(--shadow-color);
}

/* 输入框样式 - 现代化 */
.input-group {
  margin-bottom: 36rpx;
}

.input-label {
  font-size: 30rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.input-field {
  background: rgba(243, 232, 255, 0.3);
  border-radius: 16rpx;
  padding: 26rpx 28rpx;
  color: var(--text-primary);
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 30rpx;
}

.input-field:focus {
  background: #FFFFFF;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(121, 40, 202, 0.1), 0 4rpx 12rpx var(--shadow-color);
}

/* 分割线 - 渐变样式 */
.divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
  margin: 40rpx 0;
}

/* 新增玻璃态效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(232, 229, 255, 0.3);
  border-radius: 24rpx;
  padding: 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(121, 40, 202, 0.08);
}

/* 新增磨砂效果 */
.frosted-glass {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 优化动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-up {
  animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 新增软阴影类 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx var(--shadow-color);
}

.shadow-md {
  box-shadow: 0 8rpx 24rpx var(--shadow-color);
}

.shadow-lg {
  box-shadow: 0 16rpx 48rpx var(--shadow-color);
}

.shadow-xl {
  box-shadow: 0 24rpx 64rpx var(--shadow-hover);
}

/* 圆角优化 */
.rounded-sm {
  border-radius: 8rpx;
}

.rounded-md {
  border-radius: 12rpx;
}

.rounded-lg {
  border-radius: 20rpx;
}

.rounded-xl {
  border-radius: 28rpx;
}

.rounded-2xl {
  border-radius: 36rpx;
}

.rounded-full {
  border-radius: 9999rpx;
}

/* 添加加载骨架屏样式 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 布局类 */
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.space-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

/* 文字颜色 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-light {
  color: var(--text-light);
}

/* 边距类 */
.mt-16 {
  margin-top: 16rpx;
}

.mb-16 {
  margin-bottom: 16rpx;
}

.ml-16 {
  margin-left: 16rpx;
}

.mr-16 {
  margin-right: 16rpx;
}

/* 新增卡片悬浮效果 */
.hover-card {
  transition: all 0.3s ease;
}

.hover-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
}

/* 新增渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-lightest) 0%, var(--secondary-color) 100%);
}

/* 添加图片加载错误的默认样式 */
.img-error {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.img-error::before {
  content: "图片加载失败";
  color: #999;
  font-size: 24rpx;
} 
