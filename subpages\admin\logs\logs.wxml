<!--subpages/admin/logs/logs.wxml-->
<view class="logs-container">
  <view class="logs-header">
    <text class="logs-title">系统日志</text>
    <view class="logs-filters">
      <button 
        wx:for="{{filters}}" 
        wx:key="key"
        class="filter-btn {{activeFilter === item.key ? 'active' : ''}}"
        data-filter="{{item.key}}"
        bindtap="filterLogs"
      >
        {{item.name}}
      </button>
    </view>
  </view>
  
  <view class="logs-content">
    <view class="logs-list">
      <view wx:for="{{filteredLogs}}" wx:key="id" class="log-item {{item.level}}">
        <view class="log-info">
          <text class="log-time">{{item.time}}</text>
          <text class="log-level">{{item.level === 'error' ? 'ERROR' : item.level === 'warning' ? 'WARN' : 'INFO'}}</text>
        </view>
        <text class="log-message">{{item.message}}</text>
        <text class="log-source">{{item.source}}</text>
      </view>
    </view>
    
    <view wx:if="{{filteredLogs.length === 0}}" class="empty-logs">
      <text class="empty-text">暂无日志记录</text>
    </view>
  </view>
  
  <view class="logs-actions">
    <button class="action-btn secondary" bindtap="clearLogs">清理日志</button>
    <button class="action-btn primary" bindtap="exportLogs">导出日志</button>
  </view>
</view> 