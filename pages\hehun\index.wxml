<view class="container">
  <view class="header">
    <text class="title">合婚测算</text>
    <text class="subtitle">缘分天注定，姻缘有迹可循</text>
  </view>

  <view class="form-section">
    <view class="person-info male">
      <text class="section-title">男方信息</text>
      <view class="form-item">
        <text class="label">出生日期</text>
        <picker mode="date" value="{{maleBirthDate}}" bindchange="onMaleBirthDateChange">
          <view class="picker">{{maleBirthDate || '请选择出生日期'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">出生时间</text>
        <picker mode="time" value="{{maleBirthTime}}" bindchange="onMaleBirthTimeChange">
          <view class="picker">{{maleBirthTime || '请选择出生时间'}}</view>
        </picker>
      </view>
    </view>

    <view class="person-info female">
      <text class="section-title">女方信息</text>
      <view class="form-item">
        <text class="label">出生日期</text>
        <picker mode="date" value="{{femaleBirthDate}}" bindchange="onFemaleBirthDateChange">
          <view class="picker">{{femaleBirthDate || '请选择出生日期'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">出生时间</text>
        <picker mode="time" value="{{femaleBirthTime}}" bindchange="onFemaleBirthTimeChange">
          <view class="picker">{{femaleBirthTime || '请选择出生时间'}}</view>
        </picker>
      </view>
    </view>
  </view>

  <button class="analyze-btn" bindtap="analyzeMarriage" loading="{{loading}}">
    开始测算
  </button>

  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="card-title">八字合婚</view>
      <view class="bazi-comparison">
        <view class="person-bazi">
          <text class="person-title">男方八字</text>
          <view class="bazi-grid">
            <view class="grid-item" wx:for="{{maleResult.pillars}}" wx:key="index">
              <text class="pillar-name">{{item.name}}</text>
              <view class="pillar-content">
                <text>{{item.heavenlyStem}}{{item.earthlyBranch}}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="person-bazi">
          <text class="person-title">女方八字</text>
          <view class="bazi-grid">
            <view class="grid-item" wx:for="{{femaleResult.pillars}}" wx:key="index">
              <text class="pillar-name">{{item.name}}</text>
              <view class="pillar-content">
                <text>{{item.heavenlyStem}}{{item.earthlyBranch}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">五行相合</view>
      <view class="wuxing-analysis">
        <view class="wuxing-item" wx:for="{{marriageResult.wuxing}}" wx:key="element">
          <text class="element-name">{{item.element}}</text>
          <progress percent="{{item.percentage}}" stroke-width="12" color="{{item.color}}"/>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">合婚指数</view>
      <view class="marriage-score">
        <text class="score">{{marriageResult.score}}</text>
        <text class="max-score">/100</text>
      </view>
      <view class="score-details">
        <view class="score-item" wx:for="{{marriageResult.aspects}}" wx:key="name">
          <text class="aspect-name">{{item.name}}</text>
          <view class="stars">
            <text class="star" wx:for="{{item.stars}}" wx:key="index">★</text>
          </view>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">合婚建议</view>
      <view class="advice">
        <text>{{marriageResult.advice}}</text>
      </view>
    </view>
  </view>
</view> 