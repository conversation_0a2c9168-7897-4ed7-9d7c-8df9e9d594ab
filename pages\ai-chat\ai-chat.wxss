/* AI聊天界面 - 现代化设计 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  overflow: hidden;
}

/* 页面容器 */
.page-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 顶部导航栏 */
.header-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex: 1;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse-dot 2s infinite;
}

.status-dot.typing {
  background: #f59e0b;
  animation: pulse-typing 1s infinite;
}

.status-text {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.action-icon {
  font-size: 32rpx;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx 32rpx 0 0;
  margin-top: 20rpx;
  overflow: hidden;
  width: 97%;
  margin-left: auto;
  margin-right: auto;
}

/* 消息滚动区域 */
.messages-scroll {
  flex: 1;
  padding: 6rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  box-sizing: border-box;
}

/* 欢迎区域 */
.welcome-section {
  /* padding: 40rpx 0; */
  text-align: center;
}

.welcome-header {
  margin-bottom: 48rpx;
}

.ai-avatar-large {
  position: relative;
  display: inline-block;
  margin-bottom: 24rpx;
}

.avatar-glow {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 3s ease-in-out infinite;
}

.avatar-icon {
  font-size: 120rpx;
  display: block;
  position: relative;
  z-index: 1;
}

.welcome-avatar-icon {
  width: 120rpx;
  height: 120rpx;
  display: block;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 快捷功能卡片 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin: 32rpx 0;
}

.quick-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  display: block;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.card-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 使用提示 */
.usage-tips {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 32rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 消息列表 */
.message-list {
  padding-bottom: 20rpx;
}

.message-wrapper {
  margin-bottom: 22rpx;
}

/* 时间分隔线 */
.time-divider {
  text-align: center;
  margin: 24rpx 0;
}

.time-text {
  font-size: 24rpx;
  color: #9ca3af;
  background: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
}

/* 消息气泡 */
.message-bubble {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
  animation: message-fade-in 0.4s ease-out;
}

.user-message {
  flex-direction: row-reverse;
}

.ai-message {
  flex-direction: row;
}

/* 头像样式 */
.message-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
  position: relative;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-avatar {
  background: linear-gradient(145deg, #7928CA, #9b4dca);
  color: white;
  box-shadow:
    0 8rpx 20rpx rgba(123, 40, 202, 0.25),
    0 4rpx 8rpx rgba(0, 0, 0, 0.06);
}

.avatar-glow-small {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: radial-gradient(circle, rgba(121, 40, 202, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 2s ease-in-out infinite;
}

.avatar-text {
  position: relative;
  z-index: 1;
}

.avatar-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
  z-index: 1;
}

/* 消息内容 */
.message-content {
  max-width: 70%;
  position: relative;
}

.user-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20rpx 24rpx;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.ai-message .message-content {
  background: white;
  color: #1f2937;
  padding: 20rpx 24rpx;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.message-text {
  font-size: 30rpx;
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 消息操作按钮 */
.message-actions {
  margin-top: 16rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.action-button {
  background: rgba(102, 126, 234, 0.1);
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 16rpx;
  padding: 12rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.action-text {
  font-size: 26rpx;
  color: #667eea;
}

.action-icon {
  font-size: 24rpx;
  color: #667eea;
}

/* 消息反馈 */
.message-feedback {
  margin-top: 12rpx;
  display: flex;
  gap: 8rpx;
}

.feedback-btn {
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.feedback-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.feedback-icon {
  font-size: 24rpx;
}

.feedback-icon-img {
  width: 24rpx;
  height: 24rpx;
}

.feedback-text {
  font-size: 22rpx;
  color: #6b7280;
}

/* 悬浮输入区域 - 屏幕中间位置 */
.floating-input-container {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-input-container.collapsed {
  top: 50%;
  bottom: auto;
  left: auto;
  right: 40rpx;
  transform: translateY(-50%);
}

/* 键盘弹起时的适配 */
.floating-input-container.expanded {
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 展开状态的输入面板 */
.expanded-input-panel {
  width: 90vw;
  max-width: 600rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: panel-expand 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 输入框区域 */
.input-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.input-wrapper {
  position: relative;
}

.message-input {
  width: 95%;
  min-height: 120rpx;
  max-height: 300rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 32rpx;
  line-height: 1.5;
  color: #1f2937;
  resize: none;
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-placeholder {
  color: #9ca3af;
}

.input-counter {
  position: absolute;
  bottom: 12rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

/* 按钮区域 */
.button-section {
  padding: 24rpx 32rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 30rpx;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

.send-btn {
  flex: 2;
  height: 88rpx;
  border: none;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.send-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.send-btn.disabled {
  background: #e5e7eb;
  color: #9ca3af;
}

.send-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 30rpx;
}

/* 悬浮球 */
.floating-ball {
  width: 120rpx;
  height: 120rpx;
  position: relative;
  cursor: pointer;
}

.ball-glow {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background: radial-gradient(circle, rgba(121, 40, 202, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 3s ease-in-out infinite;
}

.ball-content {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(145deg, #7928CA, #9b4dca);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8rpx 20rpx rgba(123, 40, 202, 0.25),
    0 4rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.ball-content:active {
  transform: scale(0.9);
  box-shadow:
    0 4rpx 12rpx rgba(123, 40, 202, 0.28),
    0 2rpx 6rpx rgba(0, 0, 0, 0.06);
}

.ball-icon {
  font-size: 56rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.ball-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 4rpx solid rgba(121, 40, 202, 0.6);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  z-index: 999;
  transition: all 0.3s ease;
}

.overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.overlay.hide {
  opacity: 0;
  pointer-events: none;
}

/* 底部占位 */
.bottom-spacer {
  height: 120rpx;
}

/* 底部安全区域 */
.safe-bottom {
  height: calc(env(safe-area-inset-bottom) + 20rpx);
}

/* 动画效果 */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes pulse-typing {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes panel-expand {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes message-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* AI思考状态 */
.typing-indicator {
  margin-top: 32rpx;
}

.typing-indicator .ai-message {
  opacity: 0.8;
}

.typing-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
}

.typing-animation {
  display: flex;
  gap: 8rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #667eea;
  animation: typing-bounce 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.typing-text {
  font-size: 26rpx;
  color: #6b7280;
  font-style: italic;
}

.typing-glow {
  animation: glow-pulse 1s ease-in-out infinite;
}

@keyframes typing-bounce {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-12rpx);
    opacity: 1;
  }
}

.fab-ball:active {
  transform: scale(0.95);
  animation: none;
}

.fab-icon {
  font-size: 50rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 展开后的输入区域 */
.expanded-input-area {
  background: white;
  border-radius: 25rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(149, 117, 205, 0.2);
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 50rpx;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-card {
  background: var(--gradient-card);
  border-radius: 28rpx;
  padding: 48rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx var(--shadow-color);
  margin-bottom: 36rpx;
  border: 1rpx solid rgba(232, 229, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.welcome-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(121, 40, 202, 0.05) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

.welcome-avatar {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: inline-block;
  animation: gentle-float 3s ease-in-out infinite;
}

.welcome-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin: 24rpx 0;
}

.quick-item {
  background: var(--card-background);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  transition: all 0.3s ease;
}

.quick-item:active {
  transform: scale(0.98);
  background: var(--primary-lightest);
}

.quick-icon {
  font-size: 36rpx;
}

.quick-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 240rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 消息项样式 */
.message-item {
  margin-bottom: 36rpx;
  animation: message-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  box-sizing: border-box;
  padding: 0 12rpx;
}

/* 消息时间包装器 */
.message-time-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 12rpx;
  width: 100%;
}

/* 消息时间样式 */
.message-time {
  font-size: 24rpx;
  color: var(--text-muted);
  background-color: rgba(0, 0, 0, 0.04);
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
  line-height: 1.4;
}

/* 用户消息 */
.message-user {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 100%;
}

.message-user .message-content {
  background: var(--gradient-primary);
  color: white;
  padding: 24rpx 28rpx;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
  max-width: 70%;
  margin-right: 0;
  box-shadow: 0 8rpx 24rpx rgba(121, 40, 202, 0.25);
  position: relative;
  overflow: hidden;
  word-break: break-all;
}

.message-user .message-avatar {
  width: 80rpx;
  height: 80rpx;
  background: var(--gradient-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  flex-shrink: 0;
}



/* 消息文本 */
.message-text {
  font-size: 30rpx;
  line-height: 1.7;
  color: var(--text-primary);
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-user .message-text {
  color: white;
}

/* 打字机效果的文本样式 */
.message-text.typing-effect {
  position: relative;
}

.message-text.typing-effect::after {
  content: '.';
  position: absolute;
  color: #7928CA;
  animation: dots-typing 1.5s infinite;
  font-weight: bold;
}

@keyframes dots-typing {
  0% {
    content: '.';
  }
  33% {
    content: '..';
  }
  66% {
    content: '...';
  }
  100% {
    content: '.';
  }
}

.message-actions {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid var(--border-color);
}

.action-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 18rpx;
  padding: 18rpx 36rpx;
  font-size: 26rpx;
  line-height: 1;
  box-shadow: 0 6rpx 16rpx rgba(121, 40, 202, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(121, 40, 202, 0.25);
}

/* 输入中状态 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  animation: typing-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.typing-avatar {
  width: 72rpx;
  height: 72rpx;
  background: var(--gradient-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(121, 40, 202, 0.3);
}

.typing-content {
  background: var(--card-background);
  padding: 24rpx 32rpx;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  border: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
}

.typing-dots {
  display: flex;
  gap: 10rpx;
  margin-right: 20rpx;
}

.typing-dots .dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--primary-color);
  animation: typing-dot 1.5s infinite ease-in-out;
}

.typing-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.typing-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-style: italic;
}

/* 优化动画效果 */
@keyframes message-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typing-dot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 