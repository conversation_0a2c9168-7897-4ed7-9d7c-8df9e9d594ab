// 用户认证相关API
import { request } from '../utils/request'

/**
 * 微信登录
 * @param {string} code - 微信登录code
 * @param {object} userInfo - 用户信息
 */
export const wxLogin = (code, userInfo = {}) => {
  return request({
    url: '/api/auth/wx-login',
    method: 'POST',
    data: {
      code,
      user_info: userInfo
    }
  })
}

/**
 * 获取用户信息
 */
export const getUserProfile = () => {
  return request({
    url: '/api/user/profile',
    method: 'GET'
  })
}

/**
 * 更新用户信息
 * @param {object} data - 用户信息
 */
export const updateUserProfile = (data) => {
  return request({
    url: '/api/user/profile',
    method: 'PUT',
    data
  })
}

/**
 * 获取用户统计信息
 */
export const getUserStatistics = () => {
  return request({
    url: '/api/user/statistics',
    method: 'GET'
  })
}

/**
 * 刷新Token
 * @param {string} refreshToken - 刷新Token
 */
export const refreshToken = (refreshToken) => {
  return request({
    url: '/api/auth/refresh',
    method: 'POST',
    data: {
      refresh_token: refreshToken
    }
  })
}

/**
 * 退出登录
 */
export const logout = () => {
  return request({
    url: '/api/auth/logout',
    method: 'POST'
  })
}
