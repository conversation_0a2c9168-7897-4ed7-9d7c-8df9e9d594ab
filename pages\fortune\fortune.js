const app = getApp()

Page({
  data: {
    birthInfo: {
      birthDate: '',
      birthTime: '',
      gender: ''
    },
    fortuneResult: null,
    monthNodes: []
  },

  onLoad() {
    // 页面加载时获取本地存储的出生信息
    this.loadBirthInfo()
  },
  
  onShow() {
    // 每次页面显示时重新获取出生信息，以便在用户修改后更新
    this.loadBirthInfo()
    
    // 设置TabBar选中状态
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        })
      }
    } catch (error) {
      console.error('设置 TabBar 选中状态失败:', error)
    }
  },

  /**
   * 加载本地存储的出生信息
   */
  loadBirthInfo() {
    const birthInfo = wx.getStorageSync('birthInfo') || {}
    this.setData({
      birthInfo
    })
    
    // 如果有出生信息，自动计算运势
    if (birthInfo.birthDate && birthInfo.birthTime) {
      this.calculateFortune()
    }
  },
  
  /**
   * 跳转到出生信息页面
   */
  navigateToBirthInfo() {
    wx.navigateTo({
      url: '/pages/birth-info/birth-info'
    })
  },

  /**
   * 计算运势
   */
  calculateFortune() {
    const { birthDate, birthTime, gender } = this.data.birthInfo
    if (!birthDate || !birthTime || !gender) {
      return
    }

    // 生成月度运势数据
    const monthNodes = this.generateMonthNodes()
    
    // 这里添加运势计算逻辑
    const fortuneResult = {
      overview: '今年整体运势不错，各方面都有不错的发展机会。',
      career: '事业发展顺遂，有升职加薪的机会。',
      wealth: '财运稳定，适合稳健投资。',
      love: '感情运势良好，单身者有机会遇到心仪对象。',
      health: '身体状况良好，但要注意作息规律。',
      advice: '建议佩戴紫色饰品，有助于提升运势。'
    }

    this.setData({
      fortuneResult,
      monthNodes
    }, () => {
      this.drawFortuneChart()
    })
  },

  /**
   * 生成月度运势节点数据
   */
  generateMonthNodes() {
    return [
      { month: 3, type: 'good', description: '事业上升期' },
      { month: 6, type: 'bad', description: '财运波动' },
      { month: 9, type: 'good', description: '桃花运旺' },
      { month: 12, type: 'normal', description: '平稳过渡' }
    ]
  },

  /**
   * 绘制运势曲线图
   */
  drawFortuneChart() {
    const query = wx.createSelectorQuery()
    query.select('#fortuneChart')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          console.error('获取canvas节点失败')
          return
        }
        
        const canvas = res[0].node
        const ctx = canvas.getContext('2d')
        
        // 设置canvas大小
        const dpr = wx.getSystemInfoSync().pixelRatio
        canvas.width = res[0].width * dpr
        canvas.height = res[0].height * dpr
        ctx.scale(dpr, dpr)

        // 绘制曲线
        this.drawCurve(ctx, res[0].width, res[0].height)
      })
  },

  /**
   * 绘制曲线
   */
  drawCurve(ctx, width, height) {
    const padding = 40
    const chartWidth = width - padding * 2
    const chartHeight = height - padding * 2

    // 绘制坐标轴
    ctx.beginPath()
    ctx.strokeStyle = '#ddd'
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height - padding)
    ctx.lineTo(width - padding, height - padding)
    ctx.stroke()

    // 绘制曲线
    ctx.beginPath()
    ctx.strokeStyle = '#8a2be2'
    ctx.lineWidth = 2

    // 生成曲线点
    const points = []
    for (let i = 0; i <= 12; i++) {
      const x = padding + (chartWidth * i / 12)
      const y = height - padding - (Math.sin(i / 2) * 0.5 + 0.5) * chartHeight
      points.push({ x, y })
    }

    // 绘制曲线
    ctx.moveTo(points[0].x, points[0].y)
    for (let i = 1; i < points.length; i++) {
      const xc = (points[i].x + points[i - 1].x) / 2
      const yc = (points[i].y + points[i - 1].y) / 2
      ctx.quadraticCurveTo(points[i - 1].x, points[i - 1].y, xc, yc)
    }
    ctx.stroke()

    // 绘制节点
    this.data.monthNodes.forEach(node => {
      const x = padding + (chartWidth * (node.month - 1) / 11)
      const y = height - padding - (Math.sin((node.month - 1) / 2) * 0.5 + 0.5) * chartHeight
      
      ctx.beginPath()
      ctx.fillStyle = node.type === 'good' ? '#4CAF50' : 
                     node.type === 'bad' ? '#F44336' : '#FFC107'
      ctx.arc(x, y, 6, 0, Math.PI * 2)
      ctx.fill()
    })
  }
}) 