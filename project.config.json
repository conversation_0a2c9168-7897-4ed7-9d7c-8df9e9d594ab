{"compileType": "miniprogram", "libVersion": "3.8.0", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "urlCheck": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx59e55b1fe57dabe2", "projectname": "恒琦易道企业版", "description": "企业微信小程序配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "miniprogramRoot": "", "requestDomains": ["http://localhost:8000", "https://your-production-api-domain.com"], "wxwork": {"enable": true, "corpid": "", "agentid": "", "secretKey": ""}}