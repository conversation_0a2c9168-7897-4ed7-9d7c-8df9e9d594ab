/* pages/divination/divination.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

/* 占卜方式选择样式 */
.divination-types {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.type-item.active {
  background-color: #e6f3ff;
}

.type-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.type-name {
  font-size: 26rpx;
  color: #333;
}

/* 输入区域样式 */
.input-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.question-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 6rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.submit-btn {
  background-color: #4a90e2;
  color: #fff;
  font-size: 32rpx;
}

/* 结果区域样式 */
.result-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
}

/* 卦象展示样式 */
.gua-display {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 6rpx;
}

.gua-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
}

.gua-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.yao-line {
  width: 200rpx;
  height: 20rpx;
  display: flex;
  justify-content: center;
}

.yao-content {
  height: 100%;
  background-color: #333;
}

.yao-content.yang {
  width: 100%;
}

.yao-content.yin {
  width: 45%;
  margin: 0 2.5%;
}

/* 塔罗牌展示样式 */
.tarot-display {
  margin: 20rpx 0;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-image {
  width: 160rpx;
  height: 280rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.card-name {
  font-size: 24rpx;
  color: #333;
}

/* 解释区域样式 */
.interpretation-section {
  margin-top: 30rpx;
}

.interpretation-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 6rpx;
}

.item-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  padding-left: 10rpx;
  border-left: 4rpx solid #4a90e2;
}

.item-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}