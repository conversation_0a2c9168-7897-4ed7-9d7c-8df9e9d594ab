// 命理分析页面
import { baziAnalysis, yijingDivination, fengshuiAnalysis, wuxingAnalysis, ziweiAnalysis, marriageAnalysis } from '../../api/analysis'
import { getBirthInfo } from '../../api/birthInfo'
import { showToast, showLoading, hideLoading, showModal } from '../../utils/wx'

Page({
  data: {
    // 分析类型
    analysisType: '',
    analysisConfig: {},
    
    // 用户数据
    birthInfo: null,
    hasBirthInfo: false,
    
    // 分析结果
    analysisResult: null,
    loading: false,
    analyzing: false,
    
    // 分析类型配置
    analysisTypes: {
      bazi: {
        title: '八字分析',
        description: '通过出生年月日时分析命理特征',
        icon: '🔮',
        requiresBirthInfo: true,
        pointsCost: 10
      },
      yijing: {
        title: '易经占卜',
        description: '运用易经智慧指导人生决策',
        icon: '☯️',
        requiresBirthInfo: false,
        pointsCost: 5
      },
      fengshui: {
        title: '风水分析',
        description: '分析居住环境的风水布局',
        icon: '🏠',
        requiresBirthInfo: false,
        pointsCost: 8
      },
      wuxing: {
        title: '五行分析',
        description: '分析五行属性与运势',
        icon: '🌿',
        requiresBirthInfo: true,
        pointsCost: 10
      },
      ziwei: {
        title: '紫薇斗数',
        description: '紫薇斗数命盘分析',
        icon: '⭐',
        requiresBirthInfo: true,
        pointsCost: 15
      },
      marriage: {
        title: '合婚分析',
        description: '分析两人的婚姻匹配度',
        icon: '💕',
        requiresBirthInfo: true,
        pointsCost: 20
      }
    },
    
    // 表单数据
    formData: {
      question: '',
      description: '',
      focus_area: '',
      partner_info: null
    }
  },

  onLoad(options) {
    const { type } = options
    
    if (!type || !this.data.analysisTypes[type]) {
      showToast({
        title: '分析类型错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }
    
    this.setData({
      analysisType: type,
      analysisConfig: this.data.analysisTypes[type]
    })
    
    // 检查是否需要出生信息
    if (this.data.analysisConfig.requiresBirthInfo) {
      this.checkBirthInfo()
    }
  },

  /**
   * 检查出生信息
   */
  async checkBirthInfo() {
    try {
      this.setData({ loading: true })
      
      const result = await getBirthInfo()
      
      if (result.status === 'success' && result.data.birth_info) {
        this.setData({
          birthInfo: result.data.birth_info,
          hasBirthInfo: true
        })
      } else {
        this.setData({ hasBirthInfo: false })
        this.showBirthInfoRequired()
      }
    } catch (error) {
      console.error('获取出生信息失败:', error)
      this.setData({ hasBirthInfo: false })
      this.showBirthInfoRequired()
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 显示需要出生信息提示
   */
  showBirthInfoRequired() {
    showModal({
      title: '需要出生信息',
      content: '此分析需要您的出生信息，是否前往完善？',
      confirmText: '去完善',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/birth-info/index'
          })
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  /**
   * 表单输入处理
   */
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 开始分析
   */
  async startAnalysis() {
    // 验证表单
    if (!this.validateForm()) {
      return
    }
    
    try {
      this.setData({ analyzing: true })
      showLoading({ title: '分析中...' })
      
      // 准备分析数据
      const analysisData = this.prepareAnalysisData()
      
      // 调用对应的分析接口
      let result
      switch (this.data.analysisType) {
        case 'bazi':
          result = await baziAnalysis(analysisData)
          break
        case 'yijing':
          result = await yijingDivination(analysisData)
          break
        case 'fengshui':
          result = await fengshuiAnalysis(analysisData)
          break
        case 'wuxing':
          result = await wuxingAnalysis(analysisData)
          break
        case 'ziwei':
          result = await ziweiAnalysis(analysisData)
          break
        case 'marriage':
          result = await marriageAnalysis(analysisData)
          break
        default:
          throw new Error('不支持的分析类型')
      }
      
      if (result.status === 'success') {
        this.setData({
          analysisResult: result.data
        })
        
        // 跳转到结果页面
        wx.navigateTo({
          url: `/pages/analysis-result/index?id=${result.data.analysis_id}`
        })
      } else {
        throw new Error(result.message || '分析失败')
      }
    } catch (error) {
      console.error('分析失败:', error)
      showToast({
        title: error.message || '分析失败',
        icon: 'none'
      })
    } finally {
      this.setData({ analyzing: false })
      hideLoading()
    }
  },

  /**
   * 准备分析数据
   */
  prepareAnalysisData() {
    const { formData, birthInfo, analysisType } = this.data
    
    const baseData = {
      analysis_type: analysisType,
      question: formData.question,
      description: formData.description,
      focus_area: formData.focus_area
    }
    
    // 如果需要出生信息
    if (this.data.analysisConfig.requiresBirthInfo && birthInfo) {
      baseData.birth_info = birthInfo
    }
    
    // 合婚分析需要伴侣信息
    if (analysisType === 'marriage' && formData.partner_info) {
      baseData.partner_info = formData.partner_info
    }
    
    return baseData
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, analysisConfig, hasBirthInfo } = this.data
    
    // 检查出生信息
    if (analysisConfig.requiresBirthInfo && !hasBirthInfo) {
      showToast({
        title: '请先完善出生信息',
        icon: 'none'
      })
      return false
    }
    
    // 检查问题描述
    if (!formData.question.trim()) {
      showToast({
        title: '请输入您的问题',
        icon: 'none'
      })
      return false
    }
    
    // 合婚分析需要伴侣信息
    if (this.data.analysisType === 'marriage' && !formData.partner_info) {
      showToast({
        title: '请添加伴侣信息',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  /**
   * 添加伴侣信息
   */
  addPartnerInfo() {
    wx.navigateTo({
      url: '/pages/partner-info/index',
      events: {
        partnerInfoAdded: (data) => {
          this.setData({
            'formData.partner_info': data
          })
        }
      }
    })
  },

  /**
   * 选择关注领域
   */
  onFocusAreaChange(e) {
    const focusAreas = ['事业', '财运', '感情', '健康', '学业', '家庭']
    const selectedArea = focusAreas[e.detail.value]
    
    this.setData({
      'formData.focus_area': selectedArea
    })
  },

  /**
   * 查看分析历史
   */
  viewHistory() {
    wx.navigateTo({
      url: `/pages/analysis-history/index?type=${this.data.analysisType}`
    })
  },

  /**
   * 查看分析说明
   */
  viewDescription() {
    wx.navigateTo({
      url: `/pages/analysis-description/index?type=${this.data.analysisType}`
    })
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: `${this.data.analysisConfig.title} - 卦里乾坤`,
      path: `/pages/analysis/index?type=${this.data.analysisType}`,
      imageUrl: '/images/share-analysis.png'
    }
  }
})
