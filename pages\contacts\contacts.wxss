/* pages/contacts/contacts.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-bar {
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 30rpx;
  padding: 15rpx 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 部门导航样式 */
.department-nav {
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  padding: 20rpx 0;
}

.department-scroll {
  white-space: nowrap;
}

.department-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin: 0 10rpx;
  font-size: 26rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.department-item.active {
  background: #8a2be2;
  color: #fff;
}

.department-item:first-child {
  margin-left: 20rpx;
}

.department-item:last-child {
  margin-right: 20rpx;
}

/* 联系人列表样式 */
.contacts-list {
  padding: 20rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading icon {
  margin-bottom: 20rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.contact-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.contact-avatar {
  position: relative;
  margin-right: 30rpx;
}

.avatar-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

.online-status {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.online-status.online {
  background: #52c41a;
}

.online-status.offline {
  background: #d9d9d9;
}

.online-status.busy {
  background: #ff4d4f;
}

.contact-info {
  flex: 1;
  overflow: hidden;
}

.contact-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-detail {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-mobile {
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.chat-btn {
  background: #8a2be2;
  color: #fff;
}

.call-btn {
  background: #52c41a;
  color: #fff;
}

/* 浮动按钮样式 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: #8a2be2;
  border-radius: 50%;
  box-shadow: 0 8rpx 20rpx rgba(138, 43, 226, 0.3);
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.9);
}

.fab-menu {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
}

.fab-menu.show {
  opacity: 1;
  transform: translateY(0);
}

.fab-menu-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 15rpx;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  min-width: 200rpx;
  font-size: 26rpx;
  color: #333;
  transition: all 0.3s ease;
}

.fab-menu-item:active {
  transform: scale(0.95);
}

.fab-menu-item icon {
  margin-right: 15rpx;
}

/* AI功能菜单项特殊样式 */
.fab-menu-item.ai-item {
  background: linear-gradient(45deg, #8a2be2, #9b59b6);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.fab-menu-item.ai-item icon {
  color: #fff;
}

.fab-menu-item.ai-item:active {
  background: linear-gradient(45deg, #7a1fb2, #8b4d9b);
  transform: scale(0.95);
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 99;
} 