<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <view class="form-section" wx:if="{{!showResult}}">
    <!-- 男方信息 -->
    <view class="person-info male">
      <view class="section-title">男方信息</view>
      
      <view class="form-item">
        <text class="label">出生日期</text>
        <picker mode="date" value="{{male.date}}" bindchange="bindMaleDateChange">
          <view class="picker {{male.date ? '' : 'placeholder'}}">
            {{male.date || '请选择出生日期'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">出生时间</text>
        <picker mode="time" value="{{male.time}}" bindchange="bindMaleTimeChange">
          <view class="picker {{male.time ? '' : 'placeholder'}}">
            {{male.time || '请选择出生时间'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">出生地点</text>
        <view class="location-picker" bindtap="chooseMaleLocation">
          <text class="{{male.location ? '' : 'placeholder'}}">{{male.location || '点击选择出生地点'}}</text>
        </view>
      </view>
    </view>

    <!-- 女方信息 -->
    <view class="person-info female">
      <view class="section-title">女方信息</view>
      
      <view class="form-item">
        <text class="label">出生日期</text>
        <picker mode="date" value="{{female.date}}" bindchange="bindFemaleDateChange">
          <view class="picker {{female.date ? '' : 'placeholder'}}">
            {{female.date || '请选择出生日期'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">出生时间</text>
        <picker mode="time" value="{{female.time}}" bindchange="bindFemaleTimeChange">
          <view class="picker {{female.time ? '' : 'placeholder'}}">
            {{female.time || '请选择出生时间'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">出生地点</text>
        <view class="location-picker" bindtap="chooseFemaleLocation">
          <text class="{{female.location ? '' : 'placeholder'}}">{{female.location || '点击选择出生地点'}}</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <button class="submit-btn" bindtap="submitAnalysis" loading="{{loading}}">
      开始测算
    </button>
  </view>

  <!-- 分析结果 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-header">
      <view class="section-title">合婚结果</view>
      <view class="reset-btn" bindtap="resetAnalysis">重新测算</view>
    </view>

    <!-- 分析类型选择 -->
    <scroll-view class="analysis-tabs" scroll-x="true">
      <view 
        class="analysis-tab {{currentType === item.id ? 'active' : ''}}" 
        wx:for="{{analysisTypes}}" 
        wx:key="id"
        bindtap="switchAnalysisType"
        data-type="{{item.id}}"
      >
        {{item.name}}
      </view>
    </scroll-view>

    <view class="result-content">
      <!-- 总体评分 -->
      <view class="score-section" wx:if="{{currentType === 'overall'}}">
        <view class="score-ring">
          <view class="score-value">{{analysis.overall.score}}分</view>
          <view class="score-desc">{{analysis.overall.level}}</view>
        </view>
        <view class="score-detail">{{analysis.overall.description}}</view>
      </view>

      <!-- 各项分析 -->
      <view class="analysis-detail" wx:if="{{currentType !== 'overall'}}">
        <view class="detail-header">
          <view class="detail-score">匹配度：{{analysis[currentType].score}}分</view>
          <view class="detail-level">{{analysis[currentType].level}}</view>
        </view>

        <view class="detail-content">
          <view class="detail-item" wx:for="{{analysis[currentType].details}}" wx:key="title">
            <view class="detail-title">{{item.title}}</view>
            <view class="detail-desc">{{item.content}}</view>
          </view>
        </view>

        <view class="suggestions">
          <view class="suggestion-title">建议</view>
          <view class="suggestion-item" wx:for="{{analysis[currentType].suggestions}}" wx:key="index">
            <text class="suggestion-dot">•</text>
            <text class="suggestion-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享按钮 -->
    <button class="share-btn" open-type="share">
      分享测算结果
    </button>
  </view>
</view> 