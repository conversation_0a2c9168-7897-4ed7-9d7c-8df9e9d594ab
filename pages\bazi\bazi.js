const BaziCalculator = require('../../utils/bazi.js');
const { checkBirthInfo, navigateToBirthInfo } = require('../../utils/util.js');

Page({
  data: {
    birthInfo: null,
    baziResult: null,
    loading: false,
    error: null
  },

  onLoad: function() {
    // 检查是否有出生信息
    if (!checkBirthInfo()) {
      navigateToBirthInfo('/pages/bazi/bazi');
      return;
    }
    this.loadBirthInfo();
  },

  onShow: function() {
    this.loadBirthInfo();
  },

  // 加载出生信息
  loadBirthInfo: function() {
    const birthInfo = wx.getStorageSync('birthInfo');
    if (birthInfo) {
      this.setData({ birthInfo });
      this.calculateBazi(birthInfo);
    }
  },

  // 计算八字
  calculateBazi: function(birthInfo) {
    if (!birthInfo) return;

    this.setData({ loading: true, error: null });

    try {
      const calculator = new BaziCalculator();
      const result = calculator.calculateBazi(
        birthInfo.birthDate,
        birthInfo.birthTime,
        birthInfo.gender
      );

      // 处理五行分析数据
      const wuxingData = this.processWuxingData(result.wuxing);
      
      // 处理十神格局
      const shishenData = this.processShishenData(result.shishen);
      
      // 处理大运流年数据
      const fortuneData = this.processFortuneData(result.fortune);

      // 处理喜用神分析
      const xiyongData = this.processXiyongData(result.xiyong);

      this.setData({
        baziResult: {
          ...result,
          wuxing: wuxingData,
          shishen: shishenData,
          fortune: fortuneData,
          xiyong: xiyongData
        },
        loading: false
      });
    } catch (error) {
      console.error('八字计算错误:', error);
      this.setData({
        error: '计算八字时出现错误，请检查输入信息是否正确',
        loading: false
      });
    }
  },

  // 处理五行分析数据
  processWuxingData: function(wuxing) {
    if (!wuxing) return null;
    
    // 直接返回五行数据，因为 bazi.js 中已经处理好了格式
    return wuxing;
  },

  // 处理十神格局数据
  processShishenData: function(shishen) {
    if (!shishen) return null;

    return {
      pattern: shishen.pattern,
      description: shishen.description,
      details: shishen.details.map(item => ({
        name: item.name,
        value: item.value,
        meaning: item.meaning
      }))
    };
  },

  // 处理大运流年数据
  processFortuneData: function(fortune) {
    if (!fortune) return null;

    return fortune.map(item => {
      // 确保所有必要的字段都存在
      const age = item.age || '';
      const year = item.year || '';
      const pillar = item.pillar || '';
      const shishen = item.shishen || '';
      const luck = item.luck || '平';

      return {
        age,
        year,
        pillar,
        shishen,
        luck
      };
    });
  },

  // 处理喜用神分析
  processXiyongData: function(xiyong) {
    if (!xiyong) return null;

    return {
      favorable: xiyong.favorable.map(item => ({
        element: item.element,
        reason: item.reason
      })),
      unfavorable: xiyong.unfavorable.map(item => ({
        element: item.element,
        reason: item.reason
      })),
      advice: xiyong.advice
    };
  },

  // 重新计算八字
  recalculate: function() {
    if (this.data.birthInfo) {
      this.calculateBazi(this.data.birthInfo);
    }
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '我的八字命盘分析',
      path: '/pages/bazi/bazi'
    };
  }
}); 