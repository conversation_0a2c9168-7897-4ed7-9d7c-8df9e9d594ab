<view class="container">
  <!-- 显示出生信息 -->
  <view class="birth-info-display">
    <view class="section-title">出生信息</view>
    <view class="info-content">
      <view class="info-item">
        <text class="info-label">出生日期：</text>
        <text class="info-value">{{birthInfo.birthDate || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">出生时间：</text>
        <text class="info-value">{{birthInfo.birthTime || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">性别：</text>
        <text class="info-value">{{birthInfo.gender || '未设置'}}</text>
      </view>
    </view>
    <view class="info-actions">
      <button class="action-button" bindtap="navigateToBirthInfo">修改出生信息</button>
    </view>
  </view>
  
  <!-- 运势曲线图 -->
  <view class="chart-section" wx:if="{{fortuneResult}}">
    <view class="section-title">年度运势走势</view>
    <view class="chart-container">
      <canvas type="2d" id="fortuneChart" class="fortune-chart"></canvas>
    </view>
    <view class="month-nodes">
      <view class="node-item" wx:for="{{monthNodes}}" wx:key="month">
        <view class="node-dot {{item.type}}"></view>
        <view class="node-label">{{item.month}}月</view>
        <view class="node-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <!-- 结果展示区域 -->
  <view class="result-section" wx:if="{{fortuneResult}}">
    <view class="section-title">运势分析结果</view>
    
    <!-- 运势概览 -->
    <view class="overview-section">
      <view class="overview-item">
        <view class="item-title">总体运势</view>
        <view class="item-content">{{fortuneResult.overview}}</view>
      </view>
    </view>

    <!-- 运势详情 -->
    <view class="detail-section">
      <view class="detail-item">
        <view class="item-title">事业运势</view>
        <view class="item-content">{{fortuneResult.career}}</view>
      </view>

      <view class="detail-item">
        <view class="item-title">财运分析</view>
        <view class="item-content">{{fortuneResult.wealth}}</view>
      </view>

      <view class="detail-item">
        <view class="item-title">感情运势</view>
        <view class="item-content">{{fortuneResult.love}}</view>
      </view>

      <view class="detail-item">
        <view class="item-title">健康运势</view>
        <view class="item-content">{{fortuneResult.health}}</view>
      </view>
    </view>

    <!-- 运势建议 -->
    <view class="advice-section">
      <view class="section-title">运势建议</view>
      <view class="advice-item">
        <view class="item-title">开运建议</view>
        <view class="item-content">{{fortuneResult.advice}}</view>
      </view>
    </view>
  </view>
  
  <!-- 无出生信息提示 -->
  <view class="empty-tip" wx:if="{{!birthInfo.birthDate}}">
    <text class="tip-text">请先设置出生信息</text>
    <button class="tip-button" bindtap="navigateToBirthInfo">去设置</button>
  </view>
</view> 