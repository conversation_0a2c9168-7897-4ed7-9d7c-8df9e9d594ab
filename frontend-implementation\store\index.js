// 全局状态管理
import { getStorageSync, setStorageSync } from '../utils/wx'

class Store {
  constructor() {
    this.state = {
      // 用户信息
      user: {
        isLogin: false,
        userInfo: null,
        token: null,
        refreshToken: null
      },
      
      // 出生信息
      birthInfo: {
        hasInfo: false,
        data: null
      },
      
      // 系统配置
      config: {
        apiBaseUrl: 'https://api.gualiankun.com',
        version: '1.0.0',
        debug: false
      },
      
      // 应用状态
      app: {
        loading: false,
        networkStatus: 'online',
        systemInfo: null
      },
      
      // 聊天状态
      chat: {
        currentSessionId: null,
        sessions: [],
        quickActions: []
      },
      
      // 分析状态
      analysis: {
        history: [],
        currentAnalysis: null
      }
    }
    
    this.listeners = new Map()
    this.init()
  }

  /**
   * 初始化状态
   */
  init() {
    // 从缓存恢复用户状态
    this.restoreUserState()
    
    // 监听网络状态
    this.watchNetworkStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  }

  /**
   * 恢复用户状态
   */
  restoreUserState() {
    const token = getStorageSync('token')
    const refreshToken = getStorageSync('refreshToken')
    const userInfo = getStorageSync('userInfo')
    const birthInfo = getStorageSync('birthInfo')
    
    if (token && userInfo) {
      this.setState('user', {
        isLogin: true,
        userInfo,
        token,
        refreshToken
      })
    }
    
    if (birthInfo) {
      this.setState('birthInfo', {
        hasInfo: true,
        data: birthInfo
      })
    }
  }

  /**
   * 监听网络状态
   */
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      this.setState('app.networkStatus', res.isConnected ? 'online' : 'offline')
    })
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.setState('app.systemInfo', res)
      }
    })
  }

  /**
   * 设置状态
   * @param {string} path 状态路径
   * @param {any} value 状态值
   */
  setState(path, value) {
    const keys = path.split('.')
    let current = this.state
    
    // 导航到目标对象
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }
    
    // 设置值
    const lastKey = keys[keys.length - 1]
    const oldValue = current[lastKey]
    current[lastKey] = value
    
    // 通知监听器
    this.notifyListeners(path, value, oldValue)
  }

  /**
   * 获取状态
   * @param {string} path 状态路径
   */
  getState(path) {
    const keys = path.split('.')
    let current = this.state
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key]
      } else {
        return undefined
      }
    }
    
    return current
  }

  /**
   * 订阅状态变化
   * @param {string} path 状态路径
   * @param {function} callback 回调函数
   */
  subscribe(path, callback) {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set())
    }
    
    this.listeners.get(path).add(callback)
    
    // 返回取消订阅函数
    return () => {
      const pathListeners = this.listeners.get(path)
      if (pathListeners) {
        pathListeners.delete(callback)
        if (pathListeners.size === 0) {
          this.listeners.delete(path)
        }
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} path 状态路径
   * @param {any} newValue 新值
   * @param {any} oldValue 旧值
   */
  notifyListeners(path, newValue, oldValue) {
    // 通知精确路径的监听器
    const pathListeners = this.listeners.get(path)
    if (pathListeners) {
      pathListeners.forEach(callback => {
        try {
          callback(newValue, oldValue, path)
        } catch (error) {
          console.error('状态监听器执行错误:', error)
        }
      })
    }
    
    // 通知父路径的监听器
    const pathParts = path.split('.')
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join('.')
      const parentListeners = this.listeners.get(parentPath)
      if (parentListeners) {
        const parentValue = this.getState(parentPath)
        parentListeners.forEach(callback => {
          try {
            callback(parentValue, parentValue, parentPath)
          } catch (error) {
            console.error('父路径状态监听器执行错误:', error)
          }
        })
      }
    }
  }

  /**
   * 用户登录
   * @param {object} userInfo 用户信息
   * @param {string} token 访问令牌
   * @param {string} refreshToken 刷新令牌
   */
  login(userInfo, token, refreshToken) {
    // 保存到缓存
    setStorageSync('userInfo', userInfo)
    setStorageSync('token', token)
    setStorageSync('refreshToken', refreshToken)
    
    // 更新状态
    this.setState('user', {
      isLogin: true,
      userInfo,
      token,
      refreshToken
    })
  }

  /**
   * 用户登出
   */
  logout() {
    // 清除缓存
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.removeStorageSync('refreshToken')
    
    // 重置状态
    this.setState('user', {
      isLogin: false,
      userInfo: null,
      token: null,
      refreshToken: null
    })
    
    // 清除其他用户相关数据
    this.setState('birthInfo', {
      hasInfo: false,
      data: null
    })
    
    this.setState('chat', {
      currentSessionId: null,
      sessions: [],
      quickActions: []
    })
  }

  /**
   * 更新用户信息
   * @param {object} userInfo 用户信息
   */
  updateUserInfo(userInfo) {
    const currentUser = this.getState('user')
    const updatedUser = {
      ...currentUser,
      userInfo: {
        ...currentUser.userInfo,
        ...userInfo
      }
    }
    
    setStorageSync('userInfo', updatedUser.userInfo)
    this.setState('user', updatedUser)
  }

  /**
   * 设置出生信息
   * @param {object} birthInfo 出生信息
   */
  setBirthInfo(birthInfo) {
    setStorageSync('birthInfo', birthInfo)
    this.setState('birthInfo', {
      hasInfo: true,
      data: birthInfo
    })
  }

  /**
   * 设置当前聊天会话
   * @param {string} sessionId 会话ID
   */
  setCurrentChatSession(sessionId) {
    this.setState('chat.currentSessionId', sessionId)
  }

  /**
   * 添加聊天会话
   * @param {object} session 会话信息
   */
  addChatSession(session) {
    const sessions = this.getState('chat.sessions') || []
    const updatedSessions = [session, ...sessions]
    this.setState('chat.sessions', updatedSessions)
  }

  /**
   * 删除聊天会话
   * @param {string} sessionId 会话ID
   */
  removeChatSession(sessionId) {
    const sessions = this.getState('chat.sessions') || []
    const updatedSessions = sessions.filter(session => session.id !== sessionId)
    this.setState('chat.sessions', updatedSessions)
    
    // 如果删除的是当前会话，清除当前会话ID
    if (this.getState('chat.currentSessionId') === sessionId) {
      this.setState('chat.currentSessionId', null)
    }
  }

  /**
   * 设置快捷操作
   * @param {array} quickActions 快捷操作列表
   */
  setQuickActions(quickActions) {
    this.setState('chat.quickActions', quickActions)
  }

  /**
   * 添加分析记录
   * @param {object} analysis 分析记录
   */
  addAnalysisRecord(analysis) {
    const history = this.getState('analysis.history') || []
    const updatedHistory = [analysis, ...history]
    this.setState('analysis.history', updatedHistory)
  }

  /**
   * 设置当前分析
   * @param {object} analysis 分析信息
   */
  setCurrentAnalysis(analysis) {
    this.setState('analysis.currentAnalysis', analysis)
  }

  /**
   * 设置加载状态
   * @param {boolean} loading 是否加载中
   */
  setLoading(loading) {
    this.setState('app.loading', loading)
  }

  /**
   * 获取完整状态（用于调试）
   */
  getFullState() {
    return JSON.parse(JSON.stringify(this.state))
  }
}

// 创建全局store实例
const store = new Store()

// 导出store实例和便捷方法
export default store

export const {
  setState,
  getState,
  subscribe,
  login,
  logout,
  updateUserInfo,
  setBirthInfo,
  setCurrentChatSession,
  addChatSession,
  removeChatSession,
  setQuickActions,
  addAnalysisRecord,
  setCurrentAnalysis,
  setLoading
} = store
