<!--pages/post-detail/post-detail.wxml-->
<view class="page-container">
  <!-- 加载组件 -->
  <loading show="{{isLoading}}" text="加载中..." />
  
  <scroll-view class="content-scroll" scroll-y wx:if="{{!isLoading}}">
    <!-- 文章头图 -->
    <view class="article-header" wx:if="{{article.coverImage}}">
      <image class="cover-image" src="{{article.coverImage}}" mode="aspectFill" />
      <view class="header-mask"></view>
      <view class="header-info">
        <text class="article-category">{{article.category || '命理知识'}}</text>
        <text class="article-title">{{article.title}}</text>
      </view>
    </view>
    
    <!-- 作者信息 -->
    <view class="author-section">
      <view class="author-info">
        <image class="author-avatar" src="{{article.author.avatar || '/assets/images/default-avatar.png'}}" />
        <view class="author-details">
          <text class="author-name">{{article.author.name}}</text>
          <text class="author-desc">{{article.author.description || '资深命理师'}}</text>
        </view>
      </view>
      <view class="article-stats">
        <text class="stat-item">{{article.readCount || 0}} 阅读</text>
        <text class="stat-item">{{article.publishTime}}</text>
      </view>
    </view>
    
    <!-- 文章内容 -->
    <view class="article-content">
      <!-- 文章摘要 -->
      <view class="article-summary" wx:if="{{article.summary}}">
        <text>{{article.summary}}</text>
      </view>
      
      <!-- 正文内容 -->
      <view class="article-body">
        <rich-text nodes="{{article.content}}" />
      </view>
      
      <!-- 相关标签 -->
      <view class="article-tags" wx:if="{{article.tags && article.tags.length}}">
        <view class="tag-item" wx:for="{{article.tags}}" wx:key="index">
          #{{item}}
        </view>
      </view>
    </view>
    
    <!-- 互动区域 -->
    <view class="interaction-section">
      <view class="interaction-bar">
        <view class="interaction-item {{liked ? 'active' : ''}}" bindtap="handleLike">
          <text class="iconfont icon-like">👍</text>
          <text>{{article.likeCount || 0}}</text>
        </view>
        <view class="interaction-item" bindtap="handleCollect">
          <text class="iconfont icon-collect">{{collected ? '⭐' : '☆'}}</text>
          <text>收藏</text>
        </view>
        <view class="interaction-item" bindtap="handleShare">
          <text class="iconfont icon-share">📤</text>
          <text>分享</text>
        </view>
      </view>
    </view>
    
    <!-- 相关推荐 -->
    <view class="related-section" wx:if="{{relatedArticles.length}}">
      <view class="section-title">相关推荐</view>
      <view class="related-list">
        <view class="related-item" wx:for="{{relatedArticles}}" wx:key="id" bindtap="goToArticle" data-id="{{item.id}}">
          <image class="related-image" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="related-info">
            <text class="related-title">{{item.title}}</text>
            <text class="related-meta">{{item.author.name}} · {{item.readCount}}阅读</text>
          </view>
      </view>
      </view>
    </view>
    
    <!-- 评论区域 -->
    <view class="comment-section">
      <view class="section-title">
        评论 ({{comments.length}})
  </view>
  
      <!-- 评论列表 -->
    <view class="comment-list">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
          <image class="comment-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" />
        <view class="comment-content">
            <view class="comment-header">
              <text class="comment-author">{{item.author}}</text>
              <text class="comment-time">{{item.time}}</text>
            </view>
            <text class="comment-text">{{item.content}}</text>
            <view class="comment-actions">
              <text class="action-btn" bindtap="likeComment" data-id="{{item.id}}">
                {{item.liked ? '❤️' : '🤍'}} {{item.likeCount || 0}}
              </text>
              <text class="action-btn" bindtap="replyComment" data-id="{{item.id}}">回复</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMoreComments}}" bindtap="loadMoreComments">
        <text>{{loadingComments ? '加载中...' : '查看更多评论'}}</text>
    </view>
    </view>
  </scroll-view>
  
  <!-- 底部评论输入 -->
  <view class="comment-input-bar">
    <input 
      class="comment-input"
      placeholder="写下你的想法..."
      value="{{commentInput}}"
      bindinput="onCommentInput"
      bindconfirm="submitComment"
    />
    <button class="comment-submit" bindtap="submitComment">发送</button>
  </view>
</view> 