/* yijing.wxss */
.container {
  padding: 30rpx;
  background-color: var(--background-color) !important;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 20rpx 0;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 卡片通用样式 */
.card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.icon {
  margin-right: 10rpx;
  font-size: 36rpx;
  color: var(--primary-color);
}

/* 输入区域样式 */
.input-section {
  margin-bottom: 30rpx;
}

.input-group {
  margin-bottom: 25rpx;
}

.picker {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.label {
  width: 160rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

.value {
  flex: 1;
  color: var(--text-primary);
  font-size: 28rpx;
}

.gender-label {
  color: var(--text-secondary);
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.gender-group {
  display: flex;
  gap: 60rpx;
}

.gender-option {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-primary);
}

.save-btn {
  margin-top: 30rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 问题输入区域样式 */
.question-section {
  position: relative;
}

.question-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: var(--primary-lightest);
}

.char-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: var(--text-light);
}

/* 占卜按钮样式 */
.divine-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin: 40rpx auto;
  box-shadow: 0 6rpx 16rpx var(--shadow-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

/* 结果显示区域样式 */
.result-section {
  margin-bottom: 40rpx;
}

/* 卦象显示样式 */
.hexagram-card {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
}

.hexagram-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.hexagram-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-right: 15rpx;
}

.hexagram-name {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.hexagram-lines {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  margin: 30rpx 0;
}

.line {
  width: 240rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.line.yang {
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.line.yin {
  background-color: transparent;
  border-top: 4rpx solid var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

.line-value {
  position: absolute;
  right: -50rpx;
  color: var(--primary-color);
  font-size: 24rpx;
  font-weight: bold;
}

.hexagram-nature {
  text-align: center;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-top: 20rpx;
}

/* 解读内容样式 */
.interpretation {
  margin-top: 40rpx;
}

.interpretation-section {
  margin-bottom: 30rpx;
}

.interpretation-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.interpretation-content {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.8;
  padding: 20rpx;
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
}

/* 分享按钮样式 */
.share-btn {
  margin-top: 30rpx;
  background-color: var(--primary-lightest);
  color: var(--primary-color);
  font-size: 28rpx;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid var(--border-color);
}

/* 底部提示 */
.footer-tip {
  text-align: center;
  font-size: 24rpx;
  color: var(--text-light);
  margin: 30rpx 0;
  padding: 20rpx 0;
}

.birth-info {
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid var(--border-color);
}

.info-title {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-bottom: 16rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-item {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.submit-btn {
  margin-top: 40rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
  border: none;
  width: 100%;
  font-weight: bold;
  letter-spacing: 2rpx;
  transition: all 0.3s ease;
}

.submit-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 4rpx var(--shadow-color);
} 