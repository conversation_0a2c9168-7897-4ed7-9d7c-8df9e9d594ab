const app = getApp()
const { <PERSON>gua<PERSON>hart, WuxingChart } = require('../../utils/charts')
const { FengshuiCalculator } = require('../../utils/fengshui/calculator')

Page({
  data: {
    // 房屋类型选项
    houseTypes: ['住宅', '办公', '商铺', '厂房'],
    houseTypeIndex: null,

    // 方位选项
    directions: ['坐北朝南', '坐南朝北', '坐东朝西', '坐西朝东', 
                '坐东北朝西南', '坐西南朝东北', '坐东南朝西北', '坐西北朝东南'],
    directionIndex: null,

    // 户型选项
    roomNums: [1, 2, 3, 4, 5, 6],
    hallNums: [1, 2, 3],
    bathNums: [1, 2, 3, 4],
    roomNumIndex: -1,
    hallNumIndex: -1,
    bathNumIndex: -1,

    // 基本信息
    buildYear: '',
    area: '',
    loading: false,
    showResult: false,
    baguaResult: [],
    positionResult: {
      auspicious: [],
      inauspicious: []
    },
    layoutAdvice: [],
    solutions: [],
    canSubmit: false,
    price: 28
  },

  onLoad() {
    // 初始化图表
    this.baguaChart = new BaguaChart()
    this.wuxingChart = new WuxingChart()
  },

  // 房屋类型选择
  onHouseTypeChange(e) {
    this.setData({
      houseTypeIndex: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 朝向选择
  onDirectionChange(e) {
    this.setData({
      directionIndex: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 建筑年份选择
  onBuildYearChange(e) {
    this.setData({
      buildYear: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 面积输入
  onAreaInput(e) {
    this.setData({
      area: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 户型选择
  onRoomNumChange(e) {
    this.setData({
      roomNumIndex: e.detail.value
    })
    this.checkCanSubmit()
  },

  onHallNumChange(e) {
    this.setData({
      hallNumIndex: e.detail.value
    })
    this.checkCanSubmit()
  },

  onBathNumChange(e) {
    this.setData({
      bathNumIndex: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { 
      houseTypeIndex, directionIndex, buildYear, area,
      roomNumIndex, hallNumIndex, bathNumIndex 
    } = this.data

    const canSubmit = 
      houseTypeIndex !== null && 
      directionIndex !== null && 
      buildYear && 
      area && 
      roomNumIndex > -1 && 
      hallNumIndex > -1 && 
      bathNumIndex > -1

    this.setData({ canSubmit })
  },

  // 开始分析
  async analyzeFengshui() {
    if (!this.validateInput()) {
      return;
    }

    this.setData({ loading: true });

    try {
      const calculator = new FengshuiCalculator();
      const result = await calculator.calculate({
        direction: this.data.directions[this.data.directionIndex],
        houseType: this.data.houseTypes[this.data.houseTypeIndex],
        buildYear: this.data.buildYear,
        area: parseFloat(this.data.area)
      });

      this.setData({
        loading: false,
        showResult: true,
        baguaResult: result.bagua.map(item => ({
          direction: item.direction,
          name: item.name,
          element: item.element,
          active: item.isActive
        })),
        positionResult: {
          auspicious: result.positions.auspicious.map(item => ({
            direction: item.direction,
            usage: item.recommendedUsage,
            description: item.description
          })),
          inauspicious: result.positions.inauspicious.map(item => ({
            direction: item.direction,
            usage: item.avoidance,
            description: item.description
          }))
        },
        layoutAdvice: result.layout.map(item => ({
          room: item.room,
          suggestion: item.suggestion
        })),
        solutions: result.solutions.map(item => ({
          type: item.type,
          description: item.description
        }))
      });

      // 绘制图表
      this.drawCharts(result.baguaData, result.wuxingData)
    } catch (error) {
      console.error('风水分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  validateInput() {
    if (this.data.directionIndex === null) {
      wx.showToast({
        title: '请选择房屋朝向',
        icon: 'none'
      });
      return false;
    }
    if (this.data.houseTypeIndex === null) {
      wx.showToast({
        title: '请选择房屋类型',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.buildYear) {
      wx.showToast({
        title: '请选择建筑年份',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.area) {
      wx.showToast({
        title: '请输入房屋面积',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  // 绘制图表
  drawCharts(baguaData, wuxingData) {
    this.baguaChart.draw('baguaCanvas', baguaData)
    this.wuxingChart.draw('wuxingCanvas', wuxingData)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '风水布局分析',
      path: '/pages/fengshui/index'
    };
  },

  // 页面卸载
  onUnload() {
    // 清理图表实例
    if (this.baguaChart) {
      this.baguaChart.dispose()
    }
    if (this.wuxingChart) {
      this.wuxingChart.dispose()
    }
  }
}) 