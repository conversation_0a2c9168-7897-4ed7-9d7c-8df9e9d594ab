/* ziwei.wxss */
.container {
  padding: 20rpx;
  background-color: var(--background-color) !important;
  min-height: 100vh;
}

/* 输入区域样式 */
.input-section {
  background-color: var(--card-background);
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid var(--primary-color);
}

.input-group {
  margin-bottom: 20rpx;
}

.picker {
  padding: 20rpx;
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.radio {
  margin-right: 30rpx;
  color: var(--text-primary);
}

.calculate-btn {
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 结果区域样式 */
.result-section {
  margin-top: 20rpx;
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

/* 紫微斗数命盘样式 */
.ziwei-chart {
  margin: 20rpx 0;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rpx;
  background-color: var(--border-color);
  padding: 2rpx;
  border-radius: 12rpx;
}

.grid-cell {
  background-color: var(--card-background);
  padding: 10rpx;
  text-align: center;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 2rpx solid var(--border-color);
}

.palace-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 6rpx;
}

.main-star {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-bottom: 6rpx;
}

.minor-stars {
  font-size: 20rpx;
  color: var(--text-light);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4rpx;
}

/* 命盘信息样式 */
.chart-info {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 2rpx solid var(--border-color);
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 命理解读样式 */
.interpretation-section {
  margin-top: 30rpx;
}

.interpretation-item {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
}

.item-title {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-bottom: 12rpx;
}

.item-content {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 大限流年样式 */
.fortune-scroll {
  margin-top: 20rpx;
  white-space: nowrap;
  overflow-x: auto;
  padding: 10rpx 0;
}

.fortune-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.fortune-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  background-color: var(--primary-lightest);
  padding: 20rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
  border: 2rpx solid var(--border-color);
}

.fortune-age {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.fortune-year {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-bottom: 10rpx;
}

.fortune-palace {
  font-size: 24rpx;
  color: var(--primary-dark);
}

.chart-section {
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.chart-container {
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  padding: 20rpx;
  min-height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2rpx solid var(--border-color);
}

.chart-placeholder {
  color: var(--text-light);
  font-size: 28rpx;
}

/* 加载中样式 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
}

/* 错误提示样式 */
.error {
  text-align: center;
  padding: 40rpx;
  color: #ff4d4f;
}

/* 星耀类型样式 */
.star-main {
  background: #e6f7ff;
  color: #1890ff;
}

.star-assistant {
  background: #f6ffed;
  color: #52c41a;
}

.star-lucky {
  background: #fff7e6;
  color: #fa8c16;
}

.star-evil {
  background: #fff1f0;
  color: #f5222d;
}

/* 十四主星组合样式 */
.star-combinations-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
}

.combinations-list {
  padding: 20rpx;
}

.combination-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.combination-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.combination-stars {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.combination-meaning {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 人生轨迹预测样式 */
.life-trajectory-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
}

.trajectory-content {
  padding: 20rpx;
}

.trajectory-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.trajectory-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.trajectory-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.trajectory-detail {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px dashed #ddd;
}

.detail-item {
  margin-bottom: 16rpx;
}

.detail-title {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.detail-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 10rpx;
} 