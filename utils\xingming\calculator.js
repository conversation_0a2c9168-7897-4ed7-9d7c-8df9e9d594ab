// 汉字笔画数据库
const STROKE_DATA = {
  // 常用姓氏
  '王': 4, '李': 7, '张': 11, '刘': 8, '陈': 10, '杨': 7, '黄': 12,
  '赵': 9, '吴': 7, '周': 8, '徐': 10, '孙': 10, '马': 10, '朱': 6,
  '胡': 10, '郭': 11, '何': 7, '高': 10, '林': 8, '郑': 9, '谢': 12,
  '罗': 8, '梁': 11, '宋': 7, '唐': 10, '许': 6, '韩': 12, '冯': 9,
  '邓': 7, '曹': 11, '彭': 12, '曾': 12, '萧': 12, '田': 5, '董': 12,
  '袁': 10, '潘': 15, '于': 3, '蒋': 13, '蔡': 13, '余': 7, '杜': 7,
  '叶': 6, '程': 12, '苏': 9, '魏': 13, '吕': 6, '丁': 2, '任': 6,
  '沈': 7, '姚': 9, '卢': 5, '姜': 9, '崔': 11, '钟': 9, '谭': 12,
  '陆': 7, '汪': 7, '范': 8, '金': 8, '石': 5, '廖': 13, '贾': 10,
  '夏': 10, '韦': 6, '付': 5, '方': 4, '白': 5, '邹': 11, '孟': 8,
  '熊': 14, '秦': 10, '邱': 7, '江': 6, '尹': 4, '薛': 16, '闫': 8,
  '段': 9, '雷': 13, '侯': 9, '龙': 5, '史': 5, '陶': 10, '黎': 15,
  '贺': 8, '顾': 10, '毛': 4, '郝': 9, '龚': 11, '邵': 8, '万': 3,
  '钱': 10, '严': 7, '覃': 12, '武': 8, '戴': 17, '莫': 11, '孔': 4,

  // 常用名字
  '伟': 7, '芳': 7, '娜': 9, '秀': 7, '敏': 11, '静': 14, '丽': 7,
  '强': 11, '磊': 15, '军': 7, '洋': 9, '勇': 9, '艳': 11, '杰': 8,
  '娟': 9, '涛': 10, '明': 8, '超': 12, '秋': 9, '云': 4, '兰': 6,
  '梅': 11, '宇': 5, '琴': 12, '春': 9, '生': 5, '佳': 8, '红': 6,
  '霞': 16, '飞': 9, '达': 6, '华': 6, '英': 8, '建': 9, '国': 8,
  '玲': 9, '桂': 10, '荣': 9, '新': 13, '志': 7, '宏': 7, '晶': 12,
  '欣': 8, '燕': 16, '民': 5, '凯': 12, '文': 4, '林': 8, '莉': 10,
  '海': 10, '珍': 10, '凤': 4, '洁': 9, '浩': 10, '东': 5, '帆': 8,
  '雪': 11, '蕾': 15, '荣': 9, '春': 9, '晓': 10, '阳': 6, '淑': 11,
  '芬': 7, '振': 10, '萍': 11, '雯': 13, '琳': 12, '素': 10, '云': 4,
  '莲': 10, '真': 10, '环': 8, '雪': 11, '荣': 9, '爱': 10, '妹': 6,
  '霞': 16, '香': 9, '月': 4, '莺': 11, '媛': 12, '艳': 11, '瑞': 13,
  '凤': 4, '婷': 11, '颖': 15, '露': 21, '瑶': 13, '慧': 15, '巧': 7,
  '柳': 9, '青': 8, '彩': 11, '琴': 12, '兵': 7, '冬': 5
}

// 五格数理
const WUGE_DATA = {
  1: { nature: '阳', element: '木', luck: '吉', description: '积极开创，生机勃勃' },
  2: { nature: '阴', element: '木', luck: '凶', description: '消极被动，多受制约' },
  3: { nature: '阳', element: '火', luck: '吉', description: '旺盛活力，充满朝气' },
  4: { nature: '阴', element: '火', luck: '凶', description: '固执急躁，易生灾祸' },
  5: { nature: '阳', element: '土', luck: '吉', description: '中正平和，利于发展' },
  6: { nature: '阴', element: '土', luck: '吉', description: '温和坚韧，利于成长' },
  7: { nature: '阳', element: '金', luck: '吉', description: '刚毅果断，坚强不屈' },
  8: { nature: '阴', element: '金', luck: '吉', description: '富贵荣华，享受安康' },
  9: { nature: '阳', element: '水', luck: '凶', description: '浮躁不安，多变难定' },
  10: { nature: '阴', element: '水', luck: '吉', description: '灵活聪慧，通达四方' }
}

// 三才配置
const SANCAI_DATA = {
  '木木木': { score: 90, description: '稳健发展，前程远大' },
  '木木火': { score: 95, description: '蒸蒸日上，成就非凡' },
  '木木土': { score: 85, description: '根深叶茂，稳步向上' },
  '木木金': { score: 70, description: '内外相克，需要调和' },
  '木木水': { score: 80, description: '生机盎然，利于成长' },
  '木火木': { score: 85, description: '朝气蓬勃，充满活力' },
  '木火火': { score: 80, description: '光明磊落，事业有成' },
  '木火土': { score: 90, description: '兴旺发达，前途无量' },
  '木火金': { score: 75, description: '明察秋毫，谨慎行事' },
  '木火水': { score: 70, description: '起伏较大，需要节制' },
  // ... 更多三才配置数据
}

class XingmingCalculator {
  constructor(options) {
    this.options = options
    this.surname = options.surname
    this.givenName = options.givenName
    this.gender = options.gender
    this.bazi = options.bazi
  }

  async calculate() {
    try {
      // 1. 计算五格数理
      const wugeResult = this.calculateWuge()
      
      // 2. 计算三才配置
      const sancaiResult = this.calculateSancai()
      
      // 3. 计算八字配合
      const baziResult = this.calculateBaziMatch()
      
      // 4. 计算总分
      const totalScore = Math.round((wugeResult.score + sancaiResult.score + baziResult.score) / 3)
      
      // 5. 生成分析结果
      return {
        score: totalScore,
        description: this.getScoreDescription(totalScore),
        wugeList: wugeResult.list,
        sancaiAnalysis: sancaiResult.analysis,
        sancaiData: sancaiResult.data,
        baziAnalysis: baziResult.analysis,
        jixiongList: this.generateJixiongList(),
        adviceList: this.generateAdviceList()
      }
    } catch (error) {
      console.error('姓名测算错误:', error)
      throw error
    }
  }

  // 计算五格数理
  calculateWuge() {
    // 计算天格
    const tianGe = this.surname.length === 1 ? 
      STROKE_DATA[this.surname] + 1 :
      STROKE_DATA[this.surname[0]] + STROKE_DATA[this.surname[1]]

    // 计算人格
    const renGe = this.surname.length === 1 ?
      STROKE_DATA[this.surname] + STROKE_DATA[this.givenName[0]] :
      STROKE_DATA[this.surname[1]] + STROKE_DATA[this.givenName[0]]

    // 计算地格
    const diGe = this.givenName.length === 1 ?
      STROKE_DATA[this.givenName[0]] + 1 :
      STROKE_DATA[this.givenName[0]] + STROKE_DATA[this.givenName[1]]

    // 计算外格
    const waiGe = tianGe + diGe - renGe

    // 计算总格
    const zongGe = this.calculateTotalStrokes()

    // 生成五格数据
    const wugeList = [
      { name: '天格', number: tianGe, description: this.getWugeDescription(tianGe) },
      { name: '人格', number: renGe, description: this.getWugeDescription(renGe) },
      { name: '地格', number: diGe, description: this.getWugeDescription(diGe) },
      { name: '外格', number: waiGe, description: this.getWugeDescription(waiGe) },
      { name: '总格', number: zongGe, description: this.getWugeDescription(zongGe) }
    ]

    // 计算五格得分
    const score = this.calculateWugeScore(wugeList)

    return { list: wugeList, score }
  }

  // 计算三才配置
  calculateSancai() {
    // 获取三才属性
    const tianCai = this.getWuxingAttribute(this.calculateTianGe())
    const renCai = this.getWuxingAttribute(this.calculateRenGe())
    const diCai = this.getWuxingAttribute(this.calculateDiGe())

    // 组合三才
    const sancaiKey = `${tianCai}${renCai}${diCai}`
    const sancaiInfo = SANCAI_DATA[sancaiKey] || {
      score: 70,
      description: '普通配置，平稳发展'
    }

    // 生成图表数据
    const data = [
      { name: '天才', value: this.calculateAttributeStrength(tianCai), element: tianCai },
      { name: '人才', value: this.calculateAttributeStrength(renCai), element: renCai },
      { name: '地才', value: this.calculateAttributeStrength(diCai), element: diCai }
    ]

    // 生成分析文字
    const analysis = `姓名三才配置为：天才${tianCai}，人才${renCai}，地才${diCai}。${sancaiInfo.description}`

    return {
      score: sancaiInfo.score,
      analysis,
      data
    }
  }

  // 计算八字配合
  calculateBaziMatch() {
    const { bazi } = this.options
    const nameWuxing = this.getMainWuxing()
    
    let score = 60 // 基础分
    let analysis = ''

    // 判断是否与日主相生
    const riZhu = this.getRiZhuWuxing(bazi)
    if (this.isWuxingHelpful(nameWuxing, riZhu)) {
      score += 20
      analysis += '姓名五行与八字日主相生，'
    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {
      score -= 10
      analysis += '姓名五行与八字日主相克，'
    }

    // 判断是否与大运相合
    const daYun = this.getDaYunWuxing(bazi)
    if (this.isWuxingHelpful(nameWuxing, daYun)) {
      score += 10
      analysis += '与大运五行相生，有助于人生发展。'
    } else if (this.isWuxingConflict(nameWuxing, daYun)) {
      score -= 5
      analysis += '与大运五行相克，需要注意趋吉避凶。'
    } else {
      analysis += '与大运五行中和，发展平稳。'
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      analysis
    }
  }

  // 生成吉凶分析
  generateJixiongList() {
    const wuge = this.calculateWuge()
    const sancai = this.calculateSancai()
    
    return [
      {
        type: '姓名吉凶',
        description: this.getNameJixiong()
      },
      {
        type: '数理吉凶',
        description: this.getWugeJixiong(wuge.list)
      },
      {
        type: '三才吉凶',
        description: this.getSancaiJixiong(sancai)
      },
      {
        type: '八字吉凶',
        description: this.getBaziJixiong()
      }
    ]
  }

  // 生成建议列表
  generateAdviceList() {
    return [
      {
        title: '姓名用字',
        content: this.getNameAdvice()
      },
      {
        title: '五行调节',
        content: this.getWuxingAdvice()
      },
      {
        title: '事业发展',
        content: this.getCareerAdvice()
      },
      {
        title: '健康运势',
        content: this.getHealthAdvice()
      }
    ]
  }

  // 工具方法
  calculateTotalStrokes() {
    let total = 0
    for (const char of this.surname) {
      total += STROKE_DATA[char] || 0
    }
    for (const char of this.givenName) {
      total += STROKE_DATA[char] || 0
    }
    return total
  }

  calculateTianGe() {
    return this.surname.length === 1 ? 
      STROKE_DATA[this.surname] + 1 :
      STROKE_DATA[this.surname[0]] + STROKE_DATA[this.surname[1]]
  }

  calculateRenGe() {
    return this.surname.length === 1 ?
      STROKE_DATA[this.surname] + STROKE_DATA[this.givenName[0]] :
      STROKE_DATA[this.surname[1]] + STROKE_DATA[this.givenName[0]]
  }

  calculateDiGe() {
    return this.givenName.length === 1 ?
      STROKE_DATA[this.givenName[0]] + 1 :
      STROKE_DATA[this.givenName[0]] + STROKE_DATA[this.givenName[1]]
  }

  getWugeDescription(number) {
    const normalized = number % 10 || 10
    return WUGE_DATA[normalized]?.description || '普通数理'
  }

  calculateWugeScore(wugeList) {
    let score = 60 // 基础分
    
    // 计算吉数的数量
    const luckyCount = wugeList.filter(item => 
      WUGE_DATA[item.number % 10 || 10]?.luck === '吉'
    ).length

    // 根据吉数数量加分
    score += luckyCount * 8

    return Math.min(100, score)
  }

  getWuxingAttribute(number) {
    const normalized = number % 10 || 10
    return WUGE_DATA[normalized]?.element || '土'
  }

  calculateAttributeStrength(element) {
    let strength = 60
    
    // 根据八字配合加分
    if (this.isWuxingHelpful(element, this.getRiZhuWuxing(this.options.bazi))) {
      strength += 20
    }
    
    // 根据性别特征加分
    if (this.isElementSuitableForGender(element)) {
      strength += 10
    }

    return Math.min(100, strength)
  }

  getMainWuxing() {
    const renGe = this.calculateRenGe()
    return this.getWuxingAttribute(renGe)
  }

  getRiZhuWuxing(bazi) {
    return bazi.day.ganWuxing
  }

  getDaYunWuxing(bazi) {
    return bazi.currentDayun.ganWuxing
  }

  isWuxingHelpful(wuxing1, wuxing2) {
    const relations = {
      '金': ['土'],
      '木': ['水'],
      '水': ['金'],
      '火': ['木'],
      '土': ['火']
    }
    return relations[wuxing1]?.includes(wuxing2) || relations[wuxing2]?.includes(wuxing1)
  }

  isWuxingConflict(wuxing1, wuxing2) {
    const conflicts = {
      '金': ['火'],
      '木': ['金'],
      '水': ['土'],
      '火': ['水'],
      '土': ['木']
    }
    return conflicts[wuxing1]?.includes(wuxing2)
  }

  isElementSuitableForGender(element) {
    const suitableElements = {
      'male': ['金', '火'],
      'female': ['木', '水']
    }
    return suitableElements[this.options.gender]?.includes(element)
  }

  getScoreDescription(score) {
    if (score >= 90) return '大吉大利，前程似锦'
    if (score >= 80) return '吉祥如意，福运绵长'
    if (score >= 70) return '平安顺遂，稳步发展'
    if (score >= 60) return '平平稳稳，中规中矩'
    return '略有不足，需要改善'
  }

  getNameJixiong() {
    const score = this.calculateWuge().score
    if (score >= 90) return '姓名大吉，有助于人生发展，利于事业成就。'
    if (score >= 80) return '姓名吉利，对运势有积极影响，发展顺遂。'
    if (score >= 70) return '姓名尚可，运势平稳，宜守不宜进。'
    if (score >= 60) return '姓名平平，需要在为人处事上多加努力。'
    return '姓名有所不足，建议考虑改名以改善运势。'
  }

  getWugeJixiong(wugeList) {
    const luckyCount = wugeList.filter(item => 
      WUGE_DATA[item.number % 10 || 10]?.luck === '吉'
    ).length

    if (luckyCount >= 4) return '五格数理大吉，有助于各方面发展。'
    if (luckyCount >= 3) return '五格数理较好，运势发展平稳。'
    if (luckyCount >= 2) return '五格数理一般，需要在行事上多加谨慎。'
    return '五格数理欠佳，建议通过改名来改善。'
  }

  getSancaiJixiong(sancai) {
    const { score } = sancai
    if (score >= 90) return '三才配置极佳，有助于事业发展和人际关系。'
    if (score >= 80) return '三才配置良好，利于个人发展和家庭和睦。'
    if (score >= 70) return '三才配置尚可，生活工作平稳发展。'
    return '三才配置有所不足，需要在为人处事上多加注意。'
  }

  getBaziJixiong() {
    const { bazi } = this.options
    const nameWuxing = this.getMainWuxing()
    const riZhu = this.getRiZhuWuxing(bazi)
    
    if (this.isWuxingHelpful(nameWuxing, riZhu)) {
      return '姓名与八字相生，有助于运势发展，利于成就事业。'
    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {
      return '姓名与八字相克，需要注意趋吉避凶，谨慎行事。'
    }
    return '姓名与八字关系平和，运势发展平稳。'
  }

  getNameAdvice() {
    const nameWuxing = this.getMainWuxing()
    const riZhu = this.getRiZhuWuxing(this.options.bazi)
    
    if (this.isWuxingHelpful(nameWuxing, riZhu)) {
      return '现有姓名用字不错，建议保持。'
    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {
      return '建议改用与八字相生的字，可以选择五行属性为' + 
             this.getSuitableElements(riZhu).join('、') + '的字。'
    }
    return '姓名用字中规中矩，如果想要改善运势，可以考虑改用更吉利的字。'
  }

  getWuxingAdvice() {
    const { bazi } = this.options
    const nameWuxing = this.getMainWuxing()
    const riZhu = this.getRiZhuWuxing(bazi)
    const daYun = this.getDaYunWuxing(bazi)
    
    let advice = '从五行配置来看，'
    if (this.isWuxingHelpful(nameWuxing, riZhu)) {
      advice += '姓名与八字相生，可以多用' + nameWuxing + '性物品来增强运势。'
    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {
      advice += '需要注意调和五行，可以使用' + this.getSuitableElements(riZhu)[0] + '性物品来化解。'
    }
    
    if (this.isWuxingHelpful(nameWuxing, daYun)) {
      advice += '当前大运有利于发展，把握机会。'
    } else {
      advice += '当前大运需要谨慎行事，稳中求进。'
    }
    
    return advice
  }

  getCareerAdvice() {
    const nameWuxing = this.getMainWuxing()
    const careers = {
      '金': '适合从事金融、珠宝、机械等行业',
      '木': '适合从事教育、文化、艺术等行业',
      '水': '适合从事贸易、传媒、运输等行业',
      '火': '适合从事科技、餐饮、娱乐等行业',
      '土': '适合从事房地产、农业、建筑等行业'
    }
    
    return `从五行属性来看，${careers[nameWuxing]}。建议在事业选择上多考虑这些方向。`
  }

  getHealthAdvice() {
    const nameWuxing = this.getMainWuxing()
    const health = {
      '金': '注意呼吸系统和皮肤的保养',
      '木': '注意肝胆系统的调养',
      '水': '注意泌尿系统的保健',
      '火': '注意心脑血管的养护',
      '土': '注意消化系统的调理'
    }
    
    return `从健康角度考虑，${health[nameWuxing]}。平时可以多进行相关保健。`
  }

  getSuitableElements(element) {
    const relations = {
      '金': ['土', '金'],
      '木': ['水', '木'],
      '水': ['金', '水'],
      '火': ['木', '火'],
      '土': ['火', '土']
    }
    return relations[element] || ['土']
  }
}

module.exports = {
  XingmingCalculator
} 