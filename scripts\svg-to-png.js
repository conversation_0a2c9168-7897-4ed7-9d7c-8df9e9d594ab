const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// 安装必要的依赖
const installDependencies = () => {
    return new Promise((resolve, reject) => {
        exec('npm install --save-dev puppeteer', (error) => {
            if (error) {
                reject(error);
                return;
            }
            resolve();
        });
    });
};

// 使用Puppeteer将SVG转换为PNG
const convertSvgToPng = async () => {
    const puppeteer = require('puppeteer');
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    const iconDir = path.join(__dirname, '../assets/icons/new');
    const files = fs.readdirSync(iconDir);

    for (const file of files) {
        if (file.endsWith('.svg')) {
            const svgPath = path.join(iconDir, file);
            const pngPath = svgPath.replace('.svg', '.png');
            const svgContent = fs.readFileSync(svgPath, 'utf8');

            // 设置视口大小为81x81
            await page.setViewport({ width: 81, height: 81 });
            
            // 将SVG内容注入到页面
            await page.setContent(svgContent);

            // 截图并保存为PNG
            await page.screenshot({
                path: pngPath,
                clip: { x: 0, y: 0, width: 81, height: 81 }
            });

            console.log(`已转换: ${file} -> ${file.replace('.svg', '.png')}`);
        }
    }

    await browser.close();
};

// 主函数
async function main() {
    try {
        await installDependencies();
        await convertSvgToPng();
        console.log('所有图标转换完成！');
    } catch (error) {
        console.error('转换过程中出错:', error);
    }
}

main(); 