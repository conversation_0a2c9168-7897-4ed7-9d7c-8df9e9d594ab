// 网络请求工具
import { getStorageSync, setStorageSync, removeStorageSync, showToast } from './wx'
import { refreshToken } from '../api/auth'

// 请求基础配置
const BASE_URL = 'https://api.gualiankun.com' // 替换为实际的API地址
const TIMEOUT = 10000

// 请求拦截器
class RequestInterceptor {
  constructor() {
    this.requestQueue = new Map()
    this.isRefreshing = false
    this.refreshSubscribers = []
  }

  /**
   * 添加认证头
   */
  addAuthHeader(options) {
    const token = getStorageSync('token')
    if (token) {
      options.header = {
        ...options.header,
        'Authorization': `Bearer ${token}`
      }
    }
    return options
  }

  /**
   * 处理响应
   */
  async handleResponse(response, originalOptions) {
    const { statusCode, data } = response

    // HTTP状态码检查
    if (statusCode >= 200 && statusCode < 300) {
      // 业务状态码检查
      if (data.status === 'success') {
        return data
      } else if (data.status === 'error') {
        // 处理业务错误
        if (data.error_code === '1001' || data.error_code === '1002') {
          // Token过期或无效，尝试刷新
          return this.handleTokenExpired(originalOptions)
        } else {
          throw new Error(data.message || '请求失败')
        }
      } else {
        throw new Error('响应格式错误')
      }
    } else if (statusCode === 401) {
      // 未授权，尝试刷新Token
      return this.handleTokenExpired(originalOptions)
    } else {
      throw new Error(`网络错误: ${statusCode}`)
    }
  }

  /**
   * 处理Token过期
   */
  async handleTokenExpired(originalOptions) {
    if (this.isRefreshing) {
      // 如果正在刷新，将请求加入队列
      return new Promise((resolve, reject) => {
        this.refreshSubscribers.push({ resolve, reject, options: originalOptions })
      })
    }

    this.isRefreshing = true

    try {
      const refreshTokenValue = getStorageSync('refreshToken')
      if (!refreshTokenValue) {
        throw new Error('无刷新Token')
      }

      // 刷新Token
      const result = await refreshToken(refreshTokenValue)
      
      if (result.status === 'success') {
        // 保存新Token
        setStorageSync('token', result.data.token)
        setStorageSync('refreshToken', result.data.refresh_token)

        // 重试原请求
        const retryResult = await this.request(originalOptions)

        // 处理队列中的请求
        this.refreshSubscribers.forEach(({ resolve, options }) => {
          resolve(this.request(options))
        })

        return retryResult
      } else {
        throw new Error('刷新Token失败')
      }
    } catch (error) {
      // 刷新失败，清除登录信息并跳转到登录页
      this.clearAuthAndRedirect()
      
      // 拒绝队列中的请求
      this.refreshSubscribers.forEach(({ reject }) => {
        reject(error)
      })
      
      throw error
    } finally {
      this.isRefreshing = false
      this.refreshSubscribers = []
    }
  }

  /**
   * 清除认证信息并跳转登录
   */
  clearAuthAndRedirect() {
    removeStorageSync('token')
    removeStorageSync('refreshToken')
    removeStorageSync('userInfo')
    
    showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })

    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/index'
      })
    }, 1500)
  }

  /**
   * 发起请求
   */
  async request(options) {
    // 生成请求ID用于去重
    const requestId = this.generateRequestId(options)
    
    // 检查是否有相同的请求正在进行
    if (this.requestQueue.has(requestId)) {
      return this.requestQueue.get(requestId)
    }

    // 添加基础配置
    const requestOptions = {
      url: `${BASE_URL}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || TIMEOUT,
      ...options
    }

    // 添加认证头
    this.addAuthHeader(requestOptions)

    // 创建请求Promise
    const requestPromise = new Promise((resolve, reject) => {
      wx.request({
        ...requestOptions,
        success: async (response) => {
          try {
            const result = await this.handleResponse(response, options)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          console.error('请求失败:', error)
          reject(new Error(error.errMsg || '网络请求失败'))
        },
        complete: () => {
          // 请求完成后从队列中移除
          this.requestQueue.delete(requestId)
        }
      })
    })

    // 将请求添加到队列
    this.requestQueue.set(requestId, requestPromise)

    return requestPromise
  }

  /**
   * 生成请求ID
   */
  generateRequestId(options) {
    const { url, method = 'GET', data } = options
    const dataStr = data ? JSON.stringify(data) : ''
    return `${method}_${url}_${dataStr}`
  }
}

// 创建请求实例
const requestInterceptor = new RequestInterceptor()

/**
 * 通用请求方法
 * @param {Object} options 请求配置
 */
export const request = (options) => {
  return requestInterceptor.request(options)
}

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} params 请求参数
 * @param {Object} config 请求配置
 */
export const get = (url, params = {}, config = {}) => {
  return request({
    url,
    method: 'GET',
    data: params,
    ...config
  })
}

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} config 请求配置
 */
export const post = (url, data = {}, config = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...config
  })
}

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} config 请求配置
 */
export const put = (url, data = {}, config = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} config 请求配置
 */
export const del = (url, config = {}) => {
  return request({
    url,
    method: 'DELETE',
    ...config
  })
}

/**
 * 文件上传
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {Object} formData 表单数据
 * @param {Object} config 配置
 */
export const upload = (url, filePath, formData = {}, config = {}) => {
  const token = getStorageSync('token')
  
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${BASE_URL}${url}`,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        ...config.header
      },
      success: (response) => {
        try {
          const data = JSON.parse(response.data)
          if (data.status === 'success') {
            resolve(data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      },
      fail: (error) => {
        reject(new Error(error.errMsg || '上传失败'))
      }
    })
  })
}

export default {
  request,
  get,
  post,
  put,
  del,
  upload
}
