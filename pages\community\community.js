// pages/community/community.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchKeyword: '',
    currentCategory: 'all',
    categories: [
      { name: '全部', value: 'all' },
      { name: '紫微斗数', value: 'ziwei' },
      { name: '易经', value: 'yijing' },
      { name: '风水', value: 'fengshui' },
      { name: '八字', value: 'bazi' },
      { name: '运势', value: 'fortune' }
    ],
    publishCategories: [
      { name: '紫微斗数', value: 'ziwei' },
      { name: '易经', value: 'yijing' },
      { name: '风水', value: 'fengshui' },
      { name: '八字', value: 'bazi' },
      { name: '运势', value: 'fortune' }
    ],
    posts: [],
    showPublishModal: false,
    newPost: {
      title: '',
      content: '',
      images: [],
      categoryIndex: 0,
      tag: 'discussion'
    },
    isLoading: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,
    isCloudInited: false,
    showReplyModal: false,
    currentComment: null,
    sortType: 'latest', // latest, hot, recommended
    userFavorites: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initCloud()
    this.loadUserFavorites()
  },

  async initCloud() {
    if (!wx.cloud) {
      wx.showToast({
        title: '请使用 2.2.3 或以上的基础库以使用云能力',
        icon: 'none'
      })
      return
    }

    try {
      await wx.cloud.init({
        env: 'cloud1-7gnaonfy0429a3eb',
        traceUser: true
      })
      
      this.setData({ isCloudInited: true }, () => {
        this.loadPosts()
      })
    } catch (error) {
      console.error('云开发初始化失败:', error)
      wx.showToast({
        title: '系统初始化失败',
        icon: 'none'
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (!this.data.isCloudInited) {
      wx.stopPullDownRefresh()
      return
    }

    this.setData({
      posts: [],
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadPosts().then(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading && this.data.isCloudInited) {
      this.loadPosts()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(e) {
    if (e.from === 'button') {
      const { post } = e.target.dataset
      return {
        title: post.title,
        path: `/pages/post-detail/post-detail?id=${post.id}`,
        imageUrl: post.images?.[0] // 使用帖子第一张图片作为分享图片
      }
    }
    return {
      title: '玄学社区',
      path: '/pages/community/community'
    }
  },

  /**
   * 加载帖子列表
   */
  async loadPosts() {
    if (this.data.isLoading || !this.data.isCloudInited) return
    
    this.setData({ isLoading: true })
    
    try {
      const { currentCategory, searchKeyword, pageNum, pageSize, sortType } = this.data
      const db = wx.cloud.database()
      const _ = db.command
      
      // 构建查询条件
      const query = {}
      if (currentCategory !== 'all') {
        query.category = currentCategory
      }
      if (searchKeyword) {
        query.title = db.RegExp({
          regexp: searchKeyword,
          options: 'i'
        })
      }
      
      // 构建排序条件
      let orderField = 'createTime'
      let orderDirection = 'desc'
      
      switch (sortType) {
        case 'hot':
          orderField = 'likes'
          break
        case 'recommended':
          orderField = 'views'
          break
        default:
          orderField = 'createTime'
      }
      
      // 查询数据
      const res = await Promise.all([
        db.collection('posts')
          .where(query)
          .orderBy(orderField, orderDirection)
          .skip((pageNum - 1) * pageSize)
          .limit(pageSize)
          .get(),
        db.collection('posts').where(query).count()
      ])
      
      const [postsRes, countRes] = res
      
      // 处理数据
      const newPosts = postsRes.data.map(post => ({
        ...post,
        id: post._id,
        username: post.author?.nickname || '匿名用户',
        avatar: post.author?.avatar || '/assets/images/default-avatar.png',
        categoryName: this.getCategoryName(post.category),
        createTime: this.formatTime(post.createTime),
        isFavorite: this.data.userFavorites.includes(post._id)
      }))
      
      const hasMore = this.data.posts.length + newPosts.length < countRes.total
      
      this.setData({
        posts: [...this.data.posts, ...newPosts],
        pageNum: this.data.pageNum + 1,
        hasMore,
        isLoading: false
      })
    } catch (error) {
      console.error('加载帖子失败:', error)
      this.setData({ isLoading: false })
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  getCategoryName(value) {
    const category = this.data.categories.find(item => item.value === value)
    return category ? category.name : '其他'
  },

  formatTime(timestamp) {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  /**
   * 搜索帖子
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
      posts: [],
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadPosts()
    })
  },

  /**
   * 切换分类
   */
  switchCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category,
      posts: [],
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadPosts()
    })
  },

  /**
   * 显示发布模态框
   */
  showPublishModal() {
    if (!this.data.isCloudInited) {
      wx.showToast({
        title: '系统未初始化，请稍后再试',
        icon: 'none'
      })
      return
    }
    this.setData({ showPublishModal: true })
  },

  /**
   * 隐藏发布模态框
   */
  hidePublishModal() {
    this.setData({ showPublishModal: false })
  },

  /**
   * 输入标题
   */
  onTitleInput(e) {
    this.setData({
      'newPost.title': e.detail.value
    })
  },

  /**
   * 输入内容
   */
  onContentInput(e) {
    this.setData({
      'newPost.content': e.detail.value
    })
  },

  /**
   * 选择分类
   */
  onCategoryChange(e) {
    this.setData({
      'newPost.categoryIndex': parseInt(e.detail.value)
    })
  },

  /**
   * 选择图片
   */
  async chooseImage() {
    try {
      const res = await wx.chooseImage({
        count: 9 - this.data.newPost.images.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })
      
      this.setData({
        'newPost.images': [...this.data.newPost.images, ...res.tempFilePaths]
      })
    } catch (error) {
      console.error('选择图片失败:', error)
    }
  },

  /**
   * 删除图片
   */
  deleteImage(e) {
    const index = e.currentTarget.dataset.index
    const images = [...this.data.newPost.images]
    images.splice(index, 1)
    this.setData({
      'newPost.images': images
    })
  },

  /**
   * 上传图片到云存储
   */
  async uploadImages(images) {
    if (!images || images.length === 0) return []
    
    const uploadTasks = images.map(filePath => {
      return wx.cloud.uploadFile({
        cloudPath: `posts/${Date.now()}-${Math.floor(Math.random() * 1000)}.${filePath.match(/\.(\w+)$/)[1]}`,
        filePath
      })
    })
    
    try {
      const results = await Promise.all(uploadTasks)
      return results.map(res => res.fileID)
    } catch (error) {
      console.error('上传图片失败:', error)
      wx.showToast({
        title: '图片上传失败',
        icon: 'none'
      })
      return []
    }
  },

  /**
   * 发布帖子到云数据库
   */
  async publishPost() {
    const { title, content, images, categoryIndex, tag } = this.data.newPost
    const category = this.data.categories[categoryIndex]
    
    if (!title.trim()) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return
    }
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '发布中...',
      mask: true
    })
    
    try {
      // 上传图片到云存储
      const fileIDs = await this.uploadImages(images)
      
      // 获取用户信息
      const userInfo = await wx.cloud.callFunction({
        name: 'getUserInfo'
      }).catch(() => ({ result: { openid: 'anonymous' } }))
      
      // 创建帖子数据
      const postData = {
        title: title.trim(),
        content: content.trim(),
        images: fileIDs,
        category: category.value,
        categoryName: category.name,
        tag: tag,
        createTime: new Date(),
        updateTime: new Date(),
        likes: 0,
        comments: 0,
        views: 0,
        author: {
          openid: userInfo.result.openid,
          nickname: '匿名用户',
          avatar: '/assets/images/default-avatar.png'
        }
      }
      
      // 添加到云数据库
      const db = wx.cloud.database()
      const result = await db.collection('posts').add({
        data: postData
      })
      
      // 创建新帖子对象用于本地显示
      const newPost = {
        id: result._id,
        username: postData.author.nickname,
        avatar: postData.author.avatar,
        title: postData.title,
        content: postData.content,
        images: postData.images,
        category: postData.category,
        categoryName: postData.categoryName,
        createTime: postData.createTime.toLocaleString(),
        likes: postData.likes,
        comments: postData.comments,
        tag: postData.tag
      }
      
      // 更新本地数据
      this.setData({
        posts: [newPost, ...this.data.posts],
        showPublishModal: false
      })
      
      wx.hideLoading()
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('发布帖子失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 查看帖子详情
   */
  viewPost(e) {
    const postId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    })
  },

  previewImage(e) {
    const { urls, current } = e.currentTarget.dataset
    wx.previewImage({
      urls,
      current
    })
  },

  // 选择标签
  selectTag(e) {
    const tag = e.currentTarget.dataset.tag
    this.setData({
      'newPost.tag': tag
    })
  },

  // 加载用户收藏
  async loadUserFavorites() {
    try {
      const db = wx.cloud.database()
      const res = await db.collection('user_favorites').where({
        _openid: app.globalData.openid
      }).get()
      
      this.setData({
        userFavorites: res.data.map(item => item.postId)
      })
    } catch (error) {
      console.error('加载收藏失败:', error)
    }
  },

  // 收藏/取消收藏帖子
  async toggleFavorite(e) {
    const { id } = e.currentTarget.dataset
    const db = wx.cloud.database()
    
    try {
      if (this.data.userFavorites.includes(id)) {
        // 取消收藏
        await db.collection('user_favorites').where({
          postId: id,
          _openid: app.globalData.openid
        }).remove()
        
        this.setData({
          userFavorites: this.data.userFavorites.filter(item => item !== id)
        })
        
        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        })
      } else {
        // 添加收藏
        await db.collection('user_favorites').add({
          data: {
            postId: id,
            createTime: new Date()
          }
        })
        
        this.setData({
          userFavorites: [...this.data.userFavorites, id]
        })
        
        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('操作收藏失败:', error)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示评论回复框
  showReplyModal(e) {
    const { comment } = e.currentTarget.dataset
    this.setData({
      showReplyModal: true,
      currentComment: comment
    })
  },

  // 隐藏评论回复框
  hideReplyModal() {
    this.setData({
      showReplyModal: false,
      currentComment: null
    })
  },

  // 提交评论回复
  async submitReply(e) {
    const { content } = e.detail.value
    const { currentComment } = this.data
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入回复内容',
        icon: 'none'
      })
      return
    }
    
    try {
      const db = wx.cloud.database()
      await db.collection('comments').add({
        data: {
          postId: currentComment.postId,
          content: content.trim(),
          parentId: currentComment._id,
          createTime: new Date(),
          author: {
            openid: app.globalData.openid,
            nickname: app.globalData.userInfo?.nickName || '匿名用户',
            avatar: app.globalData.userInfo?.avatarUrl || '/assets/images/default-avatar.png'
          }
        }
      })
      
      this.hideReplyModal()
      this.loadPosts() // 重新加载帖子列表以更新评论
      
      wx.showToast({
        title: '回复成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('提交回复失败:', error)
      wx.showToast({
        title: '回复失败，请重试',
        icon: 'none'
      })
    }
  },

  // 切换排序方式
  changeSortType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      sortType: type,
      posts: [],
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadPosts()
    })
  }
})