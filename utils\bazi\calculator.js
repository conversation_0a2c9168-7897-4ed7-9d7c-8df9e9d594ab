// 天干
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

// 地支
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 五行属性
const WUXING = {
  metal: { name: '金', value: 0 },
  wood: { name: '木', value: 0 },
  water: { name: '水', value: 0 },
  fire: { name: '火', value: 0 },
  earth: { name: '土', value: 0 }
};

// 天干五行对应
const STEM_WUXING = {
  '甲': 'wood', '乙': 'wood',
  '丙': 'fire', '丁': 'fire',
  '戊': 'earth', '己': 'earth',
  '庚': 'metal', '辛': 'metal',
  '壬': 'water', '癸': 'water'
};

// 地支五行对应
const BRANCH_WUXING = {
  '子': 'water', '丑': 'earth',
  '寅': 'wood', '卯': 'wood',
  '辰': 'earth', '巳': 'fire',
  '午': 'fire', '未': 'earth',
  '申': 'metal', '酉': 'metal',
  '戌': 'earth', '亥': 'water'
};

class BaziCalculator {
  constructor() {
    this.heavenlyStems = HEAVENLY_STEMS;
    this.earthlyBranches = EARTHLY_BRANCHES;
  }

  async calculate(params) {
    const { birthDate, birthTime, gender } = params;

    try {
      // 解析出生日期
      const date = new Date(birthDate + ' ' + birthTime);
      
      // 计算八字
      const result = {
        yearStem: this.calculateYearStem(date),
        yearBranch: this.calculateYearBranch(date),
        monthStem: this.calculateMonthStem(date),
        monthBranch: this.calculateMonthBranch(date),
        dayStem: this.calculateDayStem(date),
        dayBranch: this.calculateDayBranch(date),
        hourStem: this.calculateHourStem(date),
        hourBranch: this.calculateHourBranch(date)
      };

      // 计算五行分布
      const wuxing = this.calculateWuxing(result);

      // 生成运势解读
      const interpretation = this.generateInterpretation(result, wuxing, gender);

      // 计算运势评分
      const fortune = this.calculateFortune(result, wuxing, gender);

      return {
        ...result,
        wuxing,
        interpretation,
        fortune
      };
    } catch (error) {
      console.error('八字计算错误:', error);
      throw new Error('八字计算失败');
    }
  }

  calculateYearStem(date) {
    const year = date.getFullYear();
    const offset = (year - 4) % 10;
    return this.heavenlyStems[offset];
  }

  calculateYearBranch(date) {
    const year = date.getFullYear();
    const offset = (year - 4) % 12;
    return this.earthlyBranches[offset];
  }

  calculateMonthStem(date) {
    const yearStemIndex = this.heavenlyStems.indexOf(this.calculateYearStem(date));
    const month = date.getMonth() + 1;
    const offset = (yearStemIndex * 2 + month) % 10;
    return this.heavenlyStems[offset];
  }

  calculateMonthBranch(date) {
    const month = date.getMonth() + 1;
    return this.earthlyBranches[month - 1];
  }

  calculateDayStem(date) {
    const baseDate = new Date('1900-01-01');
    const days = Math.floor((date - baseDate) / (24 * 60 * 60 * 1000));
    const offset = (days + 10) % 10;
    return this.heavenlyStems[offset];
  }

  calculateDayBranch(date) {
    const baseDate = new Date('1900-01-01');
    const days = Math.floor((date - baseDate) / (24 * 60 * 60 * 1000));
    const offset = (days + 12) % 12;
    return this.earthlyBranches[offset];
  }

  calculateHourStem(date) {
    const dayStemIndex = this.heavenlyStems.indexOf(this.calculateDayStem(date));
    const hour = date.getHours();
    const offset = (dayStemIndex * 2 + Math.floor(hour / 2)) % 10;
    return this.heavenlyStems[offset];
  }

  calculateHourBranch(date) {
    const hour = date.getHours();
    const offset = Math.floor((hour + 1) / 2) % 12;
    return this.earthlyBranches[offset];
  }

  calculateWuxing(bazi) {
    const wuxing = { ...WUXING };
    
    // 计算天干五行
    [bazi.yearStem, bazi.monthStem, bazi.dayStem, bazi.hourStem].forEach(stem => {
      wuxing[STEM_WUXING[stem]].value += 25;
    });

    // 计算地支五行
    [bazi.yearBranch, bazi.monthBranch, bazi.dayBranch, bazi.hourBranch].forEach(branch => {
      wuxing[BRANCH_WUXING[branch]].value += 25;
    });

    return {
      metal: wuxing.metal.value,
      wood: wuxing.wood.value,
      water: wuxing.water.value,
      fire: wuxing.fire.value,
      earth: wuxing.earth.value
    };
  }

  generateInterpretation(bazi, wuxing, gender) {
    // 这里可以根据八字和五行分布生成详细的命理解读
    // 这是一个简化版本的示例
    let interpretation = '根据您的八字命盘分析：\n\n';

    // 分析日主强弱
    const dayMaster = STEM_WUXING[bazi.dayStem];
    const dayMasterStrength = wuxing[dayMaster];
    
    if (dayMasterStrength > 150) {
      interpretation += '您的日主偏强，显示出较强的个性和领导能力。建议在事业上可以选择自主创业或管理岗位。\n\n';
    } else if (dayMasterStrength < 100) {
      interpretation += '您的日主偏弱，性格较为温和，善于协调人际关系。建议在事业上可以选择服务业或辅助性工作。\n\n';
    } else {
      interpretation += '您的日主中和，性格平衡，适应能力强。可以在多个领域都有不错的发展。\n\n';
    }

    // 分析五行特点
    const strongestElement = Object.entries(wuxing).reduce((a, b) => b[1] > a[1] ? b : a);
    interpretation += `您的命盘中${this.getWuxingName(strongestElement[0])}最旺，`;
    
    switch (strongestElement[0]) {
      case 'metal':
        interpretation += '意味着您做事果断，重视纪律，适合从事金融、IT、军警等行业。\n';
        break;
      case 'wood':
        interpretation += '表示您富有创造力，善于沟通，适合从事教育、文化、艺术等行业。\n';
        break;
      case 'water':
        interpretation += '显示您思维灵活，适应力强，适合从事科研、传媒、销售等行业。\n';
        break;
      case 'fire':
        interpretation += '代表您热情开朗，领导能力强，适合从事管理、营销、演艺等行业。\n';
        break;
      case 'earth':
        interpretation += '意味着您稳重踏实，责任心强，适合从事房地产、农业、服务业等行业。\n';
        break;
    }

    return interpretation;
  }

  calculateFortune(bazi, wuxing, gender) {
    // 这里可以根据八字和五行分布计算各方面运势
    // 这是一个简化版本的示例
    const fortune = {
      career: 0,
      wealth: 0,
      love: 0,
      health: 0
    };

    // 计算事业运
    const dayMaster = STEM_WUXING[bazi.dayStem];
    const dayMasterStrength = wuxing[dayMaster];
    fortune.career = Math.min(100, dayMasterStrength);

    // 计算财运
    const wealthElement = this.getWealthElement(dayMaster);
    fortune.wealth = Math.min(100, wuxing[wealthElement]);

    // 计算感情运
    const loveElement = this.getLoveElement(dayMaster, gender);
    fortune.love = Math.min(100, wuxing[loveElement]);

    // 计算健康运
    fortune.health = Math.min(100, (dayMasterStrength + wuxing[this.getHealthElement(dayMaster)]) / 2);

    return fortune;
  }

  getWuxingName(element) {
    const names = {
      metal: '金',
      wood: '木',
      water: '水',
      fire: '火',
      earth: '土'
    };
    return names[element];
  }

  getWealthElement(dayMaster) {
    const wealthMap = {
      metal: 'water',
      water: 'wood',
      wood: 'fire',
      fire: 'earth',
      earth: 'metal'
    };
    return wealthMap[dayMaster];
  }

  getLoveElement(dayMaster, gender) {
    if (gender === 'male') {
      const loveMap = {
        metal: 'earth',
        earth: 'fire',
        fire: 'wood',
        wood: 'water',
        water: 'metal'
      };
      return loveMap[dayMaster];
    } else {
      const loveMap = {
        metal: 'water',
        water: 'wood',
        wood: 'fire',
        fire: 'earth',
        earth: 'metal'
      };
      return loveMap[dayMaster];
    }
  }

  getHealthElement(dayMaster) {
    const healthMap = {
      metal: 'earth',
      earth: 'water',
      water: 'wood',
      wood: 'fire',
      fire: 'metal'
    };
    return healthMap[dayMaster];
  }
}

module.exports = {
  BaziCalculator
}; 