/* components/birth-info/birth-info.wxss */
.birth-info-float {
  position: fixed;
  z-index: 999;
  transition: all 0.3s ease;
}

.float-container {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 50rpx 0 0 50rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.2);
  transition: all 0.3s ease;
  transform: translateX(calc(100% - 100rpx));
}

.float-container.expanded {
  transform: translateX(0);
  background: rgba(138, 43, 226, 0.1);
}

.float-content {
  display: flex;
  align-items: center;
  min-width: 100rpx;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-right: 12rpx solid #8a2be2;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
  transition: transform 0.3s;
  margin-right: 16rpx;
  cursor: pointer;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

.float-text {
  font-size: 28rpx;
  color: #8a2be2;
  white-space: nowrap;
  cursor: pointer;
  font-weight: 500;
}

.float-text:active,
.arrow-icon:active {
  opacity: 0.7;
}

/* 隐藏原有的输入表单 */
.input-form {
  display: none;
}

.birth-info.collapsed {
  width: 200rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  border-radius: 12rpx 12rpx 0 0;
}

.collapse-icon {
  width: 32rpx;
  height: 32rpx;
}

.content {
  padding: 20rpx;
  background-color: var(--card-background);
  border-radius: 0 0 12rpx 12rpx;
  border: 2rpx solid var(--border-color);
  border-top: none;
}

.info-item {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.value {
  font-size: 28rpx;
  color: var(--text-primary);
}

.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio {
  font-size: 28rpx;
  color: var(--text-primary);
}

.save-btn {
  margin-top: 20rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 10rpx 0;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

.birth-info-container {
  padding: 20rpx;
  background-color: #fff;
  transition: all 0.3s ease;
}

.birth-info-container.hidden {
  display: none;
}

.form-group {
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.picker {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.picker.placeholder {
  color: #999;
}

.button-group {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #8a2be2;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.save-button:active {
  opacity: 0.8;
} 