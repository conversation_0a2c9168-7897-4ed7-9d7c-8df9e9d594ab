const app = getApp()
const { BaziCalculator } = require('../../utils/bazi/calculator')
const { WuxingChart } = require('../../utils/charts')
const { HehunCalculator } = require('../../utils/hehun/calculator')
const { MarriageCalculator } = require('../../utils/hehun/calculator')

Page({
  data: {
    // 基本信息
    male: {
      name: '',
      birthDate: '',
      birthTime: ''
    },
    female: {
      name: '',
      birthDate: '',
      birthTime: ''
    },
    canSubmit: false,
    price: 38,

    // 结果展示
    showResult: false,
    matchScore: 0,
    matchDesc: '',
    maleBazi: '',
    femaleBazi: '',
    baziAnalysis: '',
    wuxingAnalysis: '',
    adviceList: [],
    maleBirthDate: '',
    maleBirthTime: '',
    femaleBirthDate: '',
    femaleBirthTime: '',
    loading: false,
    maleResult: {
      pillars: []
    },
    femaleResult: {
      pillars: []
    },
    marriageResult: {
      wuxing: [],
      score: 0,
      aspects: [],
      advice: ''
    }
  },

  onLoad() {
    // 初始化五行图表
    this.wuxingChart = new WuxingChart()
  },

  // 男方信息输入处理
  onMaleNameInput(e) {
    this.setData({
      'male.name': e.detail.value
    })
    this.checkCanSubmit()
  },

  onMaleDateChange(e) {
    this.setData({
      'male.birthDate': e.detail.value
    })
    this.checkCanSubmit()
  },

  onMaleTimeChange(e) {
    this.setData({
      'male.birthTime': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 女方信息输入处理
  onFemaleNameInput(e) {
    this.setData({
      'female.name': e.detail.value
    })
    this.checkCanSubmit()
  },

  onFemaleDateChange(e) {
    this.setData({
      'female.birthDate': e.detail.value
    })
    this.checkCanSubmit()
  },

  onFemaleTimeChange(e) {
    this.setData({
      'female.birthTime': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { male, female } = this.data
    const canSubmit = male.name && male.birthDate && male.birthTime &&
                     female.name && female.birthDate && female.birthTime
    this.setData({ canSubmit })
  },

  // 开始测算
  async onAnalyze() {
    if (!this.data.canSubmit) return

    wx.showLoading({ title: '正在测算...' })

    try {
      // 计算男女双方八字
      const maleCalculator = new BaziCalculator({
        name: this.data.male.name,
        gender: 'male',
        birthDate: this.data.male.birthDate,
        birthTime: this.data.male.birthTime
      })

      const femaleCalculator = new BaziCalculator({
        name: this.data.female.name,
        gender: 'female',
        birthDate: this.data.female.birthDate,
        birthTime: this.data.female.birthTime
      })

      const maleBazi = await maleCalculator.calculate()
      const femaleBazi = await femaleCalculator.calculate()

      // 合婚测算
      const hehunCalculator = new HehunCalculator(maleBazi, femaleBazi)
      const result = await hehunCalculator.calculate()

      // 更新结果
      this.setData({
        showResult: true,
        matchScore: result.score,
        matchDesc: result.description,
        maleBazi: result.maleBaziStr,
        femaleBazi: result.femaleBaziStr,
        baziAnalysis: result.baziAnalysis,
        wuxingAnalysis: result.wuxingAnalysis,
        adviceList: result.adviceList
      })

      // 绘制五行图表
      this.drawWuxingChart(result.wuxingData)

      wx.hideLoading()
    } catch (error) {
      console.error('测算失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '测算失败，请重试',
        icon: 'none'
      })
    }
  },

  // 绘制五行图表
  drawWuxingChart(data) {
    this.wuxingChart.draw('wuxingCanvas', data)
  },

  // 分享功能
  onShareAppMessage() {
    const { male, female, matchScore } = this.data
    return {
      title: `${male.name}和${female.name}的合婚测算结果：${matchScore}分`,
      path: '/pages/hehun/index',
      imageUrl: '/assets/images/share-cover.jpg'
    }
  },

  // 页面卸载
  onUnload() {
    // 清理图表实例
    if (this.wuxingChart) {
      this.wuxingChart.dispose()
    }
  },

  onMaleBirthDateChange(e) {
    this.setData({
      maleBirthDate: e.detail.value
    });
  },

  onMaleBirthTimeChange(e) {
    this.setData({
      maleBirthTime: e.detail.value
    });
  },

  onFemaleBirthDateChange(e) {
    this.setData({
      femaleBirthDate: e.detail.value
    });
  },

  onFemaleBirthTimeChange(e) {
    this.setData({
      femaleBirthTime: e.detail.value
    });
  },

  async analyzeMarriage() {
    if (!this.validateInput()) {
      return;
    }

    this.setData({ loading: true });

    try {
      const baziCalculator = new BaziCalculator();
      const marriageCalculator = new MarriageCalculator();

      // 计算男方八字
      const maleResult = await baziCalculator.calculate({
        birthDate: this.data.maleBirthDate,
        birthTime: this.data.maleBirthTime,
        gender: 'male'
      });

      // 计算女方八字
      const femaleResult = await baziCalculator.calculate({
        birthDate: this.data.femaleBirthDate,
        birthTime: this.data.femaleBirthTime,
        gender: 'female'
      });

      // 计算合婚结果
      const marriageResult = await marriageCalculator.calculate({
        male: maleResult,
        female: femaleResult
      });

      this.setData({
        loading: false,
        showResult: true,
        maleResult: {
          pillars: [
            { name: '年柱', heavenlyStem: maleResult.yearStem, earthlyBranch: maleResult.yearBranch },
            { name: '月柱', heavenlyStem: maleResult.monthStem, earthlyBranch: maleResult.monthBranch },
            { name: '日柱', heavenlyStem: maleResult.dayStem, earthlyBranch: maleResult.dayBranch },
            { name: '时柱', heavenlyStem: maleResult.hourStem, earthlyBranch: maleResult.hourBranch }
          ]
        },
        femaleResult: {
          pillars: [
            { name: '年柱', heavenlyStem: femaleResult.yearStem, earthlyBranch: femaleResult.yearBranch },
            { name: '月柱', heavenlyStem: femaleResult.monthStem, earthlyBranch: femaleResult.monthBranch },
            { name: '日柱', heavenlyStem: femaleResult.dayStem, earthlyBranch: femaleResult.dayBranch },
            { name: '时柱', heavenlyStem: femaleResult.hourStem, earthlyBranch: femaleResult.hourBranch }
          ]
        },
        marriageResult: {
          wuxing: [
            { element: '金', percentage: marriageResult.wuxing.metal, color: '#FFD700' },
            { element: '木', percentage: marriageResult.wuxing.wood, color: '#90EE90' },
            { element: '水', percentage: marriageResult.wuxing.water, color: '#87CEEB' },
            { element: '火', percentage: marriageResult.wuxing.fire, color: '#FF6B6B' },
            { element: '土', percentage: marriageResult.wuxing.earth, color: '#DEB887' }
          ],
          score: marriageResult.score,
          aspects: [
            { name: '性格相合', stars: this.getStars(marriageResult.aspects.personality) },
            { name: '事业相辅', stars: this.getStars(marriageResult.aspects.career) },
            { name: '生活和谐', stars: this.getStars(marriageResult.aspects.lifestyle) },
            { name: '感情基础', stars: this.getStars(marriageResult.aspects.emotion) }
          ],
          advice: marriageResult.advice
        }
      });
    } catch (error) {
      console.error('合婚分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  validateInput() {
    if (!this.data.maleBirthDate || !this.data.maleBirthTime) {
      wx.showToast({
        title: '请完善男方出生信息',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.femaleBirthDate || !this.data.femaleBirthTime) {
      wx.showToast({
        title: '请完善女方出生信息',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  getStars(value) {
    // 将0-100的分数转换为1-5颗星
    const starCount = Math.ceil(value / 20);
    return new Array(starCount).fill('★');
  }
}) 