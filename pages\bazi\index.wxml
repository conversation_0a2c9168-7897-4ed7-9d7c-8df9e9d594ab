<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <view class="header">
    <text class="title">八字分析</text>
    <text class="subtitle">精准解读人生命理</text>
  </view>

  <view class="birth-info">
    <view class="form-item">
      <text class="label">出生日期</text>
      <picker mode="date" value="{{birthDate}}" bindchange="onBirthDateChange">
        <view class="picker">{{birthDate || '请选择出生日期'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="label">出生时间</text>
      <picker mode="time" value="{{birthTime}}" bindchange="onBirthTimeChange">
        <view class="picker">{{birthTime || '请选择出生时间'}}</view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">性别</text>
      <radio-group bindchange="onGenderChange">
        <label class="radio">
          <radio value="male" checked="{{gender === 'male'}}"/>男
        </label>
        <label class="radio">
          <radio value="female" checked="{{gender === 'female'}}"/>女
        </label>
      </radio-group>
    </view>
  </view>

  <button class="analyze-btn" bindtap="analyzeBazi" loading="{{loading}}">
    开始分析
  </button>

  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="card-title">八字命盘</view>
      <view class="bazi-grid">
        <view class="grid-item" wx:for="{{baziResult.pillars}}" wx:key="index">
          <text class="pillar-name">{{item.name}}</text>
          <view class="pillar-content">
            <text>天干：{{item.heavenlyStem}}</text>
            <text>地支：{{item.earthlyBranch}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">五行分析</view>
      <view class="wuxing-analysis">
        <view class="wuxing-item" wx:for="{{baziResult.wuxing}}" wx:key="element">
          <text class="element-name">{{item.element}}</text>
          <progress percent="{{item.percentage}}" stroke-width="12" color="{{item.color}}"/>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">命理解读</view>
      <view class="interpretation">
        <text>{{baziResult.interpretation}}</text>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">运势分析</view>
      <view class="fortune-analysis">
        <view class="fortune-item" wx:for="{{baziResult.fortune}}" wx:key="aspect">
          <text class="aspect-name">{{item.aspect}}</text>
          <view class="stars">
            <text class="star" wx:for="{{item.stars}}" wx:key="index">★</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 