/* pages/bazi/index.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.birth-info {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.picker {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.radio-group {
  display: flex;
  gap: 30rpx;
}

.radio {
  font-size: 28rpx;
  color: #333;
  margin-right: 30rpx;
}

.analyze-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4a5568;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 40rpx 0;
}

.result-section {
  margin-top: 40rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.bazi-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.grid-item {
  text-align: center;
}

.pillar-name {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.pillar-content {
  background: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.pillar-content text {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.wuxing-analysis {
  margin-top: 20rpx;
}

.wuxing-item {
  margin-bottom: 20rpx;
}

.element-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.interpretation {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.fortune-analysis {
  margin-top: 20rpx;
}

.fortune-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.aspect-name {
  font-size: 28rpx;
  color: #333;
}

.stars {
  color: #f0b90b;
  font-size: 28rpx;
}

/* 顶部banner */
.banner {
  position: relative;
  width: 100%;
  height: 360rpx;
  overflow: hidden;
}

.banner-bg {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  left: 40rpx;
  bottom: 40rpx;
  color: #fff;
  z-index: 1;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 表单区域 */
.form-section {
  margin: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 性别选择器 */
.gender-picker {
  display: flex;
  gap: 20rpx;
}

.gender-option {
  flex: 1;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.gender-option.active {
  background: #6236FF;
  color: #fff;
}

.gender-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 日期时间选择器 */
.picker.placeholder {
  color: #999;
}

/* 农历阳历切换 */
.calendar-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 16rpx;
}

/* 提交按钮 */
.submit-section {
  margin: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #6236FF 0%, #9F6EFF 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.submit-btn.disabled {
  opacity: 0.6;
}

.price {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  font-weight: normal;
}

/* 分析结果区域 */
.result-section {
  margin: 30rpx;
}

/* 分析卡片通用样式 */
.analysis-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 12rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 八字排盘图 */
.bazi-chart {
  width: 100%;
  height: 400rpx;
  margin-bottom: 30rpx;
}

.bazi-canvas {
  width: 100%;
  height: 100%;
}

/* 五行分析 */
.wuxing-chart {
  width: 100%;
  height: 300rpx;
  margin-bottom: 24rpx;
}

.wuxing-details {
  margin-top: 24rpx;
}

.wuxing-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.wuxing-name {
  width: 80rpx;
  font-size: 28rpx;
  color: #666;
}

.wuxing-value {
  width: 60rpx;
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 命格分析 */
.mingge-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 运势分析 */
.yunshi-tabs {
  display: flex;
  margin-bottom: 24rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
}

.tab.active {
  background: #6236FF;
  color: #fff;
}

.yunshi-chart {
  width: 100%;
  height: 300rpx;
  margin-bottom: 24rpx;
}

.yunshi-details {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 吉凶分析 */
.jixiong-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.jixiong-item {
  background: #F8F9FD;
  border-radius: 12rpx;
  overflow: hidden;
}

.item-header {
  padding: 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
}

.item-header.ji {
  background: #4CAF50;
}

.item-header.xiong {
  background: #FF5252;
}

.item-content {
  padding: 16rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 建议指导 */
.advice-list {
  margin-top: 16rpx;
}

.advice-item {
  margin-bottom: 24rpx;
}

.advice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.advice-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 分享按钮 */
.share-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
}

.share-btn {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.share-icon {
  width: 36rpx;
  height: 36rpx;
}

.share-btn text {
  font-size: 28rpx;
  color: #666;
} 