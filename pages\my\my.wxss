/* pages/my/my.wxss */
page {
  background-color: var(--background-color) !important;
}

.container {
  padding: 24rpx;
  min-height: 100vh;
  background-color: var(--background-color) !important;
}

/* 用户信息区域 */
.user-info {
  background: var(--primary-color);
  padding: 40rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid var(--primary-lightest);
  margin-right: 24rpx;
}

.user-detail {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 28rpx;
  color: var(--primary-lightest);
}

/* 功能列表 */
.function-list {
  background: var(--card-background);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  opacity: 0.8;
}

.function-name {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary);
}

.arrow-right {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx 0;
  color: var(--text-light);
  font-size: 26rpx;
}

/* 退出登录按钮 */
.logout-btn {
  background: var(--primary-color);
  color: #FFFFFF;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 8rpx;
  margin: 48rpx 32rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 功能分组标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin: 32rpx 0 16rpx;
  padding-left: 16rpx;
  border-left: 8rpx solid var(--primary-color);
} 