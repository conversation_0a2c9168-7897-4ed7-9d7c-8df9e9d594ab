// 八卦基础数据
const trigrams = {
  qian: { name: '乾', nature: '天', attribute: '刚健' },
  kun: { name: '坤', nature: '地', attribute: '柔顺' },
  zhen: { name: '震', nature: '雷', attribute: '动' },
  xun: { name: '巽', nature: '风', attribute: '入' },
  kan: { name: '坎', nature: '水', attribute: '陷' },
  li: { name: '离', nature: '火', attribute: '丽' },
  gen: { name: '艮', nature: '山', attribute: '止' },
  dui: { name: '兑', nature: '泽', attribute: '悦' }
};

// 六十四卦数据（部分示例）
const hexagramsData = [
  {
    id: 1,
    name: '乾',
    description: '乾为天',
    judgment: '元亨利贞。',
    image: '天行健，君子以自强不息。',
    interpretation: '象征着纯粹的阳刚之气，代表着事物发展的开始阶段。提示我们要像天一样运行不息，保持坚韧不拔的毅力，追求崇高的理想。',
    lines: [
      '初九：潜龙勿用。',
      '九二：见龙在田，利见大人。',
      '九三：君子终日乾乾，夕惕若，厉无咎。',
      '九四：或跃在渊，无咎。',
      '九五：飞龙在天，利见大人。',
      '上九：亢龙有悔。'
    ]
  },
  {
    id: 2,
    name: '坤',
    description: '坤为地',
    judgment: '元亨，利牝马之贞。',
    image: '地势坤，君子以厚德载物。',
    interpretation: '象征着纯粹的阴柔之气，代表着包容和滋养。提示我们要像大地一样宽厚包容，滋养万物，以柔克刚。',
    lines: [
      '初六：履霜，坚冰至。',
      '六二：直方大，不习无不利。',
      '六三：含章可贞，或从王事，无成有终。',
      '六四：括囊，无咎无誉。',
      '六五：黄裳，元吉。',
      '上六：龙战于野，其血玄黄。'
    ]
  }
  // ... 其他卦象数据
];

// 获取所有卦象
const getAllHexagrams = () => {
  return hexagramsData;
};

// 根据ID获取特定卦象
const getHexagramById = (id) => {
  return hexagramsData.find(h => h.id === id);
};

// 根据名称获取卦象
const getHexagramByName = (name) => {
  return hexagramsData.find(h => h.name === name);
};

// 获取卦象的详细解释
const getHexagramInterpretation = (hexagram) => {
  if (!hexagram) return null;
  return {
    name: hexagram.name,
    description: hexagram.description,
    judgment: hexagram.judgment,
    image: hexagram.image,
    interpretation: hexagram.interpretation,
    lines: hexagram.lines
  };
};

module.exports = {
  trigrams,
  getAllHexagrams,
  getHexagramById,
  getHexagramByName,
  getHexagramInterpretation
}; 