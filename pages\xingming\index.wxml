<view class="container">
  <!-- 顶部banner -->
  <view class="banner">
    <image class="banner-bg" src="/assets/images/xingming/banner-bg.jpg" mode="aspectFill"/>
    <view class="banner-title">
      <text class="main-title">姓名测算</text>
      <text class="sub-title">一字千金，名字决定命运</text>
    </view>
  </view>

  <!-- 信息输入表单 -->
  <view class="form-section">
    <view class="form-title">基本信息</view>
    
    <!-- 姓氏输入 -->
    <view class="form-item">
      <text class="label">姓氏</text>
      <input class="input" type="text" placeholder="请输入姓氏" model:value="{{surname}}" bindinput="onSurnameInput"/>
    </view>

    <!-- 名字输入 -->
    <view class="form-item">
      <text class="label">名字</text>
      <input class="input" type="text" placeholder="请输入名字" model:value="{{givenName}}" bindinput="onGivenNameInput"/>
    </view>

    <!-- 性别选择 -->
    <view class="form-item">
      <text class="label">性别</text>
      <view class="gender-picker">
        <view 
          class="gender-option {{gender === 'male' ? 'active' : ''}}" 
          bindtap="onGenderSelect" 
          data-gender="male"
        >男</view>
        <view 
          class="gender-option {{gender === 'female' ? 'active' : ''}}" 
          bindtap="onGenderSelect" 
          data-gender="female"
        >女</view>
      </view>
    </view>

    <!-- 出生日期 -->
    <view class="form-item">
      <text class="label">出生日期</text>
      <picker mode="date" value="{{birthDate}}" bindchange="onBirthDateChange">
        <view class="picker {{birthDate ? '' : 'placeholder'}}">
          {{birthDate || '请选择出生日期'}}
        </view>
      </picker>
    </view>

    <!-- 出生时间 -->
    <view class="form-item">
      <text class="label">出生时间</text>
      <picker mode="time" value="{{birthTime}}" bindchange="onBirthTimeChange">
        <view class="picker {{birthTime ? '' : 'placeholder'}}">
          {{birthTime || '请选择出生时间'}}
        </view>
      </picker>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? '' : 'disabled'}}" bindtap="onAnalyze" disabled="{{!canSubmit}}">
      <text>开始测算</text>
      <text class="price" wx:if="{{price > 0}}">￥{{price}}</text>
    </button>
  </view>

  <!-- 分析结果 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 姓名评分 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/score.png"/>
        <text>姓名评分</text>
      </view>
      <view class="score-display">
        <view class="score">{{totalScore}}</view>
        <view class="max-score">/100</view>
      </view>
      <view class="score-desc">{{scoreDesc}}</view>
    </view>

    <!-- 五格数理 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/wuge.png"/>
        <text>五格数理</text>
      </view>
      <view class="wuge-grid">
        <view class="wuge-item" wx:for="{{wugeList}}" wx:key="name">
          <text class="wuge-name">{{item.name}}</text>
          <text class="wuge-number">{{item.number}}</text>
          <text class="wuge-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

    <!-- 三才配置 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/sancai.png"/>
        <text>三才配置</text>
      </view>
      <view class="sancai-chart">
        <canvas canvas-id="sancaiCanvas" class="sancai-canvas"></canvas>
      </view>
      <view class="sancai-analysis">{{sancaiAnalysis}}</view>
    </view>

    <!-- 八字分析 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/bazi.png"/>
        <text>八字分析</text>
      </view>
      <view class="bazi-grid">
        <view class="bazi-row">
          <text class="label">年柱：</text>
          <text class="value">{{baziInfo.year}}</text>
        </view>
        <view class="bazi-row">
          <text class="label">月柱：</text>
          <text class="value">{{baziInfo.month}}</text>
        </view>
        <view class="bazi-row">
          <text class="label">日柱：</text>
          <text class="value">{{baziInfo.day}}</text>
        </view>
        <view class="bazi-row">
          <text class="label">时柱：</text>
          <text class="value">{{baziInfo.time}}</text>
        </view>
      </view>
      <view class="bazi-analysis">{{baziAnalysis}}</view>
    </view>

    <!-- 吉凶分析 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/jixiong.png"/>
        <text>吉凶分析</text>
      </view>
      <view class="jixiong-list">
        <view class="jixiong-item" wx:for="{{jixiongList}}" wx:key="type">
          <view class="jixiong-type">{{item.type}}</view>
          <view class="jixiong-desc">{{item.description}}</view>
        </view>
      </view>
    </view>

    <!-- 建议方案 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/advice.png"/>
        <text>建议方案</text>
      </view>
      <view class="advice-list">
        <view class="advice-item" wx:for="{{adviceList}}" wx:key="title">
          <view class="advice-title">{{item.title}}</view>
          <view class="advice-content">{{item.content}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部分享按钮 -->
  <view class="share-section" wx:if="{{showResult}}">
    <button class="share-btn" open-type="share">
      <image class="share-icon" src="/assets/icons/share.png"/>
      <text>分享测算结果</text>
    </button>
  </view>
</view> 