const { NameCalculator } = require('../../utils/name/calculator');

Page({
  data: {
    surname: '',
    givenName: '',
    gender: 'male',
    birthDate: '',
    loading: false,
    showResult: false,
    wugeResult: [],
    nameScore: 0,
    scoreDescription: '',
    wuxingResult: [],
    nameDetails: [],
    fortuneResult: [],
    suggestions: []
  },

  onLoad() {
    // 页面加载时的初始化逻辑
  },

  onSurnameInput(e) {
    this.setData({
      surname: e.detail.value
    });
  },

  onGivenNameInput(e) {
    this.setData({
      givenName: e.detail.value
    });
  },

  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    });
  },

  onBirthDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    });
  },

  async analyzeName() {
    if (!this.validateInput()) {
      return;
    }

    this.setData({ loading: true });

    try {
      const calculator = new NameCalculator();
      const result = await calculator.calculate({
        surname: this.data.surname,
        givenName: this.data.givenName,
        gender: this.data.gender,
        birthDate: this.data.birthDate
      });

      this.setData({
        loading: false,
        showResult: true,
        wugeResult: [
          { name: '天格', number: result.wuge.tian, meaning: result.wuge.tianMeaning },
          { name: '人格', number: result.wuge.ren, meaning: result.wuge.renMeaning },
          { name: '地格', number: result.wuge.di, meaning: result.wuge.diMeaning },
          { name: '外格', number: result.wuge.wai, meaning: result.wuge.waiMeaning },
          { name: '总格', number: result.wuge.zong, meaning: result.wuge.zongMeaning }
        ],
        nameScore: result.score,
        scoreDescription: result.scoreDescription,
        wuxingResult: [
          { element: '金', percentage: result.wuxing.metal, color: '#FFD700' },
          { element: '木', percentage: result.wuxing.wood, color: '#90EE90' },
          { element: '水', percentage: result.wuxing.water, color: '#87CEEB' },
          { element: '火', percentage: result.wuxing.fire, color: '#FF6B6B' },
          { element: '土', percentage: result.wuxing.earth, color: '#DEB887' }
        ],
        nameDetails: result.details.map(item => ({
          aspect: item.aspect,
          description: item.description
        })),
        fortuneResult: [
          { aspect: '事业运', stars: this.getStars(result.fortune.career) },
          { aspect: '财运', stars: this.getStars(result.fortune.wealth) },
          { aspect: '感情运', stars: this.getStars(result.fortune.love) },
          { aspect: '健康运', stars: this.getStars(result.fortune.health) }
        ],
        suggestions: result.suggestions.map(item => ({
          name: item.name,
          description: item.description
        }))
      });
    } catch (error) {
      console.error('姓名分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  validateInput() {
    if (!this.data.surname.trim()) {
      wx.showToast({
        title: '请输入姓氏',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.givenName.trim()) {
      wx.showToast({
        title: '请输入名字',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.birthDate) {
      wx.showToast({
        title: '请选择出生日期',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  getStars(value) {
    // 将0-100的分数转换为1-5颗星
    const starCount = Math.ceil(value / 20);
    return new Array(starCount).fill('★');
  },

  onShareAppMessage() {
    return {
      title: '姓名测算',
      path: '/pages/name/index'
    };
  }
}); 