// utils/update-helper.js
// 小程序更新管理工具

/**
 * 检查小程序更新
 */
function checkForUpdate() {
  const updateManager = wx.getUpdateManager()
  
  updateManager.onCheckForUpdate(function (res) {
    // 请求完新版本信息的回调
    console.log('检查更新结果:', res.hasUpdate)
  })
  
  updateManager.onUpdateReady(function () {
    wx.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: function (res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      }
    })
  })
  
  updateManager.onUpdateFailed(function () {
    // 新版本下载失败
    wx.showToast({
      title: '更新失败',
      icon: 'none'
    })
  })
}

/**
 * 强制更新检查
 */
function forceUpdate() {
  const updateManager = wx.getUpdateManager()
  
  updateManager.onUpdateReady(function () {
    updateManager.applyUpdate()
  })
  
  updateManager.onUpdateFailed(function () {
    wx.showModal({
      title: '更新失败',
      content: '新版本下载失败，请检查网络后重试',
      showCancel: false
    })
  })
}

/**
 * 版本比较
 */
function compareVersion(v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  const len = Math.max(v1.length, v2.length)
  
  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }
  
  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i])
    const num2 = parseInt(v2[i])
    
    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }
  
  return 0
}

/**
 * 获取当前版本信息
 */
function getCurrentVersion() {
  const accountInfo = wx.getAccountInfoSync()
  return {
    version: accountInfo.miniProgram.version,
    envVersion: accountInfo.miniProgram.envVersion
  }
}

/**
 * 检查基础库版本
 */
function checkLibVersion(requiredVersion) {
  const systemInfo = wx.getSystemInfoSync()
  const currentVersion = systemInfo.SDKVersion
  
  if (compareVersion(currentVersion, requiredVersion) < 0) {
    wx.showModal({
      title: '提示',
      content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
      showCancel: false
    })
    return false
  }
  
  return true
}

/**
 * 初始化更新管理
 */
function initUpdateManager() {
  // 检查小程序是否有新版本发布
  if (wx.canIUse('getUpdateManager')) {
    checkForUpdate()
  }
  
  // 检查基础库版本
  const requiredLibVersion = '2.0.0'
  checkLibVersion(requiredLibVersion)
}

module.exports = {
  checkForUpdate,
  forceUpdate,
  compareVersion,
  getCurrentVersion,
  checkLibVersion,
  initUpdateManager
} 