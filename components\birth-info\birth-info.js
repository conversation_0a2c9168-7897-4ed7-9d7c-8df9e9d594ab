Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showForm: {
      type: Boolean,
      value: true
    },
    top: {
      type: Number,
      value: 200
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    birthDate: '',
    birthTime: '',
    gender: '男',
    isExpanded: false
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 组件加载时获取已保存的信息
      const birthInfo = wx.getStorageSync('birthInfo')
      if (birthInfo) {
        this.setData({
          birthDate: birthInfo.birthDate,
          birthTime: birthInfo.birthTime,
          gender: birthInfo.gender
        })
        
        // 触发change事件，通知父组件
        this.triggerEvent('change', {
          date: birthInfo.birthDate,
          time: birthInfo.birthTime,
          gender: birthInfo.gender
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 日期选择
    onDateChange(e) {
      this.setData({
        birthDate: e.detail.value
      })
      
      // 触发change事件
      this.triggerEvent('change', {
        date: e.detail.value,
        time: this.data.birthTime,
        gender: this.data.gender
      })
    },

    // 时间选择
    onTimeChange(e) {
      this.setData({
        birthTime: e.detail.value
      })
      
      // 触发change事件
      this.triggerEvent('change', {
        date: this.data.birthDate,
        time: e.detail.value,
        gender: this.data.gender
      })
    },

    // 性别选择
    onGenderChange(e) {
      const gender = ['男', '女'][e.detail.value]
      this.setData({
        gender
      })
      
      // 触发change事件
      this.triggerEvent('change', {
        date: this.data.birthDate,
        time: this.data.birthTime,
        gender
      })
    },

    // 保存信息
    saveInfo() {
      const { birthDate, birthTime, gender } = this.data
      
      if (!birthDate || !birthTime) {
        wx.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }

      const birthInfo = {
        birthDate,
        birthTime,
        gender
      }

      wx.setStorageSync('birthInfo', birthInfo)
      
      // 触发保存成功事件
      this.triggerEvent('save', birthInfo)
      
      // 触发change事件
      this.triggerEvent('change', {
        date: birthDate,
        time: birthTime,
        gender
      })

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },

    getBirthInfo() {
      return {
        birthDate: this.data.birthDate,
        birthTime: this.data.birthTime,
        gender: this.data.gender
      }
    },

    // 点击箭头图标
    onArrowTap() {
      this.setData({
        isExpanded: !this.data.isExpanded
      })
    },

    // 点击文本
    onTextTap() {
      if (this.data.isExpanded) {
        wx.navigateTo({
          url: '/pages/birth-info/birth-info',
          success: () => {
            // 导航成功后重置状态
            setTimeout(() => {
              this.setData({ isExpanded: false })
            }, 300)
          }
        })
      } else {
        this.setData({ isExpanded: true })
      }
    }
  }
}) 