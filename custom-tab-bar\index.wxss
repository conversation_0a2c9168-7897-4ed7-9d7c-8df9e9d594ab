.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: calc(10rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -1px 6px rgba(0, 0, 0, 0.06);
  z-index: 999;
}

.tab-bar-shadow {
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.03) 100%);
  border-radius: 20px 20px 0 0;
  pointer-events: none;
}

.tab-bar-container {
  width: 100%;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.06);
  border-top: 0.5px solid rgba(232, 232, 232, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
}

/* 添加中央填充区域 - 优化形状 */
/* 注释掉中央填充区域
.tab-bar-container .center-fill {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 64px;
  height: 41px;
  background: rgba(255, 255, 255, 0.99);
  z-index: 1;
  border-radius: 0 0 32px 32px;
}
*/

/* 创建AI按钮的凸出基座效果 - 曲线形式斜接 */
/* 注释掉凸出基座
.tab-bar-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 130px;
  height: 42px;
  background: 
    radial-gradient(ellipse 65px 42px at center bottom,
      rgba(255, 255, 255, 0.99) 0%,
      rgba(255, 255, 255, 0.99) 72%,
      transparent 73%
    );
  filter: drop-shadow(0 -1px 2px rgba(0, 0, 0, 0.02));
  z-index: 2;
}
*/

/* 创建基座的左侧曲线 - 优化连接 */
/* 注释掉左侧曲线
.tab-bar-container::after {
  content: '';
  position: absolute;
  top: -20px;
  left: calc(50% - 65px);
  width: 40px;
  height: 42px;
  background: 
    radial-gradient(ellipse 40px 50px at 100% 50%,
      transparent 0%,
      transparent 65%,
      rgba(255, 255, 255, 0.99) 68%,
      rgba(255, 255, 255, 0.99) 100%
    );
  box-shadow: 
    inset -0.5px 0 1px rgba(0, 0, 0, 0.01),
    inset 0 0.5px 0.5px rgba(255, 255, 255, 0.5);
  z-index: 1;
}
*/

/* 创建基座的右侧曲线 - 优化连接 */
/* 注释掉右侧曲线
.special-item::before {
  content: '';
  position: absolute;
  bottom: 13px;
  right: -40px;
  width: 40px;
  height: 42px;
  background: 
    radial-gradient(ellipse 40px 50px at 0% 50%,
      transparent 0%,
      transparent 65%,
      rgba(255, 255, 255, 0.99) 68%,
      rgba(255, 255, 255, 0.99) 100%
    );
  box-shadow: 
    inset 0.5px 0 1px rgba(0, 0, 0, 0.01),
    inset 0 0.5px 0.5px rgba(255, 255, 255, 0.5);
  z-index: 1;
}
*/

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  padding: 4rpx 0;
  transition: all 0.3s ease;
}

/* 调整AI按钮位置 */
.special-item {
  margin-top: -30rpx;  /* 调整整体向上的距离 */
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 11;
}

.tab-icon-container {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  transition: transform 0.3s ease;
}

.special-icon-container {
  width: 100rpx;  /* 增加5% */
  height: 100rpx;  /* 增加5% */
  min-width: 100rpx;
  min-height: 100rpx;
  max-width: 100rpx;
  max-height: 100rpx;
  background: linear-gradient(145deg, #7928CA, #9b4dca);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8rpx 20rpx rgba(123, 40, 202, 0.25),
    0 4rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  flex-grow: 0;
  box-sizing: border-box;
  aspect-ratio: 1 / 1;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 12;
  margin-top: -30rpx;  /* 调整向上的距离 */
}

/* AI按钮底部连接效果 - 优化与曲线基座的融合 */
.special-icon-container::before {
  content: '';
  position: absolute;
  bottom: -8px;  /* 调整位置 */
  left: 50%;
  transform: translateX(-50%);
  width: 80px;  /* 增大阴影面积 */
  height: 16px;  /* 增加高度 */
  /* 使用更明显的阴影 */
  background: radial-gradient(ellipse at center,
    rgba(123, 40, 202, 0.2) 0%,
    rgba(123, 40, 202, 0.1) 40%,
    rgba(123, 40, 202, 0.05) 70%,
    transparent 100%
  );
  filter: blur(3px);
  z-index: -1;
}

/* 为AI按钮添加环形光晕 - 优化为更柔和的效果 - 适配新位置 */
.special-icon-container::after {
  content: '';
  position: absolute;
  top: -8px;  /* 从-5px改为-8px，增强光晕 */
  left: -8px;
  right: -8px;
  bottom: -8px;
  background: radial-gradient(circle at center,
    rgba(121, 40, 202, 0.15) 20%,
    rgba(121, 40, 202, 0.08) 50%,
    rgba(121, 40, 202, 0.03) 80%,
    transparent 100%
  );
  border-radius: 50%;
  z-index: -2;
  filter: blur(20px);
  opacity: 1;
}

/* 添加光晕效果 - 适配缩小后的尺寸 */
.special-icon-container .glow {
  content: '';
  position: absolute;
  top: -45%;  /* 调整位置以适应较小的按钮 */
  left: -45%;
  width: 190%;  /* 稍微减小范围 */
  height: 190%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: pulse 3s ease-in-out infinite;
  pointer-events: none;
}

.tab-icon {
  width: 100%;
  height: 100%;
  transition: filter 0.3s ease;
}

.ai-icon {
  width: 50rpx;  /* 增加5% */
  height: 50rpx;  /* 增加5% */
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 1;
}

/* 添加顺时针自转动画 */
.rotating {
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移除之前的浮动动画，保持图标稳定旋转 */
.special-icon-container .ai-icon {
  animation: rotate 3s linear infinite;
}

.tab-text {
  font-size: 20rpx;
  margin-top: 4rpx;
  line-height: 1;
  text-align: center;
  transition: all 0.3s ease;
}

.tab-text-active {
  font-weight: 600;
  transform: scale(1.05);
}

.special-item .tab-text {
  color: #7928ca !important;
  font-weight: 600;
  margin-top: 8rpx;  /* 增加文字与图标的间距 */
  font-size: 20rpx;
  letter-spacing: 0.3px;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* 点击效果优化 */
.tab-bar-item:active .tab-icon-container {
  transform: scale(0.95);
}

.special-item:active .special-icon-container {
  transform: translateY(2px) scale(0.96);
  box-shadow: 
    0 4px 12px rgba(123, 40, 202, 0.28),  /* 稍微减弱阴影 */
    0 2px 6px rgba(0, 0, 0, 0.06),
    0 5px 16px rgba(123, 40, 202, 0.12),
    inset 0 1px 2px rgba(255, 255, 255, 0.4),
    inset 0 -1px 2px rgba(0, 0, 0, 0.15);
}

/* 为其他图标添加悬浮效果 */
.tab-bar-item:not(.special-item):active {
  background: rgba(138, 43, 226, 0.05);
  border-radius: 15px;
} 