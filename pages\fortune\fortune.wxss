.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.result-section {
  margin-top: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #8a2be2;
}

.overview-section {
  margin-bottom: 30rpx;
}

.overview-item, .detail-item, .advice-item {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.item-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.item-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 30rpx;
}

.advice-section {
  margin-top: 30rpx;
}

.birth-info-display {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-content {
  margin: 20rpx 0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.info-actions {
  margin-top: 20rpx;
  display: flex;
  justify-content: flex-end;
}

.action-button {
  font-size: 28rpx;
  color: #8a2be2;
  background-color: transparent;
  border: 1px solid #8a2be2;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  line-height: 1.5;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.tip-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.tip-button {
  font-size: 30rpx;
  color: #fff;
  background-color: #8a2be2;
  border-radius: 40rpx;
  padding: 16rpx 60rpx;
}

.chart-section {
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 400rpx;
  margin: 20rpx 0;
}

.fortune-chart {
  width: 100%;
  height: 100%;
}

.month-nodes {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20rpx;
}

.node-item {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.node-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
}

.node-dot.good {
  background-color: #4CAF50;
}

.node-dot.bad {
  background-color: #F44336;
}

.node-dot.normal {
  background-color: #FFC107;
}

.node-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.node-desc {
  font-size: 22rpx;
  color: #999;
  text-align: center;
} 