<view class="tab-bar">
  <view class="tab-bar-shadow"></view>
  <view class="tab-bar-container">
    <view 
      wx:for="{{list}}" 
      wx:key="index" 
      class="tab-bar-item {{item.isSpecial ? 'special-item' : ''}}" 
      data-path="{{item.pagePath}}" 
      data-index="{{index}}" 
      bindtap="switchTab"
    >
      <view class="tab-icon-container {{item.isSpecial ? 'special-icon-container' : ''}}">
        <view wx:if="{{item.isSpecial}}" class="glow"></view>
        <image 
          class="tab-icon {{item.isSpecial ? 'ai-icon rotating' : ''}}" 
          src="{{selected === index ? item.selectedIconPath : item.iconPath}}"
        ></image>
      </view>
      <view class="tab-text {{selected === index ? 'tab-text-active' : ''}}" style="color: {{selected === index ? selectedColor : color}}">
        {{item.text}}
      </view>
    </view>
  </view>
</view> 