<!-- AI问答界面 - 现代化设计 -->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header-bar">
    <view class="header-content">
      <view class="header-left">
        <view class="ai-status">
          <view class="status-dot {{isTyping ? 'typing' : 'online'}}"></view>
          <text class="status-text">{{isTyping ? 'AI正在思考...' : 'AI助手在线'}}</text>
        </view>
      </view>
      <view class="header-right">
        <view class="header-actions">
          <view class="action-btn" bindtap="clearChat">
            <text class="action-icon">🗑️</text>
          </view>
          <view class="action-btn" bindtap="showSettings">
            <text class="action-icon">⚙️</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 聊天主体区域 -->
  <view class="chat-container">
    <!-- 消息滚动区域 -->
    <scroll-view
      class="messages-scroll"
      scroll-y="true"
      scroll-top="{{scrollTop}}"
      scroll-into-view="message-{{messages.length-1}}"
      enhanced="true"
      show-scrollbar="false"
      bounces="true"
      refresher-enabled="false"
    >
      <!-- 欢迎界面 -->
      <view class="welcome-section" wx:if="{{showQuickActions && messages.length === 0}}">
        <view class="welcome-header">
          <view class="ai-avatar-large">
            <view class="avatar-glow"></view>
            <image class="welcome-avatar-icon" src="https://img.icons8.com/fluency-systems-filled/96/9575cd/artificial-intelligence.png"></image>
          </view>
          <view class="welcome-title">AI命理助手</view>
          <view class="welcome-subtitle">专业的命理分析，智能的玄学咨询</view>
        </view>

        <!-- 快捷功能卡片 -->
        <view class="quick-actions-grid">
          <view class="quick-card"
                wx:for="{{quickActions}}"
                wx:key="id"
                data-id="{{item.id}}"
                bindtap="onQuickActionTap">
            <view class="card-icon">{{item.icon}}</view>
            <view class="card-title">{{item.title}}</view>
            <view class="card-desc">{{item.desc || '点击开始分析'}}</view>
            <view class="card-badge" wx:if="{{item.requiresBirthInfo}}">需要出生信息</view>
          </view>
        </view>

        <!-- 使用提示 -->
        <view class="usage-tips">
          <view class="tips-title">💡 使用提示</view>
          <view class="tips-list">
            <text class="tip-item">• 点击上方卡片快速开始分析</text>
            <text class="tip-item">• 直接输入问题进行自由对话</text>
            <text class="tip-item">• 完善出生信息获得更精准结果</text>
          </view>
        </view>
      </view>

      <!-- 消息列表 -->
      <view class="message-list" wx:if="{{messages.length > 0}}">
        <view class="message-wrapper"
              wx:for="{{messages}}"
              wx:key="id"
              id="message-{{index}}">

          <!-- 消息时间分隔 -->
          <view class="time-divider" wx:if="{{item.showTime}}">
            <text class="time-text">{{item.timestamp}}</text>
          </view>

          <!-- 用户消息 -->
          <view class="message-bubble user-message" wx:if="{{item.type === 'user'}}">
            
            <view class="message-avatar user-avatar">
              <text class="avatar-text">我</text>
            </view>
            <view class="message-content">
              <view class="message-text">{{item.content}}</view>
            </view>
          </view>

          <!-- AI消息 -->
          <view class="message-bubble ai-message" wx:if="{{item.type === 'ai' || item.type === 'system'}}">
            <view class="message-avatar ai-avatar">
              <view class="avatar-glow-small"></view>
              <image class="avatar-icon" src="https://img.icons8.com/fluency-systems-filled/96/9575cd/artificial-intelligence.png"></image>
            </view>
            <view class="message-content">
              <view class="message-text {{item.isTyping ? 'typing-effect' : ''}}">{{item.content}}</view>

              <!-- 消息操作按钮 -->
              <view class="message-actions" wx:if="{{item.actions && item.actions.length > 0}}">
                <view class="action-button"
                      wx:for="{{item.actions}}"
                      wx:for-item="action"
                      wx:key="type"
                      data-action="{{action.type}}"
                      data-url="{{action.url}}"
                      bindtap="onMessageAction">
                  <text class="action-text">{{action.text}}</text>
                  <text class="action-icon">{{action.icon || '→'}}</text>
                </view>
              </view>

              <!-- 消息反馈 -->
              <view class="message-feedback">
                <view class="feedback-btn" data-content="{{item.content}}" bindtap="copyMessage">
                  <text class="feedback-icon">📋</text>
                  <text class="feedback-text">复制</text>
                </view>
                <view class="feedback-btn" data-content="{{item.content}}" bindtap="forwardToWxWork">
                  <image class="feedback-icon-img" src="https://img.icons8.com/fluency/48/wechat.png"></image>
                  <text class="feedback-text">转发</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>



      <!-- 底部占位 -->
      <view class="bottom-spacer"></view>
    </scroll-view>
  </view>

  <!-- 悬浮输入区域 - 屏幕中间位置 -->
  <view class="floating-input-container {{isInputExpanded ? 'expanded' : 'collapsed'}}">
    <!-- 展开状态 - 上下布局 -->
    <view class="expanded-input-panel" wx:if="{{isInputExpanded}}">
      <!-- 输入框区域 -->
      <view class="input-section">
        <view class="input-wrapper">
          <textarea
            class="message-input"
            value="{{inputValue}}"
            placeholder="请输入您的问题..."
            placeholder-class="input-placeholder"
            maxlength="1000"
            cursor-spacing="20"
            show-confirm-bar="{{false}}"
            adjust-position="{{true}}"
            bindinput="onInputChange"
            bindconfirm="sendMessage"
            bindfocus="onInputFocus"
            auto-height
            fixed="true"
          />
          <view class="input-counter">{{inputValue.length}}/1000</view>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="button-section">
        <view class="action-buttons">
          <button class="cancel-btn" bindtap="onCancelInput">
            <text class="btn-icon">✕</text>
            <text class="btn-text">取消</text>
          </button>
          <button
            class="send-btn {{inputValue.trim() ? 'active' : 'disabled'}}"
            bindtap="sendMessage"
            disabled="{{!inputValue.trim() || isTyping}}"
          >
            <text class="btn-icon">{{isTyping ? '⏳' : '🚀'}}</text>
            <text class="btn-text">{{isTyping ? '发送中' : '发送'}}</text>
          </button>
        </view>
      </view>

      <!-- 快捷回复 -->
      <view class="quick-replies" wx:if="{{quickReplies.length > 0}}">
        <view class="quick-reply-item"
              wx:for="{{quickReplies}}"
              wx:key="*this"
              data-text="{{item}}"
              bindtap="selectQuickReply">
          {{item}}
        </view>
      </view>
    </view>

    <!-- 收起状态 - 悬浮球 -->
    <view class="floating-ball" wx:else bindtap="onFabClick">
      <view class="ball-glow"></view>
      <view class="ball-content">
        <text class="ball-icon">{{fabIcon}}</text>
      </view>
      <view class="ball-pulse"></view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="overlay {{isInputExpanded ? 'show' : 'hide'}}"></view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>