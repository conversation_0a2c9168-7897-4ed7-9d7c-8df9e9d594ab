Page({
  /**
   * 页面的初始数据
   */
  data: {
    logs: [],
    filteredLogs: [],
    activeFilter: 'all',
    filters: [
      { key: 'all', name: '全部' },
      { key: 'error', name: '错误' },
      { key: 'warning', name: '警告' },
      { key: 'info', name: '信息' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadLogs()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 加载日志数据
   */
  loadLogs() {
    // 模拟日志数据
    const mockLogs = [
      {
        id: 1,
        time: '2024-01-15 14:30:25',
        level: 'error',
        message: '数据库连接失败',
        source: 'system.db'
      },
      {
        id: 2,
        time: '2024-01-15 14:25:10',
        level: 'warning',
        message: '内存使用率过高 (85%)',
        source: 'system.monitor'
      },
      {
        id: 3,
        time: '2024-01-15 14:20:05',
        level: 'info',
        message: '用户登录成功',
        source: 'auth.service'
      },
      {
        id: 4,
        time: '2024-01-15 14:15:30',
        level: 'info',
        message: '系统启动完成',
        source: 'system.boot'
      }
    ]

    this.setData({
      logs: mockLogs,
      filteredLogs: mockLogs
    })
  },

  /**
   * 筛选日志
   */
  filterLogs(e) {
    const filter = e.currentTarget.dataset.filter
    
    this.setData({
      activeFilter: filter
    })

    let filteredLogs = this.data.logs
    if (filter !== 'all') {
      filteredLogs = this.data.logs.filter(log => log.level === filter)
    }

    this.setData({
      filteredLogs
    })
  },

  /**
   * 清理日志
   */
  clearLogs() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有日志吗？此操作不可撤销。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            logs: [],
            filteredLogs: []
          })
          wx.showToast({
            title: '日志已清理',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 导出日志
   */
  exportLogs() {
    wx.showLoading({
      title: '导出中...'
    })

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
    }, 1500)
  },

  /**
   * 刷新日志
   */
  onPullDownRefresh() {
    this.loadLogs()
    wx.stopPullDownRefresh()
  }
}) 