/* pages/post-detail/post-detail.wxss */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);
  display: flex;
  flex-direction: column;
}

.content-scroll {
  flex: 1;
  padding-bottom: 120rpx;
}

/* 文章头图 */
.article-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%);
}

.header-info {
  position: absolute;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  color: white;
}

.article-category {
  display: inline-block;
  padding: 8rpx 20rpx;
  background: rgba(149, 117, 205, 0.8);
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-bottom: 15rpx;
}

.article-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  line-height: 1.4;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 作者信息 */
.author-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: white;
  margin: -40rpx 20rpx 20rpx;
  border-radius: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  position: relative;
  z-index: 1;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 4rpx solid #f5f0ff;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.author-desc {
  font-size: 26rpx;
  color: #999;
}

.article-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

/* 文章内容 */
.article-content {
  padding: 0 30rpx;
}

.article-summary {
  padding: 25rpx;
  background: linear-gradient(135deg, #f8f5ff 0%, #f0ebf8 100%);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  font-size: 30rpx;
  color: #666;
  line-height: 1.8;
  border-left: 6rpx solid #9575cd;
}

.article-body {
  font-size: 32rpx;
  color: #333;
  line-height: 2;
  margin-bottom: 40rpx;
}

.article-body rich-text {
  display: block;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 40rpx;
}

.tag-item {
  padding: 10rpx 25rpx;
  background: #f5f0ff;
  color: #9575cd;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 2rpx solid #e8dff5;
  transition: all 0.3s ease;
}

/* 互动区域 */
.interaction-section {
  margin: 40rpx 30rpx;
}

.interaction-bar {
  display: flex;
  justify-content: space-around;
  background: white;
  padding: 30rpx;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.08);
}

.interaction-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.interaction-item.active {
  background: #f5f0ff;
  color: #9575cd;
}

.interaction-item text:first-child {
  font-size: 40rpx;
}

.interaction-item text:last-child {
  font-size: 26rpx;
  color: #666;
}

/* 相关推荐 */
.related-section {
  margin: 40rpx 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-left: 15rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 70%;
  background: #9575cd;
  border-radius: 3rpx;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.related-item {
  display: flex;
  background: white;
  padding: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.related-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.related-image {
  width: 140rpx;
  height: 100rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  object-fit: cover;
}

.related-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.related-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related-meta {
  font-size: 24rpx;
  color: #999;
}

/* 评论区域 */
.comment-section {
  margin: 40rpx 30rpx;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.comment-item {
  display: flex;
  background: white;
  padding: 25rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.comment-author {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.comment-actions {
  display: flex;
  gap: 30rpx;
}

.action-btn {
  font-size: 26rpx;
  color: #999;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  color: #9575cd;
}

.load-more {
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
}

/* 底部评论输入 */
.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  align-items: center;
  gap: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.comment-input {
  flex: 1;
  height: 70rpx;
  padding: 0 25rpx;
  background: #f5f0ff;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
}

.comment-submit {
  padding: 0 35rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  color: white;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.3);
} 