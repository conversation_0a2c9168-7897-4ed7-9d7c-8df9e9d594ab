// components/toast/toast.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    message: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'info' // success, error, warning, info
    },
    duration: {
      type: Number,
      value: 2000
    },
    position: {
      type: String,
      value: 'center' // top, center, bottom
    },
    showIcon: {
      type: Boolean,
      value: true
    },
    zIndex: {
      type: Number,
      value: 9999
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    iconMap: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示Toast
     */
    showToast(options = {}) {
      const { message = '', type = 'info', duration = 2000, position = 'center', showIcon = true } = options
      
      this.setData({
        show: true,
        message,
        type,
        position,
        showIcon
      })

      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => {
          this.hideToast()
        }, duration)
      }
    },

    /**
     * 隐藏Toast
     */
    hideToast() {
      this.setData({
        show: false
      })
    },

    /**
     * 成功提示
     */
    success(message, duration = 2000) {
      this.showToast({
        message,
        type: 'success',
        duration
      })
    },

    /**
     * 错误提示
     */
    error(message, duration = 3000) {
      this.showToast({
        message,
        type: 'error',
        duration
      })
    },

    /**
     * 警告提示
     */
    warning(message, duration = 2500) {
      this.showToast({
        message,
        type: 'warning',
        duration
      })
    },

    /**
     * 信息提示
     */
    info(message, duration = 2000) {
      this.showToast({
        message,
        type: 'info',
        duration
      })
    }
  }
}) 