// components/loading/loading.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    text: {
      type: String,
      value: '加载中...'
    },
    type: {
      type: String,
      value: 'default' // default, mini
    },
    maskClosable: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 隐藏loading
     */
    hide() {
      this.setData({
        show: false
      })
    },

    /**
     * 显示loading
     */
    show(text = '') {
      this.setData({
        show: true,
        text: text || this.properties.text
      })
    },

    onMaskTap() {
      if (this.properties.maskClosable) {
        this.triggerEvent('close');
      }
    }
  }
}) 