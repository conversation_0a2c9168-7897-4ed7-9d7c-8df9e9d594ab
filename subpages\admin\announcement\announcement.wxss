/* subpages/admin/announcement/announcement.wxss */
/* 此文件是为了解决编译器路径错误而创建的 */

.announcement-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

.announcement-header {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.announcement-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8a2be2;
}

.announcement-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.announcement-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.announcement-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.announcement-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.announcement-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.announcement-subject {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.announcement-date {
  font-size: 24rpx;
  color: #666;
}

.announcement-status {
  padding: 8rpx 16rpx;
  background: #8a2be2;
  color: white;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.announcement-actions {
  display: flex;
  justify-content: center;
  padding: 20rpx;
}

.action-btn {
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.action-btn:active {
  opacity: 0.8;
} 