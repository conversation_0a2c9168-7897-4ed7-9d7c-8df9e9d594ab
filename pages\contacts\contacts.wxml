<!--pages/contacts/contacts.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input 
        class="search-input" 
        placeholder="搜索联系人" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchConfirm"
      />
    </view>
  </view>

  <!-- 部门导航 -->
  <view class="department-nav" wx:if="{{departments.length > 0}}">
    <scroll-view scroll-x="true" class="department-scroll">
      <view class="department-item {{currentDepartment === '' ? 'active' : ''}}" 
            bindtap="selectDepartment" data-id="">
        全部
      </view>
      <view class="department-item {{currentDepartment === item.id ? 'active' : ''}}" 
            wx:for="{{departments}}" 
            wx:key="id"
            bindtap="selectDepartment" 
            data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 联系人列表 -->
  <view class="contacts-list">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <icon type="loading" size="20"></icon>
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:elif="{{!contacts.length}}">
      <image class="empty-image" src="/assets/images/empty-contacts.png"></image>
      <text class="empty-text">暂无联系人</text>
    </view>

    <!-- 联系人列表 -->
    <view class="contact-item" 
          wx:for="{{contacts}}" 
          wx:key="userid"
          bindtap="viewContactDetail"
          data-contact="{{item}}">
      <view class="contact-avatar">
        <image class="avatar-image" src="{{item.avatar || '/assets/images/default-avatar.png'}}"></image>
        <view class="online-status {{item.status}}" wx:if="{{item.status}}"></view>
      </view>
      
      <view class="contact-info">
        <view class="contact-name">{{item.name}}</view>
        <view class="contact-detail">
          <text class="department" wx:if="{{item.department}}">{{item.department[0]}}</text>
          <text class="position" wx:if="{{item.position}}"> · {{item.position}}</text>
        </view>
        <view class="contact-mobile" wx:if="{{item.mobile}}">{{item.mobile}}</view>
      </view>

      <view class="contact-actions">
        <view class="action-btn chat-btn" 
              bindtap="startChat" 
              data-contact="{{item}}"
              catchtap="true">
          <icon class="action-icon" type="text" size="16"></icon>
        </view>
        <view class="action-btn call-btn" 
              bindtap="makeCall" 
              data-phone="{{item.mobile}}"
              catchtap="true"
              wx:if="{{item.mobile}}">
          <icon class="action-icon" type="success" size="16"></icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作浮动按钮 -->
  <view class="fab-container">
    <view class="fab-btn" bindtap="showFabMenu">
      <icon type="plus" size="20" color="#fff"></icon>
    </view>
    
    <!-- 快速操作菜单 -->
    <view class="fab-menu {{showFabMenu ? 'show' : ''}}" wx:if="{{showFabMenu}}">
      <!-- 管理员功能 -->
      <view class="fab-menu-item" bindtap="addContact" wx:if="{{app.globalData.permissions.admin}}">
        <icon type="plus" size="16"></icon>
        <text>添加联系人</text>
      </view>
      <view class="fab-menu-item" bindtap="manageDepartments" wx:if="{{app.globalData.permissions.admin}}">
        <icon type="folder" size="16"></icon>
        <text>管理部门</text>
      </view>
      <view class="fab-menu-item" bindtap="batchSendAIMessage" wx:if="{{app.globalData.permissions.admin}}">
        <icon type="mail" size="16"></icon>
        <text>批量AI消息</text>
      </view>
      
      <!-- AI功能 -->
      <view class="fab-menu-item ai-item" bindtap="quickAIChat">
        <icon type="chat" size="16"></icon>
        <text>AI智能助手</text>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="mask" wx:if="{{showFabMenu}}" bindtap="hideFabMenu"></view>
</view> 