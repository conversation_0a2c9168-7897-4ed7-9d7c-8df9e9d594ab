<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 分类导航 -->
  <scroll-view class="category-nav" scroll-x enable-flex>
    <view 
      class="category-item {{currentCategory === item.id ? 'active' : ''}}"
      wx:for="{{categories}}"
      wx:key="id"
      bindtap="switchCategory"
      data-category="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 帖子列表 -->
  <scroll-view class="post-list" scroll-y enable-flex>
    <view class="post-item" wx:for="{{posts}}" wx:key="_id">
      <!-- 用户信息 -->
      <view class="post-header">
        <image class="user-avatar" src="{{item.author.avatarUrl}}" mode="aspectFill"/>
        <view class="user-info">
          <text class="username">{{item.author.nickName}}</text>
          <text class="post-time">{{item.createTime}}</text>
        </view>
      </view>

      <!-- 帖子内容 -->
      <view class="post-content" bindtap="handleComment" data-post-id="{{item._id}}">
        <text class="content-text">{{item.content}}</text>
        <!-- 图片列表 -->
        <view class="image-list" wx:if="{{item.images && item.images.length > 0}}">
          <image 
            class="post-image {{item.images.length === 1 ? 'single' : ''}}" 
            wx:for="{{item.images}}" 
            wx:key="*this"
            wx:for-item="image"
            src="{{image}}"
            mode="aspectFill"
          />
        </view>
      </view>

      <!-- 互动栏 -->
      <view class="interaction-bar">
        <view 
          class="interaction-item {{item.liked ? 'liked' : ''}}" 
          bindtap="handleLike"
          data-post-id="{{item._id}}"
          data-index="{{index}}"
        >
          <image class="icon" src="{{item.liked ? '/assets/icons/liked.png' : '/assets/icons/like.png'}}"/>
          <text>{{item.likes || 0}}</text>
        </view>
        <view 
          class="interaction-item" 
          bindtap="handleComment"
          data-post-id="{{item._id}}"
        >
          <image class="icon" src="/assets/icons/comment.png"/>
          <text>{{item.comments || 0}}</text>
        </view>
        <view 
          class="interaction-item" 
          bindtap="handleShare"
          data-post-id="{{item._id}}"
          data-index="{{index}}"
        >
          <image class="icon" src="/assets/icons/share.png"/>
          <text>分享</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-status">
      <view class="loading" wx:if="{{loading}}">
        <image class="loading-icon" src="/assets/icons/loading.gif"/>
        <text>加载中...</text>
      </view>
      <view class="no-more" wx:if="{{!hasMore && posts.length > 0}}">
        没有更多内容了
      </view>
      <view class="empty" wx:if="{{!loading && posts.length === 0}}">
        <image class="empty-icon" src="/assets/icons/empty.png"/>
        <text>暂无内容</text>
      </view>
    </view>
  </scroll-view>

  <!-- 发帖按钮 -->
  <view class="post-btn" bindtap="navigateToPost">
    <image class="post-icon" src="/assets/icons/post.png"/>
  </view>
</view> 