const app = getApp()

Page({
  data: {
    // 消息列表
    messages: [],
    // 输入内容
    inputContent: '',
    // 是否显示表情面板
    showEmoji: false,
    // 是否显示更多面板
    showMore: false,
    // 常见问题列表
    faqList: [
      '如何修改个人信息？',
      '如何查看订单状态？',
      '如何进行付款？',
      '如何申请退款？'
    ],
    // 客服信息
    serviceInfo: {
      avatar: '/assets/images/service-avatar.png',
      name: '智能客服小易'
    },
    // 加载状态
    loading: false,
    // 是否正在输入
    isTyping: false
  },

  onLoad() {
    this.initMessages()
  },

  // 初始化消息
  initMessages() {
    const welcomeMsg = {
      type: 'service',
      content: '您好！我是智能客服小易，很高兴为您服务。请问有什么可以帮您？',
      time: new Date().getTime()
    }
    this.setData({
      messages: [welcomeMsg]
    })
  },

  // 发送消息
  async sendMessage(content, type = 'text') {
    if (!content.trim() && type === 'text') return

    // 添加用户消息
    const userMsg = {
      type: 'user',
      content,
      contentType: type,
      time: new Date().getTime()
    }

    this.setData({
      messages: [...this.data.messages, userMsg],
      inputContent: '',
      showEmoji: false,
      showMore: false,
      isTyping: true
    })

    // 滚动到底部
    this.scrollToBottom()

    try {
      // 调用后端API获取回复
      const res = await wx.cloud.callFunction({
        name: 'getServiceReply',
        data: { content }
      })

      // 添加客服回复
      const serviceMsg = {
        type: 'service',
        content: res.result.reply,
        time: new Date().getTime()
      }

      this.setData({
        messages: [...this.data.messages, serviceMsg],
        isTyping: false
      })

      // 滚动到底部
      this.scrollToBottom()
    } catch (err) {
      console.error('获取回复失败:', err)
      this.setData({ isTyping: false })
    }
  },

  // 输入内容变化
  handleInput(e) {
    this.setData({
      inputContent: e.detail.value
    })
  },

  // 发送文本消息
  handleSend() {
    this.sendMessage(this.data.inputContent)
  },

  // 选择图片
  handleChooseImage() {
    wx.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        // 上传图片
        this.uploadImage(tempFilePath)
      }
    })
  },

  // 上传图片
  async uploadImage(filePath) {
    try {
      this.setData({ loading: true })
      
      // 上传到云存储
      const res = await wx.cloud.uploadFile({
        cloudPath: `chat/${Date.now()}.png`,
        filePath
      })

      // 发送图片消息
      this.sendMessage(res.fileID, 'image')
      
      this.setData({ loading: false })
    } catch (err) {
      console.error('上传图片失败:', err)
      this.setData({ loading: false })
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  },

  // 切换表情面板
  toggleEmoji() {
    this.setData({
      showEmoji: !this.data.showEmoji,
      showMore: false
    })
  },

  // 切换更多面板
  toggleMore() {
    this.setData({
      showMore: !this.data.showMore,
      showEmoji: false
    })
  },

  // 选择常见问题
  handleSelectFaq(e) {
    const { question } = e.currentTarget.dataset
    this.sendMessage(question)
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('#message-list')
        .boundingClientRect((rect) => {
          wx.pageScrollTo({
            scrollTop: rect.height,
            duration: 300
          })
        })
        .exec()
    }, 100)
  }
}) 