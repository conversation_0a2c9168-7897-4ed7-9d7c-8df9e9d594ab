/* pages/fengshui/index.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.form-section {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.picker {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.analyze-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4a5568;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 40rpx 0;
}

.result-section {
  margin-top: 40rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.bagua-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.grid-item {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.grid-item.active {
  background: #4a5568;
  color: #fff;
}

.direction {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.gua-name {
  font-size: 24rpx;
  display: block;
  margin-bottom: 8rpx;
}

.element {
  font-size: 24rpx;
  color: #666;
}

.position-analysis {
  margin-top: 20rpx;
}

.position-item {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.position-header.auspicious {
  color: #38a169;
}

.position-header.inauspicious {
  color: #e53e3e;
}

.usage {
  font-size: 26rpx;
}

.description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.layout-advice {
  margin-top: 20rpx;
}

.advice-item {
  margin-bottom: 20rpx;
}

.room-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.suggestion {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.solutions {
  margin-top: 20rpx;
}

.solution-item {
  margin-bottom: 20rpx;
}

.solution-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.solution-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 顶部banner */
.banner {
  position: relative;
  width: 100%;
  height: 360rpx;
  overflow: hidden;
}

.banner-bg {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  left: 40rpx;
  bottom: 40rpx;
  color: #fff;
  z-index: 1;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 户型选择器 */
.room-picker {
  display: flex;
  gap: 20rpx;
}

.room-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.room-item text {
  font-size: 28rpx;
  color: #666;
}

.number-picker {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
}

/* 提交按钮 */
.submit-section {
  margin: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FFB88C 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.submit-btn.disabled {
  opacity: 0.6;
}

.price {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  font-weight: normal;
}

/* 评分展示 */
.score-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin: 30rpx 0;
}

.score {
  font-size: 80rpx;
  font-weight: bold;
  color: #FF6B6B;
}

.max-score {
  font-size: 32rpx;
  color: #999;
  margin-left: 8rpx;
}

.score-desc {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

/* 八卦图 */
.bagua-chart {
  width: 100%;
  height: 400rpx;
  margin: 20rpx 0;
}

.bagua-canvas {
  width: 100%;
  height: 100%;
}

.direction-analysis {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 五行分析 */
.wuxing-chart {
  width: 100%;
  height: 300rpx;
  margin: 20rpx 0;
}

.wuxing-canvas {
  width: 100%;
  height: 100%;
}

.wuxing-analysis {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 布局建议 */
.advice-list {
  margin-top: 20rpx;
}

.advice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.advice-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 化解方案 */
.solution-list {
  margin-top: 20rpx;
}

.solution-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
}

.solution-type {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.solution-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 分享按钮 */
.share-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
}

.share-btn {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.share-icon {
  width: 36rpx;
  height: 36rpx;
}

.share-btn text {
  font-size: 28rpx;
  color: #666;
} 