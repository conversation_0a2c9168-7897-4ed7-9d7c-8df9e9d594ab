/* pages/community/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 分类导航 */
.category-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  padding: 20rpx 0;
  white-space: nowrap;
  border-bottom: 2rpx solid #f0f0f0;
}

.category-item {
  display: inline-block;
  padding: 12rpx 30rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.category-item.active {
  color: #07c160;
  background-color: #e8f7ed;
}

/* 帖子列表 */
.post-list {
  margin-top: 100rpx;
  padding: 20rpx;
}

.post-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 用户信息 */
.post-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
  display: block;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 帖子内容 */
.post-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
}

.post-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
}

.post-image.single {
  grid-column: span 3;
  height: 400rpx;
}

/* 互动栏 */
.interaction-bar {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.interaction-item {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx;
}

.interaction-item .icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.interaction-item text {
  font-size: 26rpx;
  color: #666;
}

.interaction-item.liked {
  color: #07c160;
}

.interaction-item.liked text {
  color: #07c160;
}

/* 加载状态 */
.loading-status {
  padding: 30rpx;
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.loading text,
.no-more,
.empty text {
  font-size: 26rpx;
  color: #999;
}

.empty {
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

/* 发帖按钮 */
.post-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #07c160;
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.post-icon {
  width: 50rpx;
  height: 50rpx;
} 