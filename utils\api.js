// utils/api.js
// API 基础配置和请求封装

const config = require('../config/config')

/**
 * 基础API请求封装
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      url: '',
      method: 'GET',
      data: {},
      header: {
        'Content-Type': 'application/json'
      }
    }
    
    const requestOptions = Object.assign({}, defaultOptions, options)
    
    // 添加基础URL
    if (!requestOptions.url.startsWith('http')) {
      requestOptions.url = config.baseUrl + requestOptions.url
    }
    
    // 添加授权header
    const token = wx.getStorageSync('access_token')
    if (token) {
      requestOptions.header.Authorization = `Bearer ${token}`
    }
    
    wx.request({
      ...requestOptions,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, data = {}) {
  return request({
    url,
    method: 'GET',
    data
  })
}

/**
 * POST请求
 */
function post(url, data = {}) {
  return request({
    url,
    method: 'POST',
    data
  })
}

/**
 * PUT请求
 */
function put(url, data = {}) {
  return request({
    url,
    method: 'PUT',
    data
  })
}

/**
 * DELETE请求
 */
function del(url, data = {}) {
  return request({
    url,
    method: 'DELETE',
    data
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
} 