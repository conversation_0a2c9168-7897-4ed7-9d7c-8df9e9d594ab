# 恒琦易道小程序 x AI占卜系统 集成指南

## 🎯 项目概述

本项目实现了微信小程序"恒琦易道"与AI占卜后端系统的深度集成，提供智能化的玄学服务体验。

### ✨ 核心功能

#### 🤖 智能AI问答
- **意图识别**: 自动识别用户的占卜需求（八字、易经、风水等）
- **上下文理解**: 支持多轮对话，记住聊天历史
- **个性化回复**: 根据用户资料提供定制化分析
- **打字机效果**: 优雅的消息显示动画

#### 🔮 专业占卜服务
- **八字分析**: 基于出生信息的详细命理分析
- **易经卦象**: 传统周易占卜和解读
- **风水布局**: 居住和办公环境的风水建议
- **五行分析**: 五行属性和运势指导
- **运势评分**: 量化的运势评估系统

#### 🔐 安全认证体系
- **微信登录**: 一键微信授权登录
- **JWT认证**: 安全的token管理机制
- **自动刷新**: 令牌自动续期，无感知体验
- **权限控制**: 基于用户状态的功能访问控制

## 🏗️ 系统架构

### 三层架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                        小程序前端层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  AI问答页   │ │  占卜功能   │ │  用户中心   │ │  社区互动   │ │
│  │   ui-chat   │ │   fortune   │ │   profile   │ │ community   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                            ↕️ HTTPS/WSS
┌─────────────────────────────────────────────────────────────────┐
│                        API网关层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  小程序API  │ │  通用API    │ │  认证中间件 │ │  监控告警   │ │
│  │miniprogram  │ │  fortune    │ │    auth     │ │ monitoring  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                            ↕️ 
┌─────────────────────────────────────────────────────────────────┐
│                        AI服务层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  模型管理   │ │  缓存系统   │ │  知识库     │ │  性能监控   │ │
│  │ModelManager │ │    Redis    │ │KnowledgeBase│ │ Prometheus  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流转模式

1. **用户输入** → 小程序UI层
2. **意图识别** → API服务层智能路由
3. **AI处理** → 后端模型推理
4. **结果缓存** → Redis缓存优化
5. **响应展示** → 小程序美化显示

## 🚀 快速开始

### 环境准备

#### 后端环境
```bash
# 1. 安装Python依赖
cd chatbot_project
pip install -r requirements.txt

# 2. 启动Redis服务
redis-server

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，配置数据库、Redis等信息

# 4. 启动后端服务
python -m app.main_optimized
```

#### 小程序环境
```bash
# 1. 使用微信开发者工具打开项目
# 项目路径: miniprogram-1

# 2. 配置小程序AppID
# 在project.config.json中配置

# 3. 配置后端地址
# 编辑config/config.js，设置正确的apiUrl
```

### 配置说明

#### 后端配置 (`chatbot_project/.env`)
```env
# 基础配置
PROJECT_NAME="AI Fortune Telling System"
VERSION="1.0.0"
ENVIRONMENT="development"
DEBUG=true

# 服务配置
HOST="0.0.0.0"
PORT=8000

# 数据库配置
POSTGRES_SERVER="localhost"
POSTGRES_USER="postgres"
POSTGRES_PASSWORD="your_password"
POSTGRES_DB="fortune"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_DB=0

# AI模型配置
MODEL_PATH="./models/chatglm-6b"
MODEL_DEVICE="auto"
```

#### 小程序配置 (`miniprogram-1/config/config.js`)
```javascript
development: {
  apiUrl: 'http://localhost:8000',
  features: {
    enableAuth: true,
    enableCache: true,
    enableTypingEffect: true,
    maxMessageHistory: 20
  }
}
```

## 📱 功能使用指南

### AI智能问答

#### 基础对话
```javascript
// 用户输入: "你好，我想了解一下自己的运势"
// 系统响应: 自动识别为运势查询，引导提供生辰信息

// 用户输入: "请帮我分析八字"
// 系统响应: 识别为八字分析，调用专门的占卜API
```

#### 智能意图识别
系统支持以下关键词自动识别：

| 关键词 | 意图类型 | 调用接口 |
|--------|---------|----------|
| 八字、命理、算命 | bazi | `/api/miniprogram/fortune` |
| 易经、卦象、占卜 | yijing | `/api/yijing/divine` |
| 风水、布局、方位 | fengshui | `/api/fengshui/analyze` |
| 五行、属性 | wuxing | `/api/wuxing/analyze` |
| 运势、今日、未来 | fortune | `/api/miniprogram/fortune` |

#### 快捷功能
小程序提供快捷操作按钮：
- 🔮 八字分析
- ☯️ 易经卦象  
- 🏠 风水分析
- 🌿 五行分析

### 用户认证流程

#### 微信登录
```javascript
// 小程序端调用
apiService.wxLogin()
  .then(user => {
    console.log('登录成功:', user)
    // 自动保存用户信息和token
  })
  .catch(error => {
    console.error('登录失败:', error)
  })
```

#### Token管理
```javascript
// 自动token刷新
if (response.statusCode === 401) {
  await apiService.refreshAccessToken()
  // 自动重试原请求
}
```

### 占卜功能详解

#### 八字分析
```javascript
// 需要提供的生辰信息
const birthInfo = {
  year: 1990,
  month: 1, 
  day: 1,
  hour: 12,
  minute: 0
}

// API调用
apiService.fortune(birthInfo, 'bazi')
  .then(result => {
    // result.data.fortune 包含分析结果
    // - summary: 综合分析
    // - details: 详细解读  
    // - advice: 建议指导
    // - score: 运势评分
  })
```

#### 易经卦象
```javascript
// 问题咨询
apiService.yijing("我的事业发展如何？", "coin")
  .then(result => {
    // 卦象解读和指导建议
  })
```

## 🔧 技术细节

### API接口规范

#### 请求格式
```javascript
// 小程序聊天API
POST /api/miniprogram/chat
{
  "query": "用户问题",
  "context": [
    {"role": "user", "content": "历史消息"},
    {"role": "assistant", "content": "AI回复"}
  ],
  "user_info": {
    "nickname": "用户昵称",
    "birthInfo": {...}
  }
}
```

#### 响应格式
```javascript
// 成功响应
{
  "status": "success",
  "message": "回复成功",
  "data": {
    "response": {
      "content": "AI回复内容",
      "type": "ai|fortune|analysis",
      "category": "智能问答",
      "icon": "🤖",
      "timestamp": "2024-01-01T12:00:00Z"
    },
    "intent": "detected_intent",
    "suggestions": ["建议问题1", "建议问题2"]
  },
  "timestamp": "2024-01-01T12:00:00Z"
}

// 错误响应
{
  "status": "error", 
  "message": "需要完整的生辰信息",
  "data": {
    "error_code": "BIRTH_INFO_REQUIRED",
    "required_fields": ["year", "month", "day", "hour"]
  }
}
```

### 错误处理机制

#### 网络错误
```javascript
// 自动重试机制
async request(url, options) {
  for (let i = 0; i < 3; i++) {
    try {
      return await this.wxRequest(url, options)
    } catch (error) {
      if (i === 2) throw error
      await this.delay(1000 * (i + 1)) // 递增延迟
    }
  }
}
```

#### 认证错误
```javascript
// 401错误自动处理
if (response.statusCode === 401) {
  await this.refreshAccessToken()
  // 重新发起请求
  return await this.wxRequest(fullUrl, requestOptions)
}
```

#### 用户体验优化
```javascript
// 友好的错误提示
handleApiError(error) {
  let message = '请求失败'
  
  if (error.message.includes('503')) {
    message = 'AI服务暂时不可用，请稍后再试'
  } else if (error.message.includes('网络')) {
    message = '网络连接异常，请检查网络'
  }
  
  wx.showToast({
    title: message,
    icon: 'none'
  })
}
```

### 性能优化策略

#### 缓存机制
```javascript
// 多级缓存策略
1. 内存缓存 (小程序本地)
2. Redis缓存 (后端服务)  
3. 模型缓存 (AI推理结果)

// 缓存时间配置
- 聊天记录: 本地永久存储
- API响应: 30分钟
- 用户信息: 24小时
- 占卜结果: 1小时
```

#### 懒加载
```javascript
// 按需加载聊天历史
loadMessageHistory() {
  const messages = wx.getStorageSync('chat_history') || []
  this.setData({ 
    messages: messages.slice(-20) // 只加载最近20条
  })
}
```

#### 图片优化
```javascript
// 渐进式图片加载
<image 
  src="{{avatarUrl}}" 
  lazy-load="true"
  mode="aspectFill"
  placeholder="/assets/icons/default-avatar.png"
/>
```

## 🛡️ 安全考虑

### 数据安全
- **传输加密**: 全程HTTPS通信
- **Token安全**: JWT令牌定期刷新
- **数据脱敏**: 敏感信息本地加密存储
- **请求限制**: API调用频率限制

### 隐私保护
- **最小化原则**: 只收集必要的用户信息
- **本地存储**: 生辰信息等敏感数据本地存储
- **匿名化**: 后端日志不记录个人敏感信息
- **用户控制**: 支持数据删除和隐私设置

### 防护措施
- **输入验证**: 所有用户输入严格验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容转义处理
- **CSRF防护**: 请求token验证

## 📊 监控运维

### 性能监控
```javascript
// 关键指标监控
- API响应时间
- 错误率统计
- 用户活跃度
- AI模型性能
- 缓存命中率
```

### 日志管理
```javascript
// 结构化日志
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO|WARN|ERROR",
  "message": "详细信息",
  "user_id": "用户ID",
  "request_id": "请求ID",
  "endpoint": "API路径",
  "latency": 150,
  "error_code": "错误码"
}
```

### 告警机制
- **响应时间**: >2秒告警
- **错误率**: >5%告警  
- **服务可用性**: <99%告警
- **资源使用率**: >80%告警

## 🔄 版本更新

### 发布流程
1. **开发测试** → 本地环境验证
2. **集成测试** → 测试环境部署
3. **性能测试** → 压力测试验证
4. **灰度发布** → 小量用户验证
5. **全量发布** → 正式环境部署

### 回滚策略
- **自动回滚**: 错误率超过阈值自动回滚
- **手动回滚**: 运维人员一键回滚
- **数据回滚**: 数据库迁移脚本支持回滚

## 🤝 开发协作

### 代码规范
- **JavaScript**: ESLint + Prettier
- **Python**: Black + Flake8
- **提交规范**: Conventional Commits
- **分支策略**: Git Flow

### 文档维护
- **API文档**: Swagger自动生成
- **代码注释**: JSDoc规范
- **变更日志**: CHANGELOG.md
- **技术文档**: Markdown格式

## 📞 技术支持

### 常见问题

#### Q: 小程序无法连接后端服务
A: 检查以下项目：
1. 后端服务是否正常启动
2. 小程序配置的API地址是否正确
3. 网络防火墙是否阻挡了请求
4. 域名是否在小程序管理后台配置

#### Q: AI回复质量不理想
A: 可以尝试：
1. 优化prompt模板
2. 调整模型参数
3. 增加训练数据
4. 升级模型版本

#### Q: 用户登录失败
A: 排查步骤：
1. 检查微信小程序配置
2. 确认后端登录接口正常
3. 验证微信开发者密钥
4. 查看详细错误日志

### 联系方式
- **技术支持**: <EMAIL>
- **问题反馈**: github.com/project/issues
- **文档更新**: <EMAIL>

---

## 📈 未来规划

### 功能增强
- [ ] 语音识别和语音回复
- [ ] 图像识别和面相分析
- [ ] 实时通知推送
- [ ] 社交分享功能
- [ ] 多语言支持

### 技术升级
- [ ] 升级到GPT-4模型
- [ ] 实现边缘计算部署
- [ ] 增加WebRTC实时通信
- [ ] 引入区块链技术
- [ ] 支持AR/VR体验

### 运营优化
- [ ] 个性化推荐算法
- [ ] 用户画像系统
- [ ] A/B测试框架
- [ ] 数据分析平台
- [ ] 商业化模式探索

---

*更新时间: 2024年1月*
*版本: v1.0.0* 