// 出生信息管理相关API
import { request } from '../utils/request'

/**
 * 保存出生信息
 * @param {object} data - 出生信息
 */
export const saveBirthInfo = (data) => {
  return request({
    url: '/api/birth-info',
    method: 'POST',
    data
  })
}

/**
 * 获取出生信息
 */
export const getBirthInfo = () => {
  return request({
    url: '/api/birth-info',
    method: 'GET'
  })
}

/**
 * 更新出生信息
 * @param {object} data - 出生信息
 */
export const updateBirthInfo = (data) => {
  return request({
    url: '/api/birth-info',
    method: 'PUT',
    data
  })
}

/**
 * 验证出生信息
 * @param {object} data - 出生信息
 */
export const validateBirthInfo = (data) => {
  return request({
    url: '/api/birth-info/validate',
    method: 'POST',
    data
  })
}

/**
 * 获取八字信息
 * @param {object} birthData - 出生数据
 */
export const getBaziInfo = (birthData) => {
  return request({
    url: '/api/birth-info/bazi',
    method: 'POST',
    data: birthData
  })
}

/**
 * 获取五行信息
 * @param {object} birthData - 出生数据
 */
export const getWuxingInfo = (birthData) => {
  return request({
    url: '/api/birth-info/wuxing',
    method: 'POST',
    data: birthData
  })
}

/**
 * 计算农历信息
 * @param {object} solarDate - 公历日期
 */
export const calculateLunarDate = (solarDate) => {
  return request({
    url: '/api/birth-info/lunar',
    method: 'POST',
    data: solarDate
  })
}

/**
 * 获取生肖信息
 * @param {number} year - 年份
 */
export const getZodiacInfo = (year) => {
  return request({
    url: `/api/birth-info/zodiac/${year}`,
    method: 'GET'
  })
}

/**
 * 获取星座信息
 * @param {number} month - 月份
 * @param {number} day - 日期
 */
export const getConstellationInfo = (month, day) => {
  return request({
    url: `/api/birth-info/constellation/${month}/${day}`,
    method: 'GET'
  })
}
