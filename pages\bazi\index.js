const app = getApp()
const { BaziCalculator } = require('../../utils/bazi/calculator')
const { Wuxing<PERSON><PERSON>, Yun<PERSON><PERSON><PERSON> } = require('../../utils/charts')

Page({
  data: {
    // 基本信息
    name: '',
    gender: 'male',
    birthDate: '',
    birthTime: '',
    isLunar: false,
    canSubmit: false,
    price: 28,

    // 结果展示
    showResult: false,
    currentTab: 'yearly',
    
    // 分析结果数据
    wuxingAnalysis: [
      { name: '金', percent: 0, value: 0, color: '#FFD700' },
      { name: '木', percent: 0, value: 0, color: '#90EE90' },
      { name: '水', percent: 0, value: 0, color: '#87CEEB' },
      { name: '火', percent: 0, value: 0, color: '#FF6B6B' },
      { name: '土', percent: 0, value: 0, color: '#DEB887' }
    ],
    minggeAnalysis: '',
    yunshiDetails: '',
    jixiongAnalysis: [],
    adviceList: [],
    loading: false,
    baziResult: {
      pillars: [],
      wuxing: [],
      interpretation: '',
      fortune: []
    }
  },

  onLoad() {
    // 初始化图表
    this.initCharts()
  },

  // 输入处理函数
  onNameInput(e) {
    this.setData({ name: e.detail.value })
    this.checkCanSubmit()
  },

  onGenderSelect(e) {
    this.setData({ gender: e.currentTarget.dataset.gender })
    this.checkCanSubmit()
  },

  onDateChange(e) {
    this.setData({ birthDate: e.detail.value })
    this.checkCanSubmit()
  },

  onTimeChange(e) {
    this.setData({ birthTime: e.detail.value })
    this.checkCanSubmit()
  },

  onCalendarSwitch(e) {
    this.setData({ isLunar: e.detail.value })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { name, gender, birthDate, birthTime } = this.data
    const canSubmit = name && gender && birthDate && birthTime
    this.setData({ canSubmit })
  },

  // 初始化图表
  initCharts() {
    this.wuxingChart = new WuxingChart()
    this.yunshiChart = new YunshiChart()
  },

  // 分析按钮点击
  async onAnalyze() {
    if (!this.data.canSubmit) return

    wx.showLoading({ title: '正在分析...' })

    try {
      // 调用八字计算器
      const calculator = new BaziCalculator({
        name: this.data.name,
        gender: this.data.gender,
        birthDate: this.data.birthDate,
        birthTime: this.data.birthTime,
        isLunar: this.data.isLunar
      })

      // 获取分析结果
      const result = await calculator.calculate()

      // 更新五行分析
      this.setData({
        wuxingAnalysis: result.wuxing.map(item => ({
          ...item,
          percent: item.value
        }))
      })

      // 更新命格分析
      this.setData({
        minggeAnalysis: result.mingge
      })

      // 更新运势分析
      this.updateYunshiAnalysis(result.yunshi)

      // 更新吉凶分析
      this.setData({
        jixiongAnalysis: result.jixiong
      })

      // 更新建议指导
      this.setData({
        adviceList: result.advice
      })

      // 显示结果
      this.setData({ showResult: true })

      // 绘制图表
      this.drawCharts()

      wx.hideLoading()
    } catch (error) {
      console.error('分析失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      })
    }
  },

  // 切换运势标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ currentTab: tab })
    this.updateYunshiChart()
  },

  // 更新运势分析
  updateYunshiAnalysis(yunshi) {
    const { currentTab } = this.data
    this.yunshiData = yunshi

    this.setData({
      yunshiDetails: yunshi[currentTab].details
    })
  },

  // 绘制图表
  drawCharts() {
    const { wuxingAnalysis } = this.data
    
    // 绘制五行图
    this.wuxingChart.draw('wuxingCanvas', wuxingAnalysis)
    
    // 绘制运势图
    this.updateYunshiChart()
  },

  // 更新运势图表
  updateYunshiChart() {
    const { currentTab } = this.data
    const data = this.yunshiData[currentTab].data
    this.yunshiChart.draw('yunshiCanvas', data)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.name}的八字分析结果`,
      path: '/pages/bazi/index',
      imageUrl: '/assets/images/share-cover.jpg'
    }
  },

  // 页面卸载
  onUnload() {
    // 清理图表实例
    if (this.wuxingChart) {
      this.wuxingChart.dispose()
    }
    if (this.yunshiChart) {
      this.yunshiChart.dispose()
    }
  },

  async analyzeBazi() {
    if (!this.validateInput()) {
      return;
    }

    this.setData({ loading: true });

    try {
      const calculator = new BaziCalculator();
      const result = await calculator.calculate({
        birthDate: this.data.birthDate,
        birthTime: this.data.birthTime,
        gender: this.data.gender
      });

      this.setData({
        loading: false,
        showResult: true,
        baziResult: {
          pillars: [
            { name: '年柱', heavenlyStem: result.yearStem, earthlyBranch: result.yearBranch },
            { name: '月柱', heavenlyStem: result.monthStem, earthlyBranch: result.monthBranch },
            { name: '日柱', heavenlyStem: result.dayStem, earthlyBranch: result.dayBranch },
            { name: '时柱', heavenlyStem: result.hourStem, earthlyBranch: result.hourBranch }
          ],
          wuxing: [
            { element: '金', percentage: result.wuxing.metal, color: '#FFD700' },
            { element: '木', percentage: result.wuxing.wood, color: '#90EE90' },
            { element: '水', percentage: result.wuxing.water, color: '#87CEEB' },
            { element: '火', percentage: result.wuxing.fire, color: '#FF6B6B' },
            { element: '土', percentage: result.wuxing.earth, color: '#DEB887' }
          ],
          interpretation: result.interpretation,
          fortune: [
            { aspect: '事业运', stars: this.getStars(result.fortune.career) },
            { aspect: '财运', stars: this.getStars(result.fortune.wealth) },
            { aspect: '感情运', stars: this.getStars(result.fortune.love) },
            { aspect: '健康运', stars: this.getStars(result.fortune.health) }
          ]
        }
      });
    } catch (error) {
      console.error('八字分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  validateInput() {
    if (!this.data.birthDate) {
      wx.showToast({
        title: '请选择出生日期',
        icon: 'none'
      });
      return false;
    }
    if (!this.data.birthTime) {
      wx.showToast({
        title: '请选择出生时间',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  getStars(value) {
    // 将0-100的分数转换为1-5颗星
    const starCount = Math.ceil(value / 20);
    return new Array(starCount).fill('★');
  }
}) 