<!--pages/fengshui/fengshui.wxml-->
<view class="container">
  <!-- 上传区域 -->
  <view class="upload-section">
    <view class="upload-title">上传户型图</view>
    <view class="upload-area" bindtap="chooseImage" wx:if="{{!floorPlan}}">
      <image class="upload-icon" src="/assets/images/upload.png" mode="aspectFit"></image>
      <text class="upload-text">点击上传户型图</text>
    </view>
    <view class="image-preview" wx:if="{{floorPlan}}">
      <image src="{{floorPlan}}" mode="aspectFit" bindtap="previewImage"></image>
      <view class="image-actions">
        <button class="action-btn" bindtap="chooseImage">重新上传</button>
        <button class="action-btn" bindtap="analyzeLayout">开始分析</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在分析户型布局...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:if="{{error}}">
    <text class="error-text">{{error}}</text>
    <button class="retry-button" bindtap="analyzeLayout">重新分析</button>
  </view>

  <!-- 分析结果展示区域 -->
  <block wx:if="{{layoutResult && !loading && !error}}">
    <view class="flex-row space-between">
      <!-- 八卦方位分析 -->
      <view class="bagua-section" style="width: 48%;">
        <view class="section-title">八卦方位</view>
        <view class="bagua-item" wx:for="{{layoutResult.baguaAnalysis}}" wx:key="position">
          <view class="item-title">{{item.position}}</view>
          <view class="item-content">{{item.analysis}}</view>
        </view>
      </view>

      <!-- 家具摆放建议 -->
      <view class="furniture-section" style="width: 48%;">
        <view class="section-title">家具摆放</view>
        <view class="furniture-item" wx:for="{{layoutResult.furnitureAdvice}}" wx:key="room">
          <view class="item-title">{{item.room}}</view>
          <view class="item-content">{{item.advice}}</view>
        </view>
      </view>
    </view>

    <view class="flex-row space-between">
      <!-- 色彩搭配建议 -->
      <view class="color-section" style="width: 48%;">
        <view class="section-title">色彩搭配</view>
        <view class="color-item" wx:for="{{layoutResult.colorAdvice}}" wx:key="area">
          <view class="item-title">{{item.area}}</view>
          <view class="item-content">
            <text class="color-text">{{item.colors}}</text>
            <view class="color-preview" style="background: {{item.colorCode}}"></view>
          </view>
        </view>
      </view>

      <!-- 风水优化建议 -->
      <view class="optimization-section" style="width: 48%;">
        <view class="section-title">优化建议</view>
        <view class="optimization-item" wx:for="{{layoutResult.optimizationAdvice}}" wx:key="index">
          <view class="item-content">{{item}}</view>
        </view>
      </view>
    </view>
  </block>

  <!-- 空状态提示 -->
  <view class="empty-tip" wx:if="{{!floorPlan && !loading && !error}}">
    <text class="tip-text">请上传户型图开始分析</text>
  </view>
</view>
