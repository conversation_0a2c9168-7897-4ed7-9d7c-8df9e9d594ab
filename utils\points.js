// utils/points.js
// 积分管理工具

/**
 * 积分操作类型
 */
const POINT_ACTIONS = {
  SIGN_IN: 'sign_in',           // 签到
  SHARE: 'share',               // 分享
  INVITE: 'invite',             // 邀请
  COMMENT: 'comment',           // 评论
  POST: 'post',                 // 发帖
  DIVINATION: 'divination',     // 占卜
  CONSUME: 'consume'            // 消费
}

/**
 * 积分规则配置
 */
const POINT_RULES = {
  [POINT_ACTIONS.SIGN_IN]: 10,
  [POINT_ACTIONS.SHARE]: 5,
  [POINT_ACTIONS.INVITE]: 100,
  [POINT_ACTIONS.COMMENT]: 2,
  [POINT_ACTIONS.POST]: 20,
  [POINT_ACTIONS.DIVINATION]: -10
}

/**
 * 获取用户积分
 */
function getUserPoints() {
  return wx.getStorageSync('user_points') || 0
}

/**
 * 设置用户积分
 */
function setUserPoints(points) {
  wx.setStorageSync('user_points', points)
  return points
}

/**
 * 增加积分
 */
function addPoints(action, amount = null) {
  const points = amount || POINT_RULES[action] || 0
  if (points <= 0) return false
  
  const currentPoints = getUserPoints()
  const newPoints = currentPoints + points
  setUserPoints(newPoints)
  
  // 记录积分变动
  recordPointsChange(action, points, newPoints)
  
  return newPoints
}

/**
 * 消费积分
 */
function consumePoints(amount) {
  const currentPoints = getUserPoints()
  if (currentPoints < amount) {
    return false // 积分不足
  }
  
  const newPoints = currentPoints - amount
  setUserPoints(newPoints)
  
  // 记录积分变动
  recordPointsChange(POINT_ACTIONS.CONSUME, -amount, newPoints)
  
  return newPoints
}

/**
 * 检查积分是否足够
 */
function checkPoints(amount) {
  return getUserPoints() >= amount
}

/**
 * 记录积分变动
 */
function recordPointsChange(action, amount, newTotal) {
  const records = getPointsHistory()
  const record = {
    id: Date.now(),
    action,
    amount,
    newTotal,
    timestamp: new Date().toISOString(),
    date: new Date().toLocaleDateString()
  }
  
  records.unshift(record)
  
  // 只保留最近100条记录
  if (records.length > 100) {
    records.splice(100)
  }
  
  wx.setStorageSync('points_history', records)
}

/**
 * 获取积分历史
 */
function getPointsHistory() {
  return wx.getStorageSync('points_history') || []
}

/**
 * 获取今日签到状态
 */
function getTodaySignStatus() {
  const today = new Date().toDateString()
  const lastSignDate = wx.getStorageSync('last_sign_date')
  return lastSignDate === today
}

/**
 * 签到
 */
function signIn() {
  if (getTodaySignStatus()) {
    return { success: false, message: '今日已签到' }
  }
  
  const points = addPoints(POINT_ACTIONS.SIGN_IN)
  wx.setStorageSync('last_sign_date', new Date().toDateString())
  
  return { 
    success: true, 
    points, 
    message: `签到成功，获得${POINT_RULES[POINT_ACTIONS.SIGN_IN]}积分` 
  }
}

/**
 * 获取积分等级
 */
function getPointsLevel(points = null) {
  const totalPoints = points || getUserPoints()
  
  if (totalPoints < 100) return { level: 1, name: '新手' }
  if (totalPoints < 500) return { level: 2, name: '学徒' }
  if (totalPoints < 1000) return { level: 3, name: '入门' }
  if (totalPoints < 2000) return { level: 4, name: '熟练' }
  if (totalPoints < 5000) return { level: 5, name: '专家' }
  if (totalPoints < 10000) return { level: 6, name: '大师' }
  return { level: 7, name: '宗师' }
}

module.exports = {
  POINT_ACTIONS,
  POINT_RULES,
  getUserPoints,
  setUserPoints,
  addPoints,
  consumePoints,
  checkPoints,
  getPointsHistory,
  getTodaySignStatus,
  signIn,
  getPointsLevel
} 