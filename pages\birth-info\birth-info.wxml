<!--pages/birth-info/birth-info.wxml-->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <view class="header-content">
      <view class="title">完善出生信息</view>
      <view class="subtitle">为您提供更精准的命理分析</view>
    </view>
    <view class="header-decoration">
      <text class="icon">🔮</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">基本信息</text>
        <text class="card-icon">👤</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">姓名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <input
            class="input"
            placeholder="请输入您的姓名"
            bindinput="onNameInput"
            value="{{name}}"
            maxlength="20"
            placeholder-class="input-placeholder"
          />
          <text class="input-icon">✏️</text>
        </view>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">性别</text>
          <text class="required">*</text>
        </view>
        <view class="gender-selector">
          <view
            class="gender-option {{gender === '男' ? 'active' : ''}}"
            bindtap="selectGender"
            data-gender="男"
          >
            <text class="gender-icon">👨</text>
            <text class="gender-text">男</text>
          </view>
          <view
            class="gender-option {{gender === '女' ? 'active' : ''}}"
            bindtap="selectGender"
            data-gender="女"
          >
            <text class="gender-icon">👩</text>
            <text class="gender-text">女</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 出生时间卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">出生时间</text>
        <text class="card-icon">📅</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生日期时间</text>
          <text class="required">*</text>
        </view>
        <picker
          mode="multiSelector"
          value="{{dateTimeArray}}"
          bindchange="onDateTimeChange"
          bindcolumnchange="onDateTimeColumnChange"
          range="{{dateTime}}"
        >
          <view class="picker-wrapper">
            <text class="picker-text {{selectedDateTime ? 'selected' : 'placeholder'}}">
              {{selectedDateTime || '请选择出生日期时间'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
        <view class="picker-tip">精确的出生时间有助于提供更准确的分析</view>
      </view>
    </view>

    <!-- 辅助信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">辅助信息</text>
        <text class="card-icon">🎯</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">生肖</text>
          <text class="optional">选填</text>
        </view>
        <picker bindchange="onZodiacChange" value="{{zodiacIndex}}" range="{{zodiacList}}">
          <view class="picker-wrapper">
            <text class="picker-text {{zodiacList[zodiacIndex] ? 'selected' : 'placeholder'}}">
              {{zodiacList[zodiacIndex] || '请选择生肖'}}
            </text>
            <text class="picker-icon">🐲</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">幸运数字</text>
          <text class="optional">选填</text>
        </view>
        <picker bindchange="onNumberChange" value="{{numberIndex}}" range="{{numberList}}">
          <view class="picker-wrapper">
            <text class="picker-text {{numberList[numberIndex] ? 'selected' : 'placeholder'}}">
              {{numberList[numberIndex] || '请选择数字(1-62)'}}
            </text>
            <text class="picker-icon">🔢</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 姓名和出生时间为必填项</text>
        <text class="tip-item">• 出生时间越精确，分析结果越准确</text>
        <text class="tip-item">• 您的信息将被安全保护，仅用于命理分析</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
        bindtap="saveInfo"
        disabled="{{!canSubmit}}"
      >
        <text class="btn-text">完成设置</text>
        <text class="btn-icon">✨</text>
      </button>
    </view>
  </view>
</view>