/* components/toast/toast.wxss */

.toast-container {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.toast-content {
  max-width: 80%;
  min-width: 120px;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 14px;
  line-height: 1.4;
  text-align: center;
  animation: toast-fade-in 0.3s ease-out;
  pointer-events: auto;
}

/* 位置样式 */
.toast-content.top {
  align-self: flex-start;
  margin-top: 100px;
}

.toast-content.center {
  align-self: center;
}

.toast-content.bottom {
  align-self: flex-end;
  margin-bottom: 100px;
}

/* 类型样式 */
.toast-content.success {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.toast-content.error {
  background: linear-gradient(135deg, #f44336, #da190b);
}

.toast-content.warning {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.toast-content.info {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.toast-icon {
  margin-bottom: 8px;
}

.icon-text {
  font-size: 20px;
  font-weight: bold;
}

.toast-text {
  color: inherit;
  word-break: break-all;
}

/* 动画 */
@keyframes toast-fade-in {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 只有图标的样式 */
.toast-content.icon-only {
  flex-direction: row;
  padding: 12px 16px;
}

.toast-content.icon-only .toast-icon {
  margin-bottom: 0;
  margin-right: 8px;
} 