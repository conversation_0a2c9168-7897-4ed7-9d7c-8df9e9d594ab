const globalState = require('../../utils/global-state')
const errorHandler = require('../../utils/error-handler')

Page({
  data: {
    name: '',
    gender: '男',
    zodiacList: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],
    zodiacIndex: null,
    numberList: Array.from({length: 62}, (_, i) => i + 1),
    numberIndex: null,
    dateTime: [],
    dateTimeArray: [],
    selectedDateTime: '',
    years: [],
    months: [],
    days: [],
    hours: [],
    minutes: [],
    canSubmit: false
  },

  onLoad() {
    // 初始化日期时间选择器数据
    this.initDateTimePickerData();

    // 从全局状态管理器读取出生信息
    this.loadExistingBirthInfo();
  },

  // 加载已有的出生信息
  loadExistingBirthInfo() {
    const birthInfo = globalState.getState('birthInfo');
    if (birthInfo) {
      this.setData({
        name: birthInfo.name || '',
        gender: birthInfo.gender || '男',
        zodiacIndex: birthInfo.zodiacIndex || null,
        numberIndex: birthInfo.numberIndex || null,
        selectedDateTime: birthInfo.selectedDateTime || ''
      });

      // 如果有已保存的日期时间，设置选择器的值
      if (birthInfo.dateTimeArray) {
        this.setData({
          dateTimeArray: birthInfo.dateTimeArray
        });
      }

      // 检查是否可以提交
      this.checkCanSubmit();
    }
  },

  initDateTimePickerData() {
    const date = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 生成年份列表（从1900年到当前年份）
    for (let i = 1900; i <= date.getFullYear(); i++) {
      years.push(i + '年');
    }

    // 生成月份列表
    for (let i = 1; i <= 12; i++) {
      months.push(i + '月');
    }

    // 生成天数列表（默认31天）
    for (let i = 1; i <= 31; i++) {
      days.push(i + '日');
    }

    // 生成小时列表
    for (let i = 0; i < 24; i++) {
      hours.push(i + '时');
    }

    // 生成分钟列表
    for (let i = 0; i < 60; i++) {
      minutes.push(i + '分');
    }

    this.setData({
      dateTime: [years, months, days, hours, minutes],
      years,
      months,
      days,
      hours,
      minutes,
      dateTimeArray: [0, 0, 0, 0, 0] // 默认选中当前时间
    });
  },

  updateDays(year, month) {
    const days = [];
    const daysInMonth = new Date(year, month, 0).getDate();
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i + '日');
    }
    return days;
  },

  onDateTimeColumnChange(e) {
    const { column, value } = e.detail;
    const { dateTime, dateTimeArray, years, months } = this.data;
    const newDateTimeArray = [...dateTimeArray];
    newDateTimeArray[column] = value;

    // 如果修改了年份或月份，需要更新天数
    if (column === 0 || column === 1) {
      const year = parseInt(years[newDateTimeArray[0]]);
      const month = parseInt(months[newDateTimeArray[1]]);
      const newDays = this.updateDays(year, month);
      const newDateTime = [...dateTime];
      newDateTime[2] = newDays;
      
      this.setData({
        dateTime: newDateTime
      });
    }

    this.setData({
      dateTimeArray: newDateTimeArray
    });
  },

  onDateTimeChange(e) {
    const { value } = e.detail;
    const { dateTime } = this.data;

    const selectedDateTime = dateTime.map((arr, index) => arr[value[index]]).join('');

    this.setData({
      dateTimeArray: value,
      selectedDateTime
    });

    // 检查是否可以提交
    this.checkCanSubmit();
  },

  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 新的性别选择方法
  selectGender(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      gender
    });
    this.checkCanSubmit();
  },

  // 兼容旧的性别选择方法
  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    });
    this.checkCanSubmit();
  },

  onZodiacChange(e) {
    this.setData({
      zodiacIndex: parseInt(e.detail.value)
    });
  },

  onNumberChange(e) {
    this.setData({
      numberIndex: parseInt(e.detail.value)
    });
    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { name, selectedDateTime } = this.data;
    const canSubmit = name.trim() && selectedDateTime;
    this.setData({ canSubmit });
  },

  saveInfo() {
    const {
      name,
      gender,
      zodiacIndex,
      numberIndex,
      zodiacList,
      numberList,
      selectedDateTime,
      dateTime,
      dateTimeArray,
      canSubmit
    } = this.data;

    // 检查是否可以提交
    if (!canSubmit) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    if (!name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    if (!selectedDateTime) {
      wx.showToast({
        title: '请选择出生日期时间',
        icon: 'none'
      });
      return;
    }

    try {
      // 构建出生信息对象
      const birthInfo = {
        name: name.trim(),
        gender,
        zodiacIndex,
        numberIndex,
        zodiac: zodiacIndex !== null ? zodiacList[zodiacIndex] : null,
        number: numberIndex !== null ? numberList[numberIndex] : null,
        selectedDateTime,
        dateTimeArray,
        dateTime: dateTime.map((arr, index) => arr[dateTimeArray[index]]).join(''),
        timestamp: new Date().getTime()
      };

      console.log('保存的出生信息:', birthInfo);

      // 使用全局状态管理器保存
      globalState.updateBirthInfo(birthInfo);

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      });

      // 获取目标页面路径
      const targetPage = wx.getStorageSync('targetPage');
      if (targetPage) {
        // 清除目标页面路径
        wx.removeStorageSync('targetPage');
        // 延迟跳转，让用户看到保存成功的提示
        setTimeout(() => {
          // 检查目标页面是否为TabBar页面
          const tabBarPages = [
            '/pages/index/index',
            '/pages/ai-chat/ai-chat',
            '/pages/profile/profile'
          ];

          if (tabBarPages.includes(targetPage)) {
            wx.switchTab({
              url: targetPage,
              fail: () => {
                // 如果switchTab失败，尝试redirectTo
                wx.redirectTo({ url: targetPage });
              }
            });
          } else {
            wx.redirectTo({
              url: targetPage,
              fail: () => {
                // 如果redirectTo失败，尝试navigateTo
                wx.navigateTo({ url: targetPage });
              }
            });
          }
        }, 1500);
      } else {
        // 如果没有目标页面，返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('保存出生信息失败:', error);
      errorHandler.handle(error, {
        customMessage: '保存失败，请重试'
      });
    }
  }
}) 