<!--ziwei.wxml-->
<view class="container">
  <!-- 使用出生信息组件 -->
  <birth-info bind:change="onBirthInfoChange"></birth-info>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>正在计算紫微斗数...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error" wx:if="{{error}}">
    <text>{{error}}</text>
  </view>
  
  <!-- 紫微斗数命盘展示 -->
  <view class="result-section" wx:if="{{ziweiResult}}">
    <view class="section-title">紫微斗数命盘</view>
    
    <!-- 命盘图 -->
    <view class="chart-section">
      <view class="chart-title">紫微斗数命盘</view>
      <view class="ziwei-chart">
        <view class="chart-grid">
          <view class="grid-cell" wx:for="{{ziweiResult.palaces}}" wx:key="position">
            <view class="palace-name">{{item.name}}</view>
            <view class="main-star" wx:if="{{item.mainStar}}">{{item.mainStar}}</view>
            <view class="minor-stars">
              <text class="star {{star.type === '主星' ? 'star-main' : star.type === '辅星' ? 'star-assistant' : star.type === '吉星' ? 'star-lucky' : 'star-evil'}}" 
                    wx:for="{{item.stars}}" 
                    wx:key="name" 
                    wx:for-item="star">{{star.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 十四主星组合 -->
    <view class="star-combinations-section" wx:if="{{ziweiResult}}">
      <view class="section-title">十四主星组合</view>
      <view class="combinations-list">
        <view class="combination-item" wx:for="{{ziweiResult.starCombinations}}" wx:key="type">
          <view class="combination-title">{{item.type}}</view>
          <view class="combination-stars">星耀：{{item.stars.join('、')}}</view>
          <view class="combination-meaning">{{item.meaning}}</view>
        </view>
      </view>
    </view>
    
    <!-- 人生轨迹预测 -->
    <view class="life-trajectory-section" wx:if="{{ziweiResult}}">
      <view class="section-title">人生轨迹预测</view>
      <view class="trajectory-content">
        <view class="trajectory-item">
          <view class="trajectory-title">事业预测</view>
          <view class="trajectory-text">{{ziweiResult.lifeTrajectory.career}}</view>
          <view class="trajectory-detail">
            <view class="detail-item" wx:for="{{ziweiResult.lifeTrajectory.careerDetails}}" wx:key="index">
              <view class="detail-title">{{item.title}}</view>
              <view class="detail-content">{{item.content}}</view>
            </view>
          </view>
        </view>
        <view class="trajectory-item">
          <view class="trajectory-title">婚姻预测</view>
          <view class="trajectory-text">{{ziweiResult.lifeTrajectory.marriage}}</view>
          <view class="trajectory-detail">
            <view class="detail-item" wx:for="{{ziweiResult.lifeTrajectory.marriageDetails}}" wx:key="index">
              <view class="detail-title">{{item.title}}</view>
              <view class="detail-content">{{item.content}}</view>
            </view>
          </view>
        </view>
        <view class="trajectory-item">
          <view class="trajectory-title">财运预测</view>
          <view class="trajectory-text">{{ziweiResult.lifeTrajectory.wealth}}</view>
          <view class="trajectory-detail">
            <view class="detail-item" wx:for="{{ziweiResult.lifeTrajectory.wealthDetails}}" wx:key="index">
              <view class="detail-title">{{item.title}}</view>
              <view class="detail-content">{{item.content}}</view>
            </view>
          </view>
        </view>
        <view class="trajectory-item">
          <view class="trajectory-title">健康预测</view>
          <view class="trajectory-text">{{ziweiResult.lifeTrajectory.health}}</view>
          <view class="trajectory-detail">
            <view class="detail-item" wx:for="{{ziweiResult.lifeTrajectory.healthDetails}}" wx:key="index">
              <view class="detail-title">{{item.title}}</view>
              <view class="detail-content">{{item.content}}</view>
            </view>
          </view>
        </view>
        <view class="trajectory-item">
          <view class="trajectory-title">大限流年</view>
          <view class="fortune-scroll">
            <view class="fortune-list">
              <view class="fortune-item" wx:for="{{ziweiResult.lifeTrajectory.fortuneYears}}" wx:key="age">
                <view class="fortune-age">{{item.age}}岁</view>
                <view class="fortune-year">{{item.year}}年</view>
                <view class="fortune-palace">{{item.palace}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 命理解读 -->
    <view class="interpretation-section">
      <view class="section-title">命理解读</view>
      <view class="interpretation-content">
        <view class="interpretation-item">
          <view class="item-title">总体解读</view>
          <view class="item-content">{{ziweiResult.interpretation.general}}</view>
        </view>
        <view class="interpretation-item">
          <view class="item-title">宫位解读</view>
          <view class="item-content" wx:for="{{ziweiResult.interpretation.palaces}}" wx:key="palace">
            <text class="palace-name">{{item.palace}}：</text>
            <text class="palace-stars">{{item.stars}}</text>
            <text class="palace-meaning">{{item.meaning}}</text>
          </view>
        </view>
        <view class="interpretation-item">
          <view class="item-title">建议</view>
          <view class="item-content">
            <view class="advice-item">
              <text class="advice-title">事业：</text>
              <text class="advice-content">{{ziweiResult.interpretation.advice.career}}</text>
            </view>
            <view class="advice-item">
              <text class="advice-title">感情：</text>
              <text class="advice-content">{{ziweiResult.interpretation.advice.relationship}}</text>
            </view>
            <view class="advice-item">
              <text class="advice-title">财运：</text>
              <text class="advice-content">{{ziweiResult.interpretation.advice.wealth}}</text>
            </view>
            <view class="advice-item">
              <text class="advice-title">健康：</text>
              <text class="advice-content">{{ziweiResult.interpretation.advice.health}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 