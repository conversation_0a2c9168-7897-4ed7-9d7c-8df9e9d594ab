// 五行生克关系
const WU_XING_RELATIONS = {
  '金': { generates: '水', restricts: '木' },
  '木': { generates: '火', restricts: '土' },
  '水': { generates: '木', restricts: '火' },
  '火': { generates: '土', restricts: '金' },
  '土': { generates: '金', restricts: '水' }
}

// 天干地支五行属性
const TIAN_GAN_WU_XING = {
  '甲': '木', '乙': '木',
  '丙': '火', '丁': '火',
  '戊': '土', '己': '土',
  '庚': '金', '辛': '金',
  '壬': '水', '癸': '水'
}

const DI_ZHI_WU_XING = {
  '寅': '木', '卯': '木',
  '巳': '火', '午': '火',
  '辰': '土', '戌': '土', '丑': '土', '未': '土',
  '申': '金', '酉': '金',
  '亥': '水', '子': '水'
}

// 五行相生相克关系
const WUXING_RELATIONS = {
  metal: { generates: 'water', restricts: 'wood' },
  water: { generates: 'wood', restricts: 'fire' },
  wood: { generates: 'fire', restricts: 'earth' },
  fire: { generates: 'earth', restricts: 'metal' },
  earth: { generates: 'metal', restricts: 'water' }
};

// 天干地支相合关系
const STEM_COMBINATIONS = {
  '甲己': 5, '乙庚': 5, '丙辛': 5, '丁壬': 5, '戊癸': 5
};

const BRANCH_COMBINATIONS = {
  '子丑': 4, '寅亥': 4, '卯戌': 4, '辰酉': 4, '巳申': 4, '午未': 4
};

class HehunCalculator {
  constructor(maleBazi, femaleBazi) {
    this.maleBazi = maleBazi
    this.femaleBazi = femaleBazi
    this.wuxingRelations = WUXING_RELATIONS;
    this.stemCombinations = STEM_COMBINATIONS;
    this.branchCombinations = BRANCH_COMBINATIONS;
  }

  async calculate() {
    try {
      // 1. 计算八字相合度
      const baziScore = this.calculateBaziScore()
      
      // 2. 计算五行相合度
      const wuxingScore = this.calculateWuxingScore()
      
      // 3. 分析相生相克
      const relationScore = this.calculateRelationScore()
      
      // 4. 计算最终得分
      const totalScore = Math.round((baziScore + wuxingScore + relationScore) / 3)
      
      // 5. 生成分析结果
      const result = {
        score: totalScore,
        description: this.getScoreDescription(totalScore),
        maleBaziStr: this.formatBaziString(this.maleBazi),
        femaleBaziStr: this.formatBaziString(this.femaleBazi),
        baziAnalysis: this.generateBaziAnalysis(),
        wuxingAnalysis: this.generateWuxingAnalysis(),
        wuxingData: this.generateWuxingChartData(),
        adviceList: this.generateAdvice(totalScore)
      }

      return result
    } catch (error) {
      console.error('合婚计算错误:', error)
      throw error
    }
  }

  // 计算八字相合度
  calculateBaziScore() {
    let score = 0
    const maleElements = this.getBaziElements(this.maleBazi)
    const femaleElements = this.getBaziElements(this.femaleBazi)

    // 天干相合
    score += this.calculateGanHe(maleElements.gan, femaleElements.gan)
    
    // 地支相合
    score += this.calculateZhiHe(maleElements.zhi, femaleElements.zhi)
    
    // 日柱相合
    score += this.calculateRiZhuHe(
      maleElements.gan[2], maleElements.zhi[2],
      femaleElements.gan[2], femaleElements.zhi[2]
    )

    return Math.min(100, score)
  }

  // 计算五行相合度
  calculateWuxingScore() {
    let score = 0
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)

    // 相生关系
    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {
      score += 40
    }
    if (WU_XING_RELATIONS[femaleWuxing].generates === maleWuxing) {
      score += 40
    }

    // 相克关系
    if (WU_XING_RELATIONS[maleWuxing].restricts === femaleWuxing) {
      score -= 20
    }
    if (WU_XING_RELATIONS[femaleWuxing].restricts === maleWuxing) {
      score -= 20
    }

    return Math.max(0, Math.min(100, score + 60))
  }

  // 计算相生相克关系得分
  calculateRelationScore() {
    let score = 60 // 基础分
    const maleElements = this.getBaziElements(this.maleBazi)
    const femaleElements = this.getBaziElements(this.femaleBazi)

    // 分析日柱关系
    const maleRiWuxing = TIAN_GAN_WU_XING[maleElements.gan[2]]
    const femaleRiWuxing = TIAN_GAN_WU_XING[femaleElements.gan[2]]

    if (this.isHelpful(maleRiWuxing, femaleRiWuxing)) {
      score += 20
    }
    if (this.isConflict(maleRiWuxing, femaleRiWuxing)) {
      score -= 10
    }

    return Math.max(0, Math.min(100, score))
  }

  // 工具方法
  getBaziElements(bazi) {
    return {
      gan: [bazi.year.gan, bazi.month.gan, bazi.day.gan, bazi.time.gan],
      zhi: [bazi.year.zhi, bazi.month.zhi, bazi.day.zhi, bazi.time.zhi]
    }
  }

  calculateGanHe(maleGan, femaleGan) {
    let score = 0
    // 天干五合
    const ganHe = {
      '甲己': 30, '乙庚': 30, '丙辛': 30, '丁壬': 30, '戊癸': 30
    }

    for (let i = 0; i < 4; i++) {
      const pair = maleGan[i] + femaleGan[i]
      const reversePair = femaleGan[i] + maleGan[i]
      if (ganHe[pair] || ganHe[reversePair]) {
        score += (i === 2) ? 40 : 20 // 日柱合为40分，其他为20分
      }
    }

    return score
  }

  calculateZhiHe(maleZhi, femaleZhi) {
    let score = 0
    // 地支六合
    const zhiHe = {
      '子丑': 20, '寅亥': 20, '卯戌': 20, '辰酉': 20, '巳申': 20, '午未': 20
    }

    for (let i = 0; i < 4; i++) {
      const pair = maleZhi[i] + femaleZhi[i]
      const reversePair = femaleZhi[i] + maleZhi[i]
      if (zhiHe[pair] || zhiHe[reversePair]) {
        score += (i === 2) ? 30 : 15 // 日支合为30分，其他为15分
      }
    }

    return score
  }

  calculateRiZhuHe(maleGan, maleZhi, femaleGan, femaleZhi) {
    let score = 0
    const maleWuxing = TIAN_GAN_WU_XING[maleGan]
    const femaleWuxing = TIAN_GAN_WU_XING[femaleGan]

    // 日柱五行相生
    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {
      score += 30
    }

    // 日支相合
    if (this.isZhiHe(maleZhi, femaleZhi)) {
      score += 20
    }

    return score
  }

  getMainWuxing(bazi) {
    // 获取日柱天干的五行属性作为主五行
    return TIAN_GAN_WU_XING[bazi.day.gan]
  }

  isHelpful(wuxing1, wuxing2) {
    return WU_XING_RELATIONS[wuxing1].generates === wuxing2 ||
           WU_XING_RELATIONS[wuxing2].generates === wuxing1
  }

  isConflict(wuxing1, wuxing2) {
    return WU_XING_RELATIONS[wuxing1].restricts === wuxing2 ||
           WU_XING_RELATIONS[wuxing2].restricts === wuxing1
  }

  isZhiHe(zhi1, zhi2) {
    const zhiHe = {
      '子丑': true, '寅亥': true, '卯戌': true,
      '辰酉': true, '巳申': true, '午未': true
    }
    return zhiHe[zhi1 + zhi2] || zhiHe[zhi2 + zhi1]
  }

  formatBaziString(bazi) {
    return `${bazi.year.gan}${bazi.year.zhi} ${bazi.month.gan}${bazi.month.zhi} ${bazi.day.gan}${bazi.day.zhi} ${bazi.time.gan}${bazi.time.zhi}`
  }

  getScoreDescription(score) {
    if (score >= 90) return '天作之合，百年好合'
    if (score >= 80) return '相配良缘，幸福美满'
    if (score >= 70) return '般配和谐，可结良缘'
    if (score >= 60) return '缘分尚可，需互相包容'
    if (score >= 50) return '缘分一般，需要磨合'
    return '八字相冲，婚姻易有波折'
  }

  generateBaziAnalysis() {
    const maleDay = this.maleBazi.day
    const femaleDay = this.femaleBazi.day
    const maleDayWuxing = TIAN_GAN_WU_XING[maleDay.gan]
    const femaleDayWuxing = TIAN_GAN_WU_XING[femaleDay.gan]

    let analysis = '从八字日柱来看，'
    if (this.isHelpful(maleDayWuxing, femaleDayWuxing)) {
      analysis += '双方八字相生，能够互相扶持，感情基础良好。'
    } else if (this.isConflict(maleDayWuxing, femaleDayWuxing)) {
      analysis += '双方八字有所冲突，需要多加沟通理解，互相包容。'
    } else {
      analysis += '双方八字中性，婚后生活平稳。'
    }

    return analysis
  }

  generateWuxingAnalysis() {
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)

    let analysis = `男方五行属${maleWuxing}，女方五行属${femaleWuxing}。`
    
    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {
      analysis += '男方五行生女方，男方能给予女方支持和关爱。'
    } else if (WU_XING_RELATIONS[femaleWuxing].generates === maleWuxing) {
      analysis += '女方五行生男方，女方能给予男方支持和温暖。'
    } else if (WU_XING_RELATIONS[maleWuxing].restricts === femaleWuxing) {
      analysis += '男方五行克女方，需要男方多关心体贴女方。'
    } else if (WU_XING_RELATIONS[femaleWuxing].restricts === maleWuxing) {
      analysis += '女方五行克男方，需要女方多体谅包容男方。'
    } else {
      analysis += '双方五行平和，婚后生活稳定。'
    }

    return analysis
  }

  generateWuxingChartData() {
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)

    return [
      { name: '金', value: this.calculateWuxingValue('金', maleWuxing, femaleWuxing), color: '#FFD700' },
      { name: '木', value: this.calculateWuxingValue('木', maleWuxing, femaleWuxing), color: '#90EE90' },
      { name: '水', value: this.calculateWuxingValue('水', maleWuxing, femaleWuxing), color: '#87CEEB' },
      { name: '火', value: this.calculateWuxingValue('火', maleWuxing, femaleWuxing), color: '#FF6B6B' },
      { name: '土', value: this.calculateWuxingValue('土', maleWuxing, femaleWuxing), color: '#DEB887' }
    ]
  }

  calculateWuxingValue(wuxing, maleWuxing, femaleWuxing) {
    let value = 20 // 基础值
    if (wuxing === maleWuxing) value += 30
    if (wuxing === femaleWuxing) value += 30
    if (WU_XING_RELATIONS[wuxing].generates === maleWuxing) value += 10
    if (WU_XING_RELATIONS[wuxing].generates === femaleWuxing) value += 10
    return value
  }

  generateAdvice(score) {
    const adviceList = []

    // 婚期选择
    adviceList.push({
      title: '婚期选择',
      content: this.getWeddingDateAdvice()
    })

    // 婚后相处
    adviceList.push({
      title: '婚后相处',
      content: this.getRelationshipAdvice(score)
    })

    // 事业发展
    adviceList.push({
      title: '事业发展',
      content: this.getCareerAdvice()
    })

    // 家庭和谐
    adviceList.push({
      title: '家庭和谐',
      content: this.getFamilyAdvice()
    })

    return adviceList
  }

  getWeddingDateAdvice() {
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)
    
    let advice = '建议选择'
    if (this.isHelpful(maleWuxing, femaleWuxing)) {
      advice += '双方五行相生的月份，如'
      switch(maleWuxing) {
        case '金': advice += '农历七月、八月'; break
        case '木': advice += '农历三月、四月'; break
        case '水': advice += '农历十一月、十二月'; break
        case '火': advice += '农历五月、六月'; break
        case '土': advice += '农历九月、十月'; break
      }
    } else {
      advice += '五行能量旺盛的月份，避开双方五行相克的时节'
    }
    advice += '举办婚礼。'
    return advice
  }

  getRelationshipAdvice(score) {
    if (score >= 80) {
      return '双方八字相合，婚后感情和睦。建议保持真诚沟通，互相理解，让感情升温。'
    } else if (score >= 60) {
      return '婚后生活平稳，需要双方互相包容。建议多创造共处时光，增进感情交流。'
    } else {
      return '婚后可能面临挑战，建议增进沟通理解，学会换位思考，共同面对困难。'
    }
  }

  getCareerAdvice() {
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)

    if (this.isHelpful(maleWuxing, femaleWuxing)) {
      return '双方在事业上能够互相扶持，建议可以考虑合作发展，共同创业。'
    } else if (this.isConflict(maleWuxing, femaleWuxing)) {
      return '建议在事业上保持适当独立，各自发展所长，互不干扰。'
    } else {
      return '可以在事业上互相支持，但要注意维护各自的发展空间。'
    }
  }

  getFamilyAdvice() {
    const maleWuxing = this.getMainWuxing(this.maleBazi)
    const femaleWuxing = this.getMainWuxing(this.femaleBazi)

    let advice = '为了家庭和谐，建议'
    if (this.isHelpful(maleWuxing, femaleWuxing)) {
      advice += '双方发挥各自优势，互相配合，共同营造温馨的家庭氛围。'
    } else if (this.isConflict(maleWuxing, femaleWuxing)) {
      advice += '适当调节各自性格，学会互相迁就，共同维护家庭和睦。'
    } else {
      advice += '保持良好的沟通习惯，共同参与家庭事务，增进感情交流。'
    }
    return advice
  }
}

module.exports = {
  HehunCalculator
} 