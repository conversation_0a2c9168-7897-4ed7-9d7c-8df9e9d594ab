.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.question-section {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.question-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.divine-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4a5568;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  transition: all 0.3s ease;
}

.divine-button.loading {
  background: #718096;
  opacity: 0.8;
}

.result-section {
  margin-top: 40rpx;
}

.hexagram-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.hexagram-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
  text-align: center;
}

.hexagram-content {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.interpretation {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
  white-space: pre-wrap;
}

.footer {
  margin-top: 40rpx;
  text-align: center;
}

.disclaimer {
  font-size: 24rpx;
  color: #718096;
} 