// wuxing.js
const app = getApp()
const { WuxingCalculator } = require('../../utils/wuxing')
const interpretationService = require('../../utils/interpretation')
const { checkBirthInfo, navigateToBirthInfo } = require('../../utils/util.js')

Page({
  data: {
    birthDate: '',
    birthTime: '',
    gender: '男',
    isLoading: false,
    wuxingResult: null,
    loading: false
  },

  onLoad() {
    // 检查是否有出生信息
    if (!checkBirthInfo()) {
      navigateToBirthInfo('/pages/wuxing/wuxing')
      return
    }
    try {
      // 加载已保存的出生信息
      const birthInfo = wx.getStorageSync('birthInfo')
      if (birthInfo) {
        this.setData({
          birthDate: birthInfo.birthDate,
          birthTime: birthInfo.birthTime,
          gender: birthInfo.gender
        })
        // 自动计算五行
        this.calculateWuxing()
      }
    } catch (error) {
      console.error('加载出生信息失败:', error)
    }
  },

  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    })
  },

  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    })
  },

  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    })
  },

  calculateWuxing() {
    const { birthDate, birthTime } = this.data;
    
    if (!birthDate || !birthTime) {
      wx.showToast({
        title: '请选择出生日期和时间',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const calculator = new WuxingCalculator();
      const date = new Date(birthDate);
      
      // 计算五行强度
      const strengths = calculator.calculateStrength(date, birthTime);
      
      // 获取分析结果
      const analysis = calculator.getAnalysis(strengths);
      
      // 获取五行关系
      const relationships = calculator.getRelationships(strengths);
      
      // 处理分布数据
      const distribution = Object.entries(strengths).map(([element, strength]) => ({
        element: calculator.wuxing[element].name,
        strength: strength
      })).sort((a, b) => b.strength - a.strength);

      this.setData({
        wuxingResult: {
          distribution,
          analysis,
          relationships
        },
        loading: false
      });
    } catch (error) {
      console.error('五行计算错误:', error);
      wx.showToast({
        title: '计算失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 获取相生关系
  getShengRelations(wuxing) {
    const relations = {
      JIN: '金生水',
      SHUI: '水生木',
      MU: '木生火',
      HUO: '火生土',
      TU: '土生金'
    }
    return relations[wuxing]
  },

  // 获取相克关系
  getKeRelations(wuxing) {
    const relations = {
      JIN: '金克木',
      MU: '木克土',
      TU: '土克水',
      SHUI: '水克火',
      HUO: '火克金'
    }
    return relations[wuxing]
  },

  onShareAppMessage() {
    return {
      title: '五行分析',
      path: '/pages/wuxing/wuxing',
      imageUrl: '/assets/images/share.png'
    }
  },

  /**
   * 处理出生信息保存事件
   */
  onBirthInfoSave(e) {
    const { birthDate, birthTime } = e.detail;
    this.setData({ birthDate, birthTime });
    this.calculateWuxing();
  }
}) 