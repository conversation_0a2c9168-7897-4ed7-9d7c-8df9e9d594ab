# AI聊天API接口文档

## 目录
1. [接口概述](#接口概述)
2. [接口详情](#接口详情)
3. [错误处理](#错误处理)
4. [示例代码](#示例代码)
5. [安全说明](#安全说明)

## 接口概述

### 基础信息
- 基础URL: `http://localhost:8000` (开发环境)
- 接口版本: v1
- 数据格式: JSON
- 字符编码: UTF-8

### 通用请求头
```http
Content-Type: application/json
Accept: application/json
```

## 接口详情

### 1. 发送消息
#### 请求
- 路径: `/api/chat/send`
- 方法: POST
- 描述: 发送用户消息并获取AI回复

#### 请求参数
```json
{
  "message": "string",     // 用户输入的消息
  "context": [             // 对话上下文（最近5条）
    {
      "role": "string",    // 角色：user或assistant
      "content": "string"  // 消息内容
    }
  ]
}
```

#### 响应
```json
{
  "code": 200,            // 状态码
  "message": "success",   // 状态信息
  "data": {
    "response": "string", // AI回复内容
    "metadata": {         // 元数据（可选）
      "tokens": 123,      // 使用的token数
      "model": "string"   // 使用的模型
    }
  }
}
```

### 2. 获取历史消息
#### 请求
- 路径: `/api/chat/history`
- 方法: GET
- 描述: 获取历史对话记录

#### 请求参数
```
?page=1&size=20&userId=xxx
```

#### 响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": "string",
        "role": "string",
        "content": "string",
        "timestamp": "string",
        "metadata": {}
      }
    ]
  }
}
```

## 错误处理

### 错误码说明
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 检查认证信息 |
| 403 | 禁止访问 | 检查权限 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器错误 | 联系管理员 |

### 错误响应格式
```json
{
  "code": 400,
  "message": "错误信息",
  "errors": [
    {
      "field": "message",
      "error": "消息不能为空"
    }
  ]
}
```

## 示例代码

### 1. 发送消息
```javascript
// 使用Promise
function sendMessage(message, context) {
  return wx.request({
    url: `${config.apiUrl}/api/chat/send`,
    method: 'POST',
    data: { message, context },
    header: {
      'content-type': 'application/json'
    }
  })
}

// 使用async/await
async function sendMessageAsync(message, context) {
  try {
    const response = await wx.request({
      url: `${config.apiUrl}/api/chat/send`,
      method: 'POST',
      data: { message, context }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data.message)
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    throw error
  }
}
```

### 2. 获取历史消息
```javascript
async function getHistory(page = 1, size = 20) {
  try {
    const response = await wx.request({
      url: `${config.apiUrl}/api/chat/history`,
      method: 'GET',
      data: { page, size }
    })
    
    return response.data
  } catch (error) {
    console.error('获取历史记录失败:', error)
    throw error
  }
}
```

## 安全说明

### 1. 请求频率限制
- 单个用户每分钟最多发送30条消息
- 超过限制将返回429错误
- 建议实现指数退避重试

### 2. 内容安全
- 所有用户输入需进行过滤和验证
- 敏感信息不应在前端存储
- 遵循最小权限原则

### 3. 认证和授权
```javascript
// 添加认证头
const headers = {
  'content-type': 'application/json',
  'Authorization': `Bearer ${token}`
}

// 请求示例
wx.request({
  url: `${config.apiUrl}/api/chat/send`,
  method: 'POST',
  data: data,
  header: headers
})
```

### 4. 数据加密
- 生产环境必须使用HTTPS
- 敏感数据传输时需要加密
- 存储的数据需要加密

## 开发建议

1. 错误处理
```javascript
try {
  const response = await sendMessage(message)
  handleSuccess(response)
} catch (error) {
  if (error.code === 429) {
    await wait(1000)  // 等待1秒后重试
    return sendMessage(message)
  }
  handleError(error)
}
```

2. 重试机制
```javascript
async function sendWithRetry(message, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await sendMessage(message)
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await wait(Math.pow(2, i) * 1000)
    }
  }
}
```

3. 消息队列
```javascript
class MessageQueue {
  constructor() {
    this.queue = []
    this.processing = false
  }

  async add(message) {
    this.queue.push(message)
    if (!this.processing) {
      this.process()
    }
  }

  async process() {
    this.processing = true
    while (this.queue.length > 0) {
      const message = this.queue.shift()
      try {
        await sendMessage(message)
      } catch (error) {
        console.error('处理消息失败:', error)
      }
    }
    this.processing = false
  }
}
```

4. 日志记录
```javascript
function logRequest(url, data, response) {
  console.log({
    timestamp: new Date().toISOString(),
    url,
    request: data,
    response: response.data,
    status: response.statusCode
  })
}
``` 