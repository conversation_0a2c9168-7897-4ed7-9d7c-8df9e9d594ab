<view class="container">
  <view class="header">
    <text class="title">姓名测算</text>
    <text class="subtitle">解析姓名中的天机玄机</text>
  </view>

  <view class="form-section">
    <view class="form-item">
      <text class="label">姓氏</text>
      <input type="text" class="input" placeholder="请输入姓氏" value="{{surname}}" bindinput="onSurnameInput"/>
    </view>

    <view class="form-item">
      <text class="label">名字</text>
      <input type="text" class="input" placeholder="请输入名字" value="{{givenName}}" bindinput="onGivenNameInput"/>
    </view>

    <view class="form-item">
      <text class="label">性别</text>
      <radio-group class="radio-group" bindchange="onGenderChange">
        <label class="radio">
          <radio value="male" checked="{{gender === 'male'}}"/>男
        </label>
        <label class="radio">
          <radio value="female" checked="{{gender === 'female'}}"/>女
        </label>
      </radio-group>
    </view>

    <view class="form-item">
      <text class="label">出生日期</text>
      <picker mode="date" value="{{birthDate}}" bindchange="onBirthDateChange">
        <view class="picker">{{birthDate || '请选择出生日期'}}</view>
      </picker>
    </view>
  </view>

  <button class="analyze-btn" bindtap="analyzeName" loading="{{loading}}">
    开始测算
  </button>

  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="card-title">五格数理</view>
      <view class="wuge-grid">
        <view class="grid-item" wx:for="{{wugeResult}}" wx:key="name">
          <text class="grid-name">{{item.name}}</text>
          <text class="grid-number">{{item.number}}</text>
          <text class="grid-meaning">{{item.meaning}}</text>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">姓名评分</view>
      <view class="score-section">
        <text class="total-score">{{nameScore}}</text>
        <text class="max-score">/100</text>
        <text class="score-desc">{{scoreDescription}}</text>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">五行分析</view>
      <view class="wuxing-analysis">
        <view class="wuxing-item" wx:for="{{wuxingResult}}" wx:key="element">
          <text class="element-name">{{item.element}}</text>
          <progress percent="{{item.percentage}}" stroke-width="12" color="{{item.color}}"/>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">姓名详解</view>
      <view class="name-details">
        <view class="detail-item" wx:for="{{nameDetails}}" wx:key="aspect">
          <text class="aspect-name">{{item.aspect}}</text>
          <text class="aspect-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">运势分析</view>
      <view class="fortune-analysis">
        <view class="fortune-item" wx:for="{{fortuneResult}}" wx:key="aspect">
          <text class="aspect-name">{{item.aspect}}</text>
          <view class="stars">
            <text class="star" wx:for="{{item.stars}}" wx:key="index">★</text>
          </view>
        </view>
      </view>
    </view>

    <view class="result-card">
      <view class="card-title">改名建议</view>
      <view class="name-suggestions">
        <view class="suggestion-item" wx:for="{{suggestions}}" wx:key="name">
          <text class="suggested-name">{{item.name}}</text>
          <text class="suggestion-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 