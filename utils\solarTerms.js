// 节气数据管理
const solarTerms = [
  { name: '立春', month: 2, day: 4 },
  { name: '雨水', month: 2, day: 19 },
  { name: '惊蛰', month: 3, day: 6 },
  { name: '春分', month: 3, day: 21 },
  { name: '清明', month: 4, day: 5 },
  { name: '谷雨', month: 4, day: 20 },
  { name: '立夏', month: 5, day: 6 },
  { name: '小满', month: 5, day: 21 },
  { name: '芒种', month: 6, day: 6 },
  { name: '夏至', month: 6, day: 21 },
  { name: '小暑', month: 7, day: 7 },
  { name: '大暑', month: 7, day: 23 },
  { name: '立秋', month: 8, day: 8 },
  { name: '处暑', month: 8, day: 23 },
  { name: '白露', month: 9, day: 8 },
  { name: '秋分', month: 9, day: 23 },
  { name: '寒露', month: 10, day: 8 },
  { name: '霜降', month: 10, day: 24 },
  { name: '立冬', month: 11, day: 8 },
  { name: '小雪', month: 11, day: 22 },
  { name: '大雪', month: 12, day: 7 },
  { name: '冬至', month: 12, day: 22 },
  { name: '小寒', month: 1, day: 6 },
  { name: '大寒', month: 1, day: 20 }
];

// 节气描述
const solarTermsDesc = {
  '立春': '春回大地，万物复苏',
  '雨水': '细雨润物，春意渐浓',
  '惊蛰': '春雷惊蛰，万物苏醒',
  '春分': '昼夜平分，阴阳调和',
  '清明': '天清地明，祭祀扫墓',
  '谷雨': '雨生百谷，万物生长',
  '立夏': '夏季开始，草木繁茂',
  '小满': '麦粒饱满，春意渐浓',
  '芒种': '播种芒种，农事繁忙',
  '夏至': '日长夜短，阳气至极',
  '小暑': '暑气渐盛，天气炎热',
  '大暑': '暑气正盛，炎热至极',
  '立秋': '秋季开始，暑气渐消',
  '处暑': '暑气渐退，秋高气爽',
  '白露': '露气渐重，秋意渐浓',
  '秋分': '昼夜平分，秋收开始',
  '寒露': '露气渐寒，秋意正浓',
  '霜降': '霜气降临，秋季将尽',
  '立冬': '冬季开始，万物收藏',
  '小雪': '雪气渐盛，天气渐寒',
  '大雪': '雪气正盛，天寒地冻',
  '冬至': '日短夜长，阳气生发',
  '小寒': '寒气渐盛，天气寒冷',
  '大寒': '寒气正盛，严寒至极'
};

// 获取今日节气信息
const getTodaySolarTerm = () => {
  const today = new Date();
  const month = today.getMonth() + 1;
  const day = today.getDate();

  const todaySolarTerm = solarTerms.find(term => term.month === month && term.day === day);
  
  if (todaySolarTerm) {
    return {
      isToday: true,
      name: todaySolarTerm.name,
      description: solarTermsDesc[todaySolarTerm.name]
    };
  }

  // 获取最近的下一个节气
  const nextSolarTerm = solarTerms.find(term => {
    if (term.month > month || (term.month === month && term.day > day)) {
      return true;
    }
    return false;
  }) || solarTerms[0]; // 如果没有找到，说明是年末，返回下一年第一个节气

  return {
    isToday: false,
    name: nextSolarTerm.name,
    description: solarTermsDesc[nextSolarTerm.name],
    daysUntil: calculateDaysUntil(month, day, nextSolarTerm.month, nextSolarTerm.day)
  };
};

// 计算距离下一个节气的天数
const calculateDaysUntil = (currentMonth, currentDay, targetMonth, targetDay) => {
  const today = new Date();
  const year = today.getFullYear();
  const target = new Date(year, targetMonth - 1, targetDay);
  
  if (target < today) {
    target.setFullYear(year + 1);
  }
  
  const diffTime = Math.abs(target - today);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

module.exports = {
  getTodaySolarTerm
}; 