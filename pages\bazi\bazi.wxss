page {
  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);
}

.container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

/* 输入区域样式 */
.input-section {
  background-color: var(--primary-color);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin: 40rpx 0 25rpx;
  padding-left: 20rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 70%;
  background: linear-gradient(180deg, #9575cd 0%, #7e57c2 100%);
  border-radius: 3rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.input-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.picker-value {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: var(--primary-lightest);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.radio-label {
  margin-right: 30rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.calculate-btn {
  margin-top: 30rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 结果区域样式 */
.result-section {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

/* 八字图表样式 */
.bazi-chart {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.pillar-row {
  display: flex;
  justify-content: space-around;
}

.pillar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.pillar-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.pillar-content {
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 8rpx rgba(149, 117, 205, 0.2);
}

.hidden-stem {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

.nayin {
  font-size: 24rpx;
  color: var(--text-light);
  margin-top: 4rpx;
}

/* 五行分析样式 */
.wuxing-analysis {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.wuxing-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.wuxing-item:last-child {
  margin-bottom: 0;
}

.wuxing-label {
  width: 80rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.wuxing-bar {
  flex: 1;
  height: 20rpx;
  background: #f5f0ff;
  border-radius: 10rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.wuxing-progress {
  height: 100%;
  background: linear-gradient(90deg, #b39ddb 0%, #9575cd 100%);
  border-radius: 10rpx;
  transition: width 0.6s ease;
}

.wuxing-value {
  width: 80rpx;
  text-align: right;
  font-size: 26rpx;
  color: #666;
}

/* 十神格局样式 */
.shishen-section {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.pattern-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #9575cd;
  margin-bottom: 15rpx;
  text-align: center;
}

.pattern-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f5ff;
  border-radius: 15rpx;
}

.shishen-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.detail-item {
  background: #f5f0ff;
  padding: 20rpx;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
}

.detail-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 32rpx;
  color: #9575cd;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.detail-meaning {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 喜用神分析样式 */
.xiyong-section {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.favorable-section,
.unfavorable-section,
.advice-section {
  margin-bottom: 30rpx;
}

.favorable-section:last-child,
.unfavorable-section:last-child,
.advice-section:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  padding-left: 15rpx;
  position: relative;
}

.subsection-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 60%;
  background: #9575cd;
  border-radius: 2rpx;
}

.element-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.element-item {
  padding: 15rpx 25rpx;
  background: #f5f0ff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.element-item:last-child {
  margin-bottom: 0;
}

.element-name {
  font-size: 28rpx;
  color: #9575cd;
  font-weight: 500;
}

.element-reason {
  font-size: 24rpx;
  color: #666;
}

.advice-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  padding: 20rpx;
  background: #f8f5ff;
  border-radius: 15rpx;
}

/* 命理解读样式 */
.interpretation {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.interpretation-item {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.interpretation-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.interpretation-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.interpretation-content {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

/* 大运流年样式 */
.fortune {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx 0;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  margin-bottom: 30rpx;
}

.fortune-scroll {
  white-space: nowrap;
  padding: 0 30rpx;
}

.fortune-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
  padding: 20rpx;
  margin-right: 20rpx;
  background: #f5f0ff;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.fortune-item:active {
  transform: scale(0.95);
  background: #ece7f5;
}

.fortune-age {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.fortune-year {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.fortune-pillar {
  font-size: 32rpx;
  color: #9575cd;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.fortune-shishen {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.fortune-luck {
  font-size: 28rpx;
  font-weight: bold;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.fortune-luck.ji {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
}

.fortune-luck.xiong {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.fortune-luck.ping {
  color: #ffa726;
  background: rgba(255, 167, 38, 0.1);
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.tip-text {
  font-size: 32rpx;
  color: #999;
  text-align: center;
  line-height: 1.8;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f5f0ff;
  border-top-color: #9575cd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #666;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
}

.error-text {
  font-size: 30rpx;
  color: #ff6b6b;
  text-align: center;
  margin-bottom: 40rpx;
}

.retry-button {
  padding: 20rpx 60rpx;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.3);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 通用样式 */
view {
  box-sizing: border-box;
} 