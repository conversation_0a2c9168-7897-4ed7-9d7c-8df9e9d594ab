/**
 * 项目集成完整性检查脚本
 * 检查页面间功能衔接、业务逻辑完整性、配置一致性等
 */

const fs = require('fs');
const path = require('path');

class IntegrationChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.projectRoot = process.cwd();
  }

  /**
   * 运行完整性检查
   */
  async run() {
    console.log('🔍 开始项目集成完整性检查...\n');

    try {
      await this.checkAppConfig();
      await this.checkTabBarIntegration();
      await this.checkPageRoutes();
      await this.checkNavigationConsistency();
      await this.checkStateManagement();
      await this.checkErrorHandling();
      await this.checkAssetReferences();
      await this.checkBusinessLogic();

      this.generateReport();
    } catch (error) {
      console.error('❌ 检查过程中发生错误:', error);
    }
  }

  /**
   * 检查应用配置
   */
  async checkAppConfig() {
    console.log('📋 检查应用配置...');

    const appJsonPath = path.join(this.projectRoot, 'app.json');
    const projectConfigPath = path.join(this.projectRoot, 'project.config.json');

    if (!fs.existsSync(appJsonPath)) {
      this.errors.push('app.json 文件不存在');
      return;
    }

    if (!fs.existsSync(projectConfigPath)) {
      this.errors.push('project.config.json 文件不存在');
      return;
    }

    try {
      const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
      const projectConfig = JSON.parse(fs.readFileSync(projectConfigPath, 'utf8'));

      // 检查TabBar配置
      if (appConfig.tabBar && appConfig.tabBar.custom) {
        const customTabBarPath = path.join(this.projectRoot, 'custom-tab-bar');
        if (!fs.existsSync(customTabBarPath)) {
          this.errors.push('自定义TabBar目录不存在');
        }
      }

      // 检查企业微信配置
      if (appConfig.wxwork && appConfig.wxwork.enable) {
        if (!projectConfig.wxwork || !projectConfig.wxwork.enable) {
          this.warnings.push('app.json中启用了企业微信，但project.config.json中未配置');
        }
      }

      console.log('✅ 应用配置检查完成');
    } catch (error) {
      this.errors.push(`解析配置文件失败: ${error.message}`);
    }
  }

  /**
   * 检查TabBar集成
   */
  async checkTabBarIntegration() {
    console.log('📱 检查TabBar集成...');

    const customTabBarFiles = [
      'custom-tab-bar/index.js',
      'custom-tab-bar/index.json',
      'custom-tab-bar/index.wxml',
      'custom-tab-bar/index.wxss'
    ];

    for (const file of customTabBarFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (!fs.existsSync(filePath)) {
        this.errors.push(`自定义TabBar文件缺失: ${file}`);
      }
    }

    // 检查TabBar页面的setSelected调用
    const tabBarPages = ['pages/index/index.js', 'pages/ai-chat/ai-chat.js', 'pages/profile/profile.js'];
    
    for (const pagePath of tabBarPages) {
      const fullPath = path.join(this.projectRoot, pagePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        if (!content.includes('getTabBar') || !content.includes('setData')) {
          this.warnings.push(`${pagePath} 可能缺少TabBar选中状态设置`);
        }
      }
    }

    console.log('✅ TabBar集成检查完成');
  }

  /**
   * 检查页面路由
   */
  async checkPageRoutes() {
    console.log('🛣️ 检查页面路由...');

    const appJsonPath = path.join(this.projectRoot, 'app.json');
    if (!fs.existsSync(appJsonPath)) return;

    try {
      const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
      const pages = appConfig.pages || [];

      for (const page of pages) {
        const pageFiles = [
          `${page}.js`,
          `${page}.json`,
          `${page}.wxml`,
          `${page}.wxss`
        ];

        for (const file of pageFiles) {
          const filePath = path.join(this.projectRoot, file);
          if (!fs.existsSync(filePath)) {
            if (file.endsWith('.js') || file.endsWith('.wxml')) {
              this.errors.push(`关键页面文件缺失: ${file}`);
            } else {
              this.warnings.push(`页面文件缺失: ${file}`);
            }
          }
        }
      }

      console.log('✅ 页面路由检查完成');
    } catch (error) {
      this.errors.push(`检查页面路由失败: ${error.message}`);
    }
  }

  /**
   * 检查导航一致性
   */
  async checkNavigationConsistency() {
    console.log('🧭 检查导航一致性...');

    const navigationUtilPath = path.join(this.projectRoot, 'utils/navigation.js');
    if (!fs.existsSync(navigationUtilPath)) {
      this.warnings.push('导航管理工具不存在，建议使用统一的导航管理');
    }

    // 检查出生信息验证的一致性
    const pagesWithBirthCheck = [
      'pages/index/index.js',
      'pages/ai-chat/ai-chat.js'
    ];

    const birthInfoPages = [
      '/pages/bazi/bazi',
      '/pages/ziwei/ziwei',
      '/pages/wuxing/wuxing',
      '/pages/yijing/yijing',
      '/pages/fortune/fortune',
      '/pages/marriage/marriage'
    ];

    for (const pagePath of pagesWithBirthCheck) {
      const fullPath = path.join(this.projectRoot, pagePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 检查是否包含出生信息验证逻辑
        let hasAllPages = true;
        for (const birthPage of birthInfoPages) {
          if (!content.includes(birthPage)) {
            hasAllPages = false;
            break;
          }
        }
        
        if (!hasAllPages) {
          this.warnings.push(`${pagePath} 的出生信息验证页面列表可能不完整`);
        }
      }
    }

    console.log('✅ 导航一致性检查完成');
  }

  /**
   * 检查状态管理
   */
  async checkStateManagement() {
    console.log('📊 检查状态管理...');

    const globalStatePath = path.join(this.projectRoot, 'utils/global-state.js');
    const userStatePath = path.join(this.projectRoot, 'utils/userState.js');

    if (!fs.existsSync(globalStatePath)) {
      this.warnings.push('全局状态管理器不存在');
    }

    if (!fs.existsSync(userStatePath)) {
      this.warnings.push('用户状态管理器不存在');
    }

    // 检查关键页面是否使用了状态管理
    const keyPages = ['pages/profile/profile.js', 'pages/ai-chat/ai-chat.js'];
    
    for (const pagePath of keyPages) {
      const fullPath = path.join(this.projectRoot, pagePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        if (!content.includes('globalState') && !content.includes('userState')) {
          this.warnings.push(`${pagePath} 可能未使用状态管理`);
        }
      }
    }

    console.log('✅ 状态管理检查完成');
  }

  /**
   * 检查错误处理
   */
  async checkErrorHandling() {
    console.log('🚨 检查错误处理...');

    const errorHandlerPath = path.join(this.projectRoot, 'utils/error-handler.js');
    if (!fs.existsSync(errorHandlerPath)) {
      this.warnings.push('错误处理工具不存在，建议添加统一的错误处理');
    }

    console.log('✅ 错误处理检查完成');
  }

  /**
   * 检查资源引用
   */
  async checkAssetReferences() {
    console.log('🖼️ 检查资源引用...');

    const assetsPath = path.join(this.projectRoot, 'assets');
    if (!fs.existsSync(assetsPath)) {
      this.warnings.push('assets目录不存在');
      return;
    }

    // 检查是否存在重复的images目录
    const imagesPath = path.join(this.projectRoot, 'images');
    if (fs.existsSync(imagesPath)) {
      this.warnings.push('存在重复的images目录，建议统一到assets目录');
    }

    console.log('✅ 资源引用检查完成');
  }

  /**
   * 检查业务逻辑
   */
  async checkBusinessLogic() {
    console.log('💼 检查业务逻辑...');

    // 检查农历工具
    const lunarPath = path.join(this.projectRoot, 'utils/lunar.js');
    if (fs.existsSync(lunarPath)) {
      const content = fs.readFileSync(lunarPath, 'utf8');
      if (!content.includes('getTodayInfo')) {
        this.errors.push('lunar.js 缺少 getTodayInfo 方法');
      }
    }

    // 检查出生信息页面的跳转逻辑
    const birthInfoPath = path.join(this.projectRoot, 'pages/birth-info/birth-info.js');
    if (fs.existsSync(birthInfoPath)) {
      const content = fs.readFileSync(birthInfoPath, 'utf8');
      if (!content.includes('targetPage')) {
        this.warnings.push('出生信息页面可能缺少目标页面跳转逻辑');
      }
    }

    console.log('✅ 业务逻辑检查完成');
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📋 检查报告');
    console.log('='.repeat(50));

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 恭喜！项目集成检查全部通过！');
    } else {
      if (this.errors.length > 0) {
        console.log(`\n❌ 发现 ${this.errors.length} 个错误:`);
        this.errors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }

      if (this.warnings.length > 0) {
        console.log(`\n⚠️ 发现 ${this.warnings.length} 个警告:`);
        this.warnings.forEach((warning, index) => {
          console.log(`  ${index + 1}. ${warning}`);
        });
      }
    }

    console.log('\n📊 检查统计:');
    console.log(`  错误: ${this.errors.length}`);
    console.log(`  警告: ${this.warnings.length}`);
    console.log(`  状态: ${this.errors.length === 0 ? '✅ 通过' : '❌ 需要修复'}`);
  }
}

// 运行检查
if (require.main === module) {
  const checker = new IntegrationChecker();
  checker.run();
}

module.exports = IntegrationChecker;
