// 用户状态管理
const STORAGE_KEYS = {
  SIGN_IN_RECORD: 'sign_in_record',
  RECENT_USED: 'recent_used',
  USER_POINTS: 'user_points',
  SIGN_IN_STATS: 'sign_in_stats',
  MONTHLY_SIGN_IN: 'monthly_sign_in'
};

// 获取今天的日期字符串 YYYY-MM-DD
const getTodayString = () => {
  const date = new Date();
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取当前年月字符串 YYYY-MM
const getCurrentMonthString = () => {
  const date = new Date();
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
};

// 签到奖励规则
const SIGN_IN_REWARDS = {
  daily: 10,                    // 每日基础奖励
  consecutive: {                // 连续签到奖励
    3: 20,                     // 连续3天
    7: 50,                     // 连续7天
    15: 100,                   // 连续15天
    30: 200                    // 连续30天
  },
  monthly: {                    // 月度签到奖励
    10: 30,                    // 月度10天
    20: 80,                    // 月度20天
    30: 150                    // 月度全勤
  }
};

// 签到相关函数
const signInManager = {
  // 检查今天是否已签到
  async checkTodaySignIn() {
    const record = wx.getStorageSync(STORAGE_KEYS.SIGN_IN_RECORD) || {};
    return !!record[getTodayString()];
  },

  // 执行签到
  async signIn() {
    const today = getTodayString();
    const currentMonth = getCurrentMonthString();
    const record = wx.getStorageSync(STORAGE_KEYS.SIGN_IN_RECORD) || {};
    const monthlyRecord = wx.getStorageSync(STORAGE_KEYS.MONTHLY_SIGN_IN) || {};
    const stats = wx.getStorageSync(STORAGE_KEYS.SIGN_IN_STATS) || {
      totalDays: 0,
      totalPoints: 0,
      maxConsecutive: 0
    };
    
    if (record[today]) {
      return { success: false, message: '今天已经签到过了' };
    }

    // 计算连续签到天数
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
    
    let consecutiveDays = 1;
    if (record[yesterdayString]) {
      consecutiveDays = (record[yesterdayString].consecutiveDays || 0) + 1;
    }

    // 更新月度签到记录
    if (!monthlyRecord[currentMonth]) {
      monthlyRecord[currentMonth] = {
        days: [],
        totalDays: 0
      };
    }
    monthlyRecord[currentMonth].days.push(new Date().getDate());
    monthlyRecord[currentMonth].totalDays += 1;

    // 计算积分奖励
    let totalPoints = SIGN_IN_REWARDS.daily;
    const rewards = [];
    
    // 基础奖励
    rewards.push({
      type: 'daily',
      points: SIGN_IN_REWARDS.daily,
      description: '每日签到'
    });

    // 连续签到奖励
    for (const [days, points] of Object.entries(SIGN_IN_REWARDS.consecutive)) {
      if (consecutiveDays == days) {
        totalPoints += points;
        rewards.push({
          type: 'consecutive',
          points: points,
          description: `连续签到${days}天`
        });
      }
    }

    // 月度签到奖励
    const monthlyDays = monthlyRecord[currentMonth].totalDays;
    for (const [days, points] of Object.entries(SIGN_IN_REWARDS.monthly)) {
      if (monthlyDays == days) {
        totalPoints += points;
        rewards.push({
          type: 'monthly',
          points: points,
          description: `本月签到${days}天`
        });
      }
    }

    // 更新签到记录
    record[today] = {
      timestamp: Date.now(),
      consecutiveDays,
      pointsEarned: totalPoints,
      rewards: rewards
    };
    wx.setStorageSync(STORAGE_KEYS.SIGN_IN_RECORD, record);
    wx.setStorageSync(STORAGE_KEYS.MONTHLY_SIGN_IN, monthlyRecord);

    // 更新统计数据
    stats.totalDays += 1;
    stats.totalPoints += totalPoints;
    stats.maxConsecutive = Math.max(stats.maxConsecutive, consecutiveDays);
    wx.setStorageSync(STORAGE_KEYS.SIGN_IN_STATS, stats);

    // 更新用户积分
    const currentPoints = wx.getStorageSync(STORAGE_KEYS.USER_POINTS) || 0;
    wx.setStorageSync(STORAGE_KEYS.USER_POINTS, currentPoints + totalPoints);

    return {
      success: true,
      consecutiveDays,
      monthlyDays,
      pointsEarned: totalPoints,
      rewards: rewards,
      totalPoints: currentPoints + totalPoints
    };
  },

  // 获取连续签到天数
  getConsecutiveDays() {
    const record = wx.getStorageSync(STORAGE_KEYS.SIGN_IN_RECORD) || {};
    const today = getTodayString();
    
    // 如果今天已签到，返回今天的连续天数
    if (record[today]) {
      return record[today].consecutiveDays || 0;
    }
    
    // 如果今天未签到，检查昨天是否签到
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
    
    if (record[yesterdayString]) {
      return record[yesterdayString].consecutiveDays || 0;
    }
    
    return 0;
  },

  // 获取本月签到记录
  getMonthlySignInRecord() {
    const currentMonth = getCurrentMonthString();
    const monthlyRecord = wx.getStorageSync(STORAGE_KEYS.MONTHLY_SIGN_IN) || {};
    return monthlyRecord[currentMonth] || { days: [], totalDays: 0 };
  },

  // 获取签到统计
  getSignInStats() {
    return wx.getStorageSync(STORAGE_KEYS.SIGN_IN_STATS) || {
      totalDays: 0,
      totalPoints: 0,
      maxConsecutive: 0
    };
  },

  // 获取签到奖励规则
  getRewardRules() {
    return SIGN_IN_REWARDS;
  }
};

// 最近使用功能管理
const recentUsedManager = {
  // 最大记录数
  MAX_RECENT_ITEMS: 10,

  // 添加最近使用记录
  addRecentUsed(item) {
    const recentList = wx.getStorageSync(STORAGE_KEYS.RECENT_USED) || [];
    
    // 移除重复项
    const filteredList = recentList.filter(i => i.type !== item.type);
    
    // 添加到开头
    filteredList.unshift(item);
    
    // 限制数量
    if (filteredList.length > this.MAX_RECENT_ITEMS) {
      filteredList.pop();
    }
    
    wx.setStorageSync(STORAGE_KEYS.RECENT_USED, filteredList);
  },

  // 获取最近使用列表
  getRecentUsed() {
    return wx.getStorageSync(STORAGE_KEYS.RECENT_USED) || [];
  },

  // 清空最近使用记录
  clearRecentUsed() {
    wx.removeStorageSync(STORAGE_KEYS.RECENT_USED);
  }
};

// 积分管理
const pointsManager = {
  // 获取用户总积分
  getTotalPoints() {
    return wx.getStorageSync(STORAGE_KEYS.USER_POINTS) || 0;
  },

  // 添加积分
  addPoints(points) {
    const currentPoints = this.getTotalPoints();
    wx.setStorageSync(STORAGE_KEYS.USER_POINTS, currentPoints + points);
    return currentPoints + points;
  }
};

module.exports = {
  signInManager,
  recentUsedManager,
  pointsManager
}; 