<!--pages/community/community.wxml-->
<view class="container">
  <!-- 顶部搜索栏 -->
  <view class="search-bar">
    <view class="search-input {{searchFocus ? 'focus' : ''}}">
      <icon type="search" size="14"></icon>
      <input 
        type="text" 
        placeholder="搜索帖子、用户、话题" 
        bindinput="onSearchInput" 
        bindconfirm="onSearchConfirm"
        bindfocus="onSearchFocus"
        bindblur="onSearchBlur"
        value="{{searchKeyword}}"
      />
      <view class="clear-btn" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{searchFocus && searchHistory.length > 0}}">
    <view class="history-header">
      <text>搜索历史</text>
      <text class="clear-history" bindtap="clearSearchHistory">清空</text>
    </view>
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="selectSearchHistory"
        data-keyword="{{item}}"
      >
        <icon type="search" size="12"></icon>
        <text>{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 分类和排序栏 -->
  <view class="filter-bar">
    <!-- 分类标签 -->
    <scroll-view class="category-tabs" scroll-x>
      <view 
        wx:for="{{categories}}" 
        wx:key="value" 
        class="category-tab {{currentCategory === item.value ? 'active' : ''}}"
        data-category="{{item.value}}"
        bindtap="switchCategory"
      >
        {{item.name}}
      </view>
    </scroll-view>
    
    <!-- 排序选项 -->
    <view class="sort-options">
      <picker 
        bindchange="onSortChange" 
        value="{{sortIndex}}" 
        range="{{sortOptions}}" 
        range-key="name"
      >
        <view class="sort-picker">
          <text>{{sortOptions[sortIndex].name}}</text>
          <icon type="clear" size="12" style="transform: rotate(90deg);"></icon>
        </view>
      </picker>
    </view>
  </view>

  <!-- 帖子列表 -->
  <view class="post-list">
    <block wx:if="{{posts && posts.length > 0}}">
      <view class="post-item hover-card" wx:for="{{posts}}" wx:key="id" bindtap="viewPost" data-id="{{item.id}}">
        <view class="post-header">
          <image class="avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="post-info">
            <view class="username">{{item.username || '匿名用户'}}</view>
            <view class="post-meta">
              <text class="category">{{item.categoryName}}</text>
              <text class="time">{{item.createTime || ''}}</text>
              <view class="hot-badge" wx:if="{{item.isHot}}">🔥</view>
            </view>
          </view>
          <view class="follow-btn" wx:if="{{!item.isFollowing && item.userId !== userInfo.id}}" bindtap="followUser" data-id="{{item.userId}}" catchtap="true">
            关注
          </view>
        </view>
        <view class="post-content">
          <view class="post-title">{{item.title || ''}}</view>
          <view class="post-text">{{item.content || ''}}</view>
          <!-- 话题标签 -->
          <view class="post-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">#{{tag}}</text>
          </view>
          <view class="post-images" wx:if="{{item.images && item.images.length > 0}}">
            <image 
              wx:for="{{item.images}}" 
              wx:key="*this" 
              wx:for-item="image"
              src="{{image}}" 
              mode="aspectFill"
              bindtap="previewImage"
              data-urls="{{item.images}}"
              data-current="{{image}}"
            ></image>
          </view>
        </view>
        <view class="post-footer">
          <view class="post-actions">
            <view class="action-btn {{item.isLiked ? 'liked' : ''}}" bindtap="toggleLike" data-id="{{item.id}}" catchtap="true">
              <icon type="{{item.isLiked ? 'success' : 'success_no_circle'}}" size="16"></icon>
              <text>{{item.likes || 0}}</text>
            </view>
            <view class="action-btn" bindtap="viewPost" data-id="{{item.id}}">
              <icon type="info_circle" size="16"></icon>
              <text>{{item.comments || 0}}</text>
            </view>
            <view class="action-btn {{item.isCollected ? 'collected' : ''}}" bindtap="toggleCollect" data-id="{{item.id}}" catchtap="true">
              <icon type="{{item.isCollected ? 'success' : 'success_no_circle'}}" size="16"></icon>
              <text>收藏</text>
            </view>
            <view class="action-btn" bindtap="sharePost" data-id="{{item.id}}" catchtap="true">
              <icon type="info_circle" size="16"></icon>
              <text>分享</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && posts && posts.length > 0}}">
      <text>没有更多内容了</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isLoading && (!posts || posts.length === 0)}}">
      <image src="/assets/icons/community/empty.png" mode="aspectFit"></image>
      <text>暂无相关帖子</text>
      <button class="empty-action-btn" bindtap="showPublishModal">发布第一篇帖子</button>
    </view>
  </view>

  <!-- 发布按钮 -->
  <view class="publish-btn" bindtap="showPublishModal">
    <image src="/assets/icons/community/publish.png"></image>
  </view>

  <!-- 发布弹窗 -->
  <view class="modal" wx:if="{{showPublishModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <text>发布帖子</text>
        <image class="close-icon" src="/assets/icons/community/close.png" bindtap="hidePublishModal"></image>
      </view>
      <view class="modal-body">
        <input class="title-input" placeholder="请输入标题" value="{{newPost.title}}" bindinput="onTitleInput"/>
        <textarea class="content-input" placeholder="请输入内容" value="{{newPost.content}}" bindinput="onContentInput"/>
        
        <!-- 话题标签输入 -->
        <view class="tags-input">
          <text class="input-label">话题标签（用空格分隔）</text>
          <input placeholder="例如：命理 风水 占卜" value="{{newPost.tagsInput}}" bindinput="onTagsInput"/>
          <view class="tags-preview" wx:if="{{newPost.tags && newPost.tags.length > 0}}">
            <text class="tag" wx:for="{{newPost.tags}}" wx:key="*this">#{{item}}</text>
          </view>
        </view>
        
        <view class="image-uploader">
          <view class="image-list">
            <view class="image-item" wx:for="{{newPost.images}}" wx:key="*this">
              <image src="{{item}}" mode="aspectFill"></image>
              <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-btn" bindtap="chooseImage" wx:if="{{newPost.images.length < 9}}">
              <image src="/assets/icons/community/upload.png"></image>
              <text>上传图片</text>
            </view>
          </view>
        </view>
        
        <view class="category-selector">
          <picker 
            bindchange="onCategoryChange" 
            value="{{newPost.categoryIndex}}" 
            range="{{publishCategories}}" 
            range-key="name"
          >
            <view class="picker">
              分类：{{publishCategories[newPost.categoryIndex].name}}
            </view>
          </picker>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="hidePublishModal">取消</button>
        <button class="confirm-btn" bindtap="publishPost" disabled="{{!canPublish}}">发布</button>
      </view>
    </view>
  </view>

  <!-- 回到顶部按钮 -->
  <view class="back-to-top" wx:if="{{showBackToTop}}" bindtap="backToTop">
    <icon type="clear" size="20" style="transform: rotate(180deg);"></icon>
  </view>
</view>
