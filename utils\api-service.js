const config = require('../config/config')

/**
 * API服务管理类
 * 统一管理所有后端API调用，包括认证、缓存、错误处理等
 */
class ApiService {
  constructor() {
    this.baseUrl = config.apiUrl
    this.endpoints = config.endpoints
    this.features = config.features
    this.accessToken = null
    this.refreshToken = null
    this.init()
  }

  /**
   * 初始化服务
   */
  init() {
    // 从本地存储恢复token
    try {
      this.accessToken = wx.getStorageSync('access_token')
      this.refreshToken = wx.getStorageSync('refresh_token')
    } catch (error) {
      console.warn('Failed to restore tokens:', error)
    }
  }

  /**
   * 获取请求头
   * @param {boolean} needAuth 是否需要认证
   * @returns {Object} 请求头对象
   */
  getHeaders(needAuth = true) {
    const headers = {
      'Content-Type': 'application/json'
    }

    if (needAuth && this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`
    }

    return headers
  }

  /**
   * 通用请求方法
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  async request(url, options = {}) {
    const fullUrl = `${this.baseUrl}${url}`
    const defaultOptions = {
      method: 'GET',
      header: this.getHeaders(options.needAuth !== false),
      timeout: 10000
    }

    const requestOptions = { ...defaultOptions, ...options }

    try {
      const response = await this.wxRequest(fullUrl, requestOptions)
      
      // 处理认证错误
      if (response.statusCode === 401) {
        await this.refreshAccessToken()
        // 重试请求
        requestOptions.header = this.getHeaders(options.needAuth !== false)
        return await this.wxRequest(fullUrl, requestOptions)
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.data
      } else {
        throw new Error(`API Error: ${response.statusCode} - ${response.data?.detail || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('API Request failed:', error)
      throw error
    }
  }

  /**
   * 封装wx.request为Promise
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  wxRequest(url, options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url,
        ...options,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 微信登录
   * @returns {Promise} 登录结果
   */
  async wxLogin() {
    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      // 获取用户信息
      const userProfile = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })

      // 调用后端登录接口
      const response = await this.request(this.endpoints.auth.wxLogin, {
        method: 'POST',
        data: {
          code: loginRes.code,
          user_info: userProfile.userInfo
        },
        needAuth: false
      })

      if (response.status === 'success') {
        this.accessToken = response.access_token
        this.refreshToken = response.refresh_token
        
        // 保存到本地存储
        wx.setStorageSync('access_token', this.accessToken)
        wx.setStorageSync('refresh_token', this.refreshToken)
        wx.setStorageSync('user_info', response.user)

        return response.user
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    }
  }

  /**
   * 刷新访问令牌
   * @returns {Promise} 刷新结果
   */
  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await this.request(this.endpoints.auth.refresh, {
        method: 'POST',
        data: { refresh_token: this.refreshToken },
        needAuth: false
      })

      this.accessToken = response.access_token
      wx.setStorageSync('access_token', this.accessToken)
      
      return response
    } catch (error) {
      // 刷新失败，清除所有token
      this.logout()
      throw error
    }
  }

  /**
   * 退出登录
   */
  logout() {
    this.accessToken = null
    this.refreshToken = null
    wx.removeStorageSync('access_token')
    wx.removeStorageSync('refresh_token')
    wx.removeStorageSync('user_info')
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!this.accessToken
  }

  /**
   * AI聊天接口
   * @param {string} query 用户问题
   * @param {Array} context 上下文消息
   * @returns {Promise} 聊天结果
   */
  async chat(query, context = []) {
    return await this.request(this.endpoints.chat, {
      method: 'POST',
      data: {
        query,
        context: context.slice(-5), // 限制上下文长度
        user_info: await this.getUserInfo()
      }
    })
  }

  /**
   * 占卜接口
   * @param {Object} birthInfo 生辰信息
   * @param {string} queryType 占卜类型
   * @returns {Promise} 占卜结果
   */
  async fortune(birthInfo, queryType = 'bazi') {
    return await this.request(this.endpoints.fortune, {
      method: 'POST',
      data: {
        query: `请进行${queryType}分析`,
        birth_info: birthInfo,
        fortune_type: queryType
      }
    })
  }

  /**
   * 易经卦象
   * @param {string} question 问题
   * @param {string} method 起卦方法
   * @returns {Promise} 卦象结果
   */
  async yijing(question, method = 'coin') {
    return await this.request(this.endpoints.yijing, {
      method: 'POST',
      data: {
        question,
        method,
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * 风水分析
   * @param {Object} params 风水参数
   * @returns {Promise} 风水分析结果
   */
  async fengshui(params) {
    return await this.request(this.endpoints.fengshui, {
      method: 'POST',
      data: params
    })
  }

  /**
   * 五行分析
   * @param {Object} params 五行参数
   * @returns {Promise} 五行分析结果
   */
  async wuxing(params) {
    return await this.request(this.endpoints.wuxing, {
      method: 'POST',
      data: params
    })
  }

  /**
   * 智能识别用户意图并调用相应API
   * @param {string} query 用户输入
   * @param {Object} userInfo 用户信息
   * @returns {Promise} 处理结果
   */
  async intelligentQuery(query, userInfo = null) {
    const lowerQuery = query.toLowerCase()

    // 关键词映射
    const intentMap = {
      八字: 'bazi',
      紫微: 'ziwei',
      易经: 'yijing',
      风水: 'fengshui',
      五行: 'wuxing',
      占卜: 'fortune',
      算命: 'fortune',
      运势: 'fortune'
    }

    // 检测意图
    let detectedIntent = null
    for (const [keyword, intent] of Object.entries(intentMap)) {
      if (lowerQuery.includes(keyword)) {
        detectedIntent = intent
        break
      }
    }

    // 根据意图调用相应接口
    try {
      if (['bazi', 'ziwei', 'fortune'].includes(detectedIntent)) {
        // 需要生辰信息的占卜
        if (userInfo && userInfo.birthInfo) {
          return await this.fortune(userInfo.birthInfo, detectedIntent)
        } else {
          throw new Error('NEED_BIRTH_INFO')
        }
      } else {
        // 使用通用聊天接口，让后端处理意图识别
        return await this.chat(query)
      }
    } catch (error) {
      if (error.message === 'NEED_BIRTH_INFO') {
        throw error
      }
      // 降级到普通聊天
      return await this.chat(query)
    }
  }

  /**
   * 获取用户信息（从本地存储）
   * @returns {Object} 用户信息
   */
  async getUserInfo() {
    try {
      const userInfo = wx.getStorageSync('user_info')
      return userInfo || null
    } catch (error) {
      console.warn('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取建议问题
   * @param {string} intent 意图类型
   * @returns {Promise} 建议问题列表
   */
  async getSuggestions(intent = 'question') {
    try {
      const response = await this.request(`${this.endpoints.suggestions}?intent=${intent}`, {
        method: 'GET',
        needAuth: false
      })
      return response.data || []
    } catch (error) {
      console.warn('获取建议问题失败:', error)
      return []
    }
  }

  /**
   * 健康检查
   * @returns {Promise} 服务状态
   */
  async healthCheck() {
    try {
      const response = await this.request(this.endpoints.health, {
        method: 'GET',
        needAuth: false,
        timeout: 5000
      })
      return response.data || { status: 'unknown' }
    } catch (error) {
      console.warn('健康检查失败:', error)
      return { status: 'error', error: error.message }
    }
  }

  /**
   * 获取用户档案
   * @returns {Promise} 用户信息
   */
  async getUserProfile() {
    return await this.request(this.endpoints.auth.profile)
  }
}

// 创建全局实例
const apiService = new ApiService()

module.exports = apiService 