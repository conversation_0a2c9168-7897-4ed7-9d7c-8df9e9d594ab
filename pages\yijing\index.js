Page({
  data: {
    hexagrams: [], // 存储64卦数据
    selectedHexagram: null, // 当前选中的卦象
    question: '', // 用户提问
    interpretation: '', // 解读结果
    loading: false
  },

  onLoad() {
    this.initHexagrams();
  },

  // 初始化64卦数据
  initHexagrams() {
    const hexagramData = require('../../utils/yijing/hexagrams');
    this.setData({
      hexagrams: hexagramData.getAllHexagrams()
    });
  },

  // 用户输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 随机选择卦象
  async divineHexagram() {
    if (!this.data.question) {
      wx.showToast({
        title: '请先输入您的问题',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    
    // 模拟卜卦过程
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const randomIndex = Math.floor(Math.random() * this.data.hexagrams.length);
    const hexagram = this.data.hexagrams[randomIndex];
    
    this.setData({
      selectedHexagram: hexagram,
      interpretation: this.generateInterpretation(hexagram),
      loading: false
    });
  },

  // 生成解读结果
  generateInterpretation(hexagram) {
    return `${hexagram.name}卦 - ${hexagram.description}\n\n
卦辞：${hexagram.judgment}\n
象辞：${hexagram.image}\n
解读：${hexagram.interpretation}`;
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '易经解读',
      path: '/pages/yijing/index'
    };
  }
}); 