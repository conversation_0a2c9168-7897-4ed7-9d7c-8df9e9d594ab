// AI智能聊天相关API
import { request } from '../utils/request'

/**
 * 发送AI消息
 * @param {object} data - 消息数据
 */
export const sendAIMessage = (data) => {
  return request({
    url: '/api/ai-chat/message',
    method: 'POST',
    data
  })
}

/**
 * 获取聊天历史
 * @param {object} params - 查询参数
 */
export const getChatHistory = (params = {}) => {
  return request({
    url: '/api/ai-chat/history',
    method: 'GET',
    params
  })
}

/**
 * 删除聊天会话
 * @param {string} sessionId - 会话ID
 */
export const deleteChatSession = (sessionId) => {
  return request({
    url: `/api/ai-chat/session/${sessionId}`,
    method: 'DELETE'
  })
}

/**
 * 获取会话列表
 * @param {object} params - 查询参数
 */
export const getChatSessions = (params = {}) => {
  return request({
    url: '/api/ai-chat/sessions',
    method: 'GET',
    params
  })
}

/**
 * 创建新会话
 * @param {object} data - 会话数据
 */
export const createChatSession = (data = {}) => {
  return request({
    url: '/api/ai-chat/session',
    method: 'POST',
    data
  })
}

/**
 * 更新会话标题
 * @param {string} sessionId - 会话ID
 * @param {string} title - 新标题
 */
export const updateSessionTitle = (sessionId, title) => {
  return request({
    url: `/api/ai-chat/session/${sessionId}`,
    method: 'PUT',
    data: { title }
  })
}

/**
 * 获取快捷操作列表
 */
export const getQuickActions = () => {
  return request({
    url: '/api/ai-chat/quick-actions',
    method: 'GET'
  })
}

/**
 * 执行快捷操作
 * @param {string} actionId - 操作ID
 * @param {object} data - 操作数据
 */
export const executeQuickAction = (actionId, data = {}) => {
  return request({
    url: '/api/ai-chat/quick-action',
    method: 'POST',
    data: {
      action_id: actionId,
      ...data
    }
  })
}

/**
 * 意图识别
 * @param {string} text - 文本内容
 * @param {object} context - 上下文
 */
export const recognizeIntent = (text, context = {}) => {
  return request({
    url: '/api/ai-chat/intent-recognition',
    method: 'POST',
    data: {
      text,
      context
    }
  })
}

/**
 * 消息反馈
 * @param {string} messageId - 消息ID
 * @param {object} feedback - 反馈数据
 */
export const submitMessageFeedback = (messageId, feedback) => {
  return request({
    url: '/api/ai-chat/feedback',
    method: 'POST',
    data: {
      message_id: messageId,
      ...feedback
    }
  })
}

/**
 * 导出聊天记录
 * @param {object} data - 导出参数
 */
export const exportChatHistory = (data) => {
  return request({
    url: '/api/ai-chat/export',
    method: 'POST',
    data,
    responseType: 'blob'
  })
}
