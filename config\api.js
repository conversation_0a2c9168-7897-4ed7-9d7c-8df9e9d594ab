/**
 * API接口配置文件
 */
const config = {
  // API基础URL
  baseUrl: 'https://your-api-domain.com',
  
  // 接口路径
  api: {
    // 首页接口
    home: {
      banner: '/api/banner/list', // 轮播图列表
      category: '/api/category/list', // 测算类目列表
      hotItems: '/api/items/hot', // 热门测算项目
      articles: '/api/articles/list', // 文章列表
    },
    
    // 用户接口
    user: {
      login: '/api/user/login', // 登录
      register: '/api/user/register', // 注册
      profile: '/api/user/profile', // 用户信息
      orders: '/api/user/orders', // 订单列表
    },
    
    // 测算接口
    calculate: {
      submit: '/api/calculate/submit', // 提交测算
      result: '/api/calculate/result', // 获取结果
      history: '/api/calculate/history', // 历史记录
    },
    
    // 支付接口
    payment: {
      create: '/api/payment/create', // 创建订单
      notify: '/api/payment/notify', // 支付回调
      query: '/api/payment/query', // 订单查询
    }
  }
}

// 请求拦截器
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')
    
    wx.request({
      url: config.baseUrl + url,
      ...options,
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: reject
    })
  })
}

module.exports = {
  config,
  request
} 