<!--八字合婚页面-->
<view class="page-container">
  <view class="header">
    <view class="title">八字合婚</view>
    <view class="subtitle">分析两人八字配对情况</view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section" wx:if="{{!analysisResult}}">
    <!-- 男方信息 -->
    <view class="person-card">
      <view class="person-header">
        <view class="gender-icon male">♂</view>
        <text class="person-title">男方信息</text>
      </view>
      <view class="form-group">
        <view class="form-item">
          <text class="label">姓名</text>
          <input 
            class="input" 
            placeholder="请输入男方姓名"
            value="{{maleInfo.name}}"
            bindinput="onMaleNameInput"
          />
        </view>
        <view class="form-item">
          <text class="label">出生日期</text>
          <picker 
            mode="date"
            value="{{maleInfo.birthDate}}"
            bindchange="onMaleDateChange"
          >
            <view class="picker">
              {{maleInfo.birthDate || '请选择出生日期'}}
            </view>
          </picker>
        </view>
        <view class="form-item">
          <text class="label">出生时间</text>
          <picker 
            mode="time"
            value="{{maleInfo.birthTime}}"
            bindchange="onMaleTimeChange"
          >
            <view class="picker">
              {{maleInfo.birthTime || '请选择出生时间'}}
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 女方信息 -->
    <view class="person-card">
      <view class="person-header">
        <view class="gender-icon female">♀</view>
        <text class="person-title">女方信息</text>
      </view>
      <view class="form-group">
        <view class="form-item">
          <text class="label">姓名</text>
          <input 
            class="input" 
            placeholder="请输入女方姓名"
            value="{{femaleInfo.name}}"
            bindinput="onFemaleNameInput"
          />
        </view>
        <view class="form-item">
          <text class="label">出生日期</text>
          <picker 
            mode="date"
            value="{{femaleInfo.birthDate}}"
            bindchange="onFemaleDateChange"
          >
            <view class="picker">
              {{femaleInfo.birthDate || '请选择出生日期'}}
            </view>
          </picker>
        </view>
        <view class="form-item">
          <text class="label">出生时间</text>
          <picker 
            mode="time"
            value="{{femaleInfo.birthTime}}"
            bindchange="onFemaleTimeChange"
          >
            <view class="picker">
              {{femaleInfo.birthTime || '请选择出生时间'}}
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 开始分析按钮 -->
    <button 
      class="analyze-btn"
      bindtap="startAnalysis"
      disabled="{{!canAnalyze}}"
    >
      开始合婚分析
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{isLoading}}">
    <view class="loading-animation">
      <view class="heart-icon">💕</view>
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
    </view>
    <view class="loading-text">正在分析姻缘...</view>
  </view>

  <!-- 分析结果 -->
  <view class="result-section" wx:if="{{analysisResult && !isLoading}}">
    <!-- 总体评分 -->
    <view class="overall-score">
      <view class="score-circle">
        <text class="score-number">{{analysisResult.totalScore}}</text>
        <text class="score-label">分</text>
      </view>
      <view class="score-description">
        <view class="score-title">{{analysisResult.compatibility}}</view>
        <view class="score-subtitle">{{analysisResult.description}}</view>
      </view>
    </view>

    <!-- 双方八字信息 -->
    <view class="bazi-section">
      <view class="section-title">双方八字</view>
      <view class="bazi-comparison">
        <view class="bazi-column">
          <view class="person-name">{{maleInfo.name}} ♂</view>
          <view class="bazi-pillars">
            <view class="pillar" wx:for="{{analysisResult.maleBazi}}" wx:key="index">
              <view class="pillar-top">{{item.tiangan}}</view>
              <view class="pillar-bottom">{{item.dizhi}}</view>
            </view>
          </view>
        </view>
        <view class="vs-divider">VS</view>
        <view class="bazi-column">
          <view class="person-name">{{femaleInfo.name}} ♀</view>
          <view class="bazi-pillars">
            <view class="pillar" wx:for="{{analysisResult.femaleBazi}}" wx:key="index">
              <view class="pillar-top">{{item.tiangan}}</view>
              <view class="pillar-bottom">{{item.dizhi}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细分析 -->
    <view class="analysis-details">
      <view class="section-title">详细分析</view>
      <view class="analysis-item" wx:for="{{analysisResult.details}}" wx:key="type">
        <view class="analysis-header">
          <view class="analysis-icon">{{item.icon}}</view>
          <view class="analysis-title">{{item.title}}</view>
          <view class="analysis-score {{item.level}}">{{item.score}}分</view>
        </view>
        <view class="analysis-content">{{item.content}}</view>
        <view class="score-bar">
          <view class="score-fill" style="width: {{item.score}}%; background: {{item.color}}"></view>
        </view>
      </view>
    </view>

    <!-- 婚姻建议 -->
    <view class="suggestions-section">
      <view class="section-title">婚姻建议</view>
      <view class="suggestions-list">
        <view class="suggestion-item" wx:for="{{analysisResult.suggestions}}" wx:key="index">
          <view class="suggestion-icon">💡</view>
          <view class="suggestion-text">{{item}}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="save-btn" bindtap="saveResult">保存结果</button>
      <button class="share-btn" bindtap="shareResult">分享</button>
      <button class="retest-btn" bindtap="resetAnalysis">重新分析</button>
    </view>
  </view>
</view> 