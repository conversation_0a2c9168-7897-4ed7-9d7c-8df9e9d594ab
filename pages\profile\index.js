const app = getApp()

Page({
  data: {
    userInfo: {
      avatarUrl: '',
      nickName: '未登录',
      points: 0,
      level: '普通用户'
    },
    // 用户统计信息
    statistics: {
      orders: 0,
      favorites: 0,
      points: 0,
      coupons: 0
    },
    // 功能列表
    functionList: [
      {
        id: 'orders',
        name: '我的订单',
        icon: '/assets/icons/order.png',
        url: '/pages/orders/index'
      },
      {
        id: 'favorites',
        name: '我的收藏',
        icon: '/assets/icons/favorite.png',
        url: '/pages/favorites/index'
      },
      {
        id: 'points',
        name: '积分中心',
        icon: '/assets/icons/points.png',
        url: '/pages/points/index'
      },
      {
        id: 'coupons',
        name: '优惠券',
        icon: '/assets/icons/coupon.png',
        url: '/pages/coupons/index'
      },
      {
        id: 'history',
        name: '浏览历史',
        icon: '/assets/icons/history.png',
        url: '/pages/history/index'
      },
      {
        id: 'address',
        name: '地址管理',
        icon: '/assets/icons/address.png',
        url: '/pages/address/index'
      }
    ],
    // 设置列表
    settingList: [
      {
        id: 'notification',
        name: '消息设置',
        icon: '/assets/icons/notification.png',
        url: '/pages/notification-settings/index'
      },
      {
        id: 'privacy',
        name: '隐私设置',
        icon: '/assets/icons/privacy.png',
        url: '/pages/privacy-settings/index'
      },
      {
        id: 'about',
        name: '关于我们',
        icon: '/assets/icons/about.png',
        url: '/pages/about/index'
      },
      {
        id: 'feedback',
        name: '意见反馈',
        icon: '/assets/icons/feedback.png',
        url: '/pages/feedback/index'
      }
    ]
  },

  onLoad() {
    this.getUserInfo()
    this.getStatistics()
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4  // 个人中心的 tabBar 索引
      })
    }
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo
      })
    }
  },

  // 获取统计信息
  async getStatistics() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getUserStatistics'
      })
      
      if (res.result) {
        this.setData({
          statistics: res.result
        })
      }
    } catch (err) {
      console.error('获取用户统计信息失败:', err)
    }
  },

  // 登录
  handleLogin() {
    if (!this.data.userInfo.nickName || this.data.userInfo.nickName === '未登录') {
      wx.navigateTo({
        url: '/pages/login/index'
      })
    }
  },

  // 跳转到功能页面
  navigateToFunction(e) {
    const { url } = e.currentTarget.dataset
    wx.navigateTo({ url })
  },

  // 跳转到设置页面
  navigateToSetting(e) {
    const { url } = e.currentTarget.dataset
    wx.navigateTo({ url })
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync()
          this.setData({
            userInfo: {
              avatarUrl: '',
              nickName: '未登录',
              points: 0,
              level: '普通用户'
            },
            statistics: {
              orders: 0,
              favorites: 0,
              points: 0,
              coupons: 0
            }
          })
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
}) 