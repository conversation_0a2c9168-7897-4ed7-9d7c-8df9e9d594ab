// 首页 - 使用示例
import { createPage } from '../../mixins/basePage'
import { getUserProfile, getUserStatistics } from '../../api/auth'
import { getBirthInfo } from '../../api/birthInfo'
import { getChatSessions, getQuickActions } from '../../api/aiChat'
import { getAnalysisHistory } from '../../api/analysis'
import store from '../../store/index'

createPage({
  data: {
    // 页面数据
    userStats: null,
    birthInfo: null,
    recentAnalyses: [],
    chatSessions: [],
    quickActions: [],
    
    // 功能模块
    analysisTypes: [
      {
        id: 'bazi',
        title: '八字分析',
        description: '深度解析命理特征',
        icon: '🔮',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        path: '/pages/analysis/index?type=bazi'
      },
      {
        id: 'yijing',
        title: '易经占卜',
        description: '古老智慧指引',
        icon: '☯️',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        path: '/pages/analysis/index?type=yijing'
      },
      {
        id: 'fengshui',
        title: '风水分析',
        description: '环境布局优化',
        icon: '🏠',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        path: '/pages/analysis/index?type=fengshui'
      },
      {
        id: 'wuxing',
        title: '五行分析',
        description: '五行属性解读',
        icon: '🌿',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        path: '/pages/analysis/index?type=wuxing'
      }
    ],
    
    // UI状态
    refreshing: false,
    showWelcome: false
  },

  // 页面需要登录
  requireLogin: true,

  // 分享配置
  shareTitle: '卦里乾坤 - 专业命理分析平台',
  sharePath: '/pages/index/index',

  /**
   * 页面加载
   */
  async pageOnLoad(options) {
    // 检查是否是新用户
    this.checkNewUser()
    
    // 加载页面数据
    await this.loadPageData()
  },

  /**
   * 页面显示
   */
  pageOnShow() {
    // 刷新用户统计
    this.refreshUserStats()
  },

  /**
   * 检查新用户
   */
  checkNewUser() {
    const userInfo = store.getState('user.userInfo')
    const isFirstTime = !wx.getStorageSync('hasVisited')
    
    if (isFirstTime && userInfo) {
      this.setData({ showWelcome: true })
      wx.setStorageSync('hasVisited', true)
    }
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    try {
      this.showLoading('加载中...')
      
      // 并行加载多个数据
      const promises = [
        this.loadUserStats(),
        this.loadBirthInfo(),
        this.loadRecentAnalyses(),
        this.loadChatSessions(),
        this.loadQuickActions()
      ]
      
      await Promise.allSettled(promises)
    } catch (error) {
      console.error('加载页面数据失败:', error)
      this.showError('加载失败，请稍后重试')
    } finally {
      this.hideLoading()
    }
  },

  /**
   * 加载用户统计
   */
  async loadUserStats() {
    try {
      const result = await getUserStatistics()
      if (result.status === 'success') {
        this.setData({ userStats: result.data })
      }
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  /**
   * 加载出生信息
   */
  async loadBirthInfo() {
    try {
      const result = await getBirthInfo()
      if (result.status === 'success' && result.data.birth_info) {
        this.setData({ birthInfo: result.data.birth_info })
        store.setBirthInfo(result.data.birth_info)
      }
    } catch (error) {
      console.error('加载出生信息失败:', error)
    }
  },

  /**
   * 加载最近分析
   */
  async loadRecentAnalyses() {
    try {
      const result = await getAnalysisHistory({ limit: 5 })
      if (result.status === 'success') {
        this.setData({ recentAnalyses: result.data.analyses || [] })
      }
    } catch (error) {
      console.error('加载分析历史失败:', error)
    }
  },

  /**
   * 加载聊天会话
   */
  async loadChatSessions() {
    try {
      const result = await getChatSessions({ limit: 3 })
      if (result.status === 'success') {
        this.setData({ chatSessions: result.data.sessions || [] })
      }
    } catch (error) {
      console.error('加载聊天会话失败:', error)
    }
  },

  /**
   * 加载快捷操作
   */
  async loadQuickActions() {
    try {
      const result = await getQuickActions()
      if (result.status === 'success') {
        this.setData({ quickActions: result.data.actions || [] })
        store.setQuickActions(result.data.actions || [])
      }
    } catch (error) {
      console.error('加载快捷操作失败:', error)
    }
  },

  /**
   * 刷新用户统计
   */
  async refreshUserStats() {
    try {
      const result = await getUserStatistics()
      if (result.status === 'success') {
        this.setData({ userStats: result.data })
      }
    } catch (error) {
      console.error('刷新用户统计失败:', error)
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.setData({ refreshing: true })
      await this.loadPageData()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      this.showError('刷新失败')
    } finally {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 点击分析类型
   */
  onAnalysisTypeTap(e) {
    const { type } = e.currentTarget.dataset
    
    // 检查是否需要出生信息
    const needsBirthInfo = ['bazi', 'wuxing', 'ziwei', 'marriage'].includes(type)
    
    if (needsBirthInfo && !this.data.birthInfo) {
      wx.showModal({
        title: '需要出生信息',
        content: '此功能需要您的出生信息，是否前往完善？',
        confirmText: '去完善',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/birth-info/index'
            })
          }
        }
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/analysis/index?type=${type}`
    })
  },

  /**
   * 点击快捷操作
   */
  onQuickActionTap(e) {
    const { action } = e.currentTarget.dataset
    
    if (action.type === 'analysis') {
      this.onAnalysisTypeTap({ currentTarget: { dataset: { type: action.analysis_type } } })
    } else if (action.type === 'navigate') {
      wx.navigateTo({
        url: action.url
      })
    } else if (action.type === 'chat') {
      wx.navigateTo({
        url: '/pages/ai-chat/index'
      })
    }
  },

  /**
   * 查看分析详情
   */
  onAnalysisDetailTap(e) {
    const { analysisId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/analysis-result/index?id=${analysisId}`
    })
  },

  /**
   * 查看聊天会话
   */
  onChatSessionTap(e) {
    const { sessionId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/ai-chat/index?sessionId=${sessionId}`
    })
  },

  /**
   * 前往AI聊天
   */
  goToAIChat() {
    wx.navigateTo({
      url: '/pages/ai-chat/index'
    })
  },

  /**
   * 前往出生信息页面
   */
  goToBirthInfo() {
    wx.navigateTo({
      url: '/pages/birth-info/index'
    })
  },

  /**
   * 前往分析历史
   */
  goToAnalysisHistory() {
    wx.navigateTo({
      url: '/pages/analysis-history/index'
    })
  },

  /**
   * 前往个人中心
   */
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/index'
    })
  },

  /**
   * 关闭欢迎弹窗
   */
  closeWelcome() {
    this.setData({ showWelcome: false })
  },

  /**
   * 查看更多功能
   */
  viewMoreFeatures() {
    wx.navigateTo({
      url: '/pages/features/index'
    })
  }
})
