// 微信小程序API封装工具

/**
 * 存储数据到本地缓存
 * @param {string} key 键名
 * @param {any} data 数据
 */
export const setStorageSync = (key, data) => {
  try {
    wx.setStorageSync(key, data)
  } catch (error) {
    console.error('存储数据失败:', error)
  }
}

/**
 * 从本地缓存获取数据
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 */
export const getStorageSync = (key, defaultValue = null) => {
  try {
    return wx.getStorageSync(key) || defaultValue
  } catch (error) {
    console.error('获取缓存数据失败:', error)
    return defaultValue
  }
}

/**
 * 删除本地缓存数据
 * @param {string} key 键名
 */
export const removeStorageSync = (key) => {
  try {
    wx.removeStorageSync(key)
  } catch (error) {
    console.error('删除缓存数据失败:', error)
  }
}

/**
 * 清空本地缓存
 */
export const clearStorageSync = () => {
  try {
    wx.clearStorageSync()
  } catch (error) {
    console.error('清空缓存失败:', error)
  }
}

/**
 * 显示消息提示框
 * @param {Object} options 配置选项
 */
export const showToast = (options) => {
  const defaultOptions = {
    title: '操作成功',
    icon: 'success',
    duration: 2000,
    mask: false
  }
  
  wx.showToast({
    ...defaultOptions,
    ...options
  })
}

/**
 * 显示加载提示
 * @param {Object} options 配置选项
 */
export const showLoading = (options) => {
  const defaultOptions = {
    title: '加载中...',
    mask: true
  }
  
  wx.showLoading({
    ...defaultOptions,
    ...options
  })
}

/**
 * 隐藏加载提示
 */
export const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 显示模态对话框
 * @param {Object} options 配置选项
 */
export const showModal = (options) => {
  const defaultOptions = {
    title: '提示',
    content: '确定要执行此操作吗？',
    showCancel: true,
    cancelText: '取消',
    confirmText: '确定',
    cancelColor: '#000000',
    confirmColor: '#576B95'
  }
  
  return new Promise((resolve) => {
    wx.showModal({
      ...defaultOptions,
      ...options,
      success: (res) => {
        resolve(res)
        if (options.success) {
          options.success(res)
        }
      },
      fail: (error) => {
        resolve({ confirm: false, cancel: true })
        if (options.fail) {
          options.fail(error)
        }
      }
    })
  })
}

/**
 * 显示操作菜单
 * @param {Object} options 配置选项
 */
export const showActionSheet = (options) => {
  return new Promise((resolve, reject) => {
    wx.showActionSheet({
      ...options,
      success: (res) => {
        resolve(res)
        if (options.success) {
          options.success(res)
        }
      },
      fail: (error) => {
        reject(error)
        if (options.fail) {
          options.fail(error)
        }
      }
    })
  })
}

/**
 * 页面跳转
 * @param {string} url 页面路径
 * @param {Object} options 配置选项
 */
export const navigateTo = (url, options = {}) => {
  wx.navigateTo({
    url,
    ...options
  })
}

/**
 * 关闭当前页面，跳转到应用内的某个页面
 * @param {string} url 页面路径
 * @param {Object} options 配置选项
 */
export const redirectTo = (url, options = {}) => {
  wx.redirectTo({
    url,
    ...options
  })
}

/**
 * 跳转到 tabBar 页面
 * @param {string} url 页面路径
 * @param {Object} options 配置选项
 */
export const switchTab = (url, options = {}) => {
  wx.switchTab({
    url,
    ...options
  })
}

/**
 * 关闭所有页面，打开到应用内的某个页面
 * @param {string} url 页面路径
 * @param {Object} options 配置选项
 */
export const reLaunch = (url, options = {}) => {
  wx.reLaunch({
    url,
    ...options
  })
}

/**
 * 关闭当前页面，返回上一页面或多级页面
 * @param {number} delta 返回的页面数
 */
export const navigateBack = (delta = 1) => {
  wx.navigateBack({
    delta
  })
}

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  return new Promise((resolve, reject) => {
    wx.getSystemInfo({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取用户信息
 * @param {Object} options 配置选项
 */
export const getUserInfo = (options = {}) => {
  return new Promise((resolve, reject) => {
    wx.getUserInfo({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取用户Profile
 * @param {Object} options 配置选项
 */
export const getUserProfile = (options = {}) => {
  return new Promise((resolve, reject) => {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 微信登录
 */
export const login = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 检查会话密钥是否有效
 */
export const checkSession = () => {
  return new Promise((resolve, reject) => {
    wx.checkSession({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 设置剪贴板内容
 * @param {string} data 剪贴板内容
 */
export const setClipboardData = (data) => {
  return new Promise((resolve, reject) => {
    wx.setClipboardData({
      data,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取剪贴板内容
 */
export const getClipboardData = () => {
  return new Promise((resolve, reject) => {
    wx.getClipboardData({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择图片
 * @param {Object} options 配置选项
 */
export const chooseImage = (options = {}) => {
  const defaultOptions = {
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera']
  }
  
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      ...defaultOptions,
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 预览图片
 * @param {Object} options 配置选项
 */
export const previewImage = (options) => {
  wx.previewImage(options)
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片文件路径
 */
export const saveImageToPhotosAlbum = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取位置信息
 * @param {Object} options 配置选项
 */
export const getLocation = (options = {}) => {
  const defaultOptions = {
    type: 'wgs84'
  }
  
  return new Promise((resolve, reject) => {
    wx.getLocation({
      ...defaultOptions,
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 打开地图选择位置
 * @param {Object} options 配置选项
 */
export const chooseLocation = (options = {}) => {
  return new Promise((resolve, reject) => {
    wx.chooseLocation({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 分享到朋友圈
 * @param {Object} options 分享配置
 */
export const shareAppMessage = (options = {}) => {
  return {
    title: '卦里乾坤 - 专业命理分析',
    path: '/pages/index/index',
    imageUrl: '/images/share-default.png',
    ...options
  }
}

/**
 * 分享到朋友圈
 * @param {Object} options 分享配置
 */
export const shareTimeline = (options = {}) => {
  return {
    title: '卦里乾坤 - 专业命理分析',
    query: '',
    imageUrl: '/images/share-timeline.png',
    ...options
  }
}

/**
 * 震动反馈
 * @param {string} type 震动类型
 */
export const vibrateShort = (type = 'medium') => {
  wx.vibrateShort({
    type
  })
}

/**
 * 长震动
 */
export const vibrateLong = () => {
  wx.vibrateLong()
}

export default {
  setStorageSync,
  getStorageSync,
  removeStorageSync,
  clearStorageSync,
  showToast,
  showLoading,
  hideLoading,
  showModal,
  showActionSheet,
  navigateTo,
  redirectTo,
  switchTab,
  reLaunch,
  navigateBack,
  getSystemInfo,
  getUserInfo,
  getUserProfile,
  login,
  checkSession,
  setClipboardData,
  getClipboardData,
  chooseImage,
  previewImage,
  saveImageToPhotosAlbum,
  getLocation,
  chooseLocation,
  shareAppMessage,
  shareTimeline,
  vibrateShort,
  vibrateLong
}
