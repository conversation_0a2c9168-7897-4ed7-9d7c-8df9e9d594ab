// 命理分析相关API
import { request } from '../utils/request'

/**
 * 八字分析
 * @param {object} data - 分析数据
 */
export const baziAnalysis = (data) => {
  return request({
    url: '/api/miniprogram/bazi/analysis',
    method: 'POST',
    data
  })
}

/**
 * 易经占卜
 * @param {object} data - 占卜数据
 */
export const yijingDivination = (data) => {
  return request({
    url: '/api/miniprogram/yijing/divination',
    method: 'POST',
    data
  })
}

/**
 * 风水分析
 * @param {object} data - 风水数据
 */
export const fengshuiAnalysis = (data) => {
  return request({
    url: '/api/miniprogram/fengshui/analysis',
    method: 'POST',
    data
  })
}

/**
 * 五行分析
 * @param {object} data - 五行数据
 */
export const wuxingAnalysis = (data) => {
  return request({
    url: '/api/miniprogram/wuxing/analysis',
    method: 'POST',
    data
  })
}

/**
 * 紫薇斗数分析
 * @param {object} data - 紫薇数据
 */
export const ziweiAnalysis = (data) => {
  return request({
    url: '/api/miniprogram/ziwei/analysis',
    method: 'POST',
    data
  })
}

/**
 * 合婚分析
 * @param {object} data - 合婚数据
 */
export const marriageAnalysis = (data) => {
  return request({
    url: '/api/miniprogram/marriage/analysis',
    method: 'POST',
    data
  })
}

/**
 * 获取分析历史
 * @param {object} params - 查询参数
 */
export const getAnalysisHistory = (params = {}) => {
  return request({
    url: '/api/analysis/history',
    method: 'GET',
    params
  })
}

/**
 * 获取分析详情
 * @param {string} analysisId - 分析ID
 */
export const getAnalysisDetail = (analysisId) => {
  return request({
    url: `/api/analysis/${analysisId}`,
    method: 'GET'
  })
}

/**
 * 删除分析记录
 * @param {string} analysisId - 分析ID
 */
export const deleteAnalysis = (analysisId) => {
  return request({
    url: `/api/analysis/${analysisId}`,
    method: 'DELETE'
  })
}

/**
 * 分析反馈
 * @param {string} analysisId - 分析ID
 * @param {object} feedback - 反馈数据
 */
export const submitAnalysisFeedback = (analysisId, feedback) => {
  return request({
    url: `/api/analysis/${analysisId}/feedback`,
    method: 'POST',
    data: feedback
  })
}

/**
 * 获取分析统计
 */
export const getAnalysisStatistics = () => {
  return request({
    url: '/api/analysis/statistics',
    method: 'GET'
  })
}

/**
 * 导出分析报告
 * @param {string} analysisId - 分析ID
 * @param {string} format - 导出格式
 */
export const exportAnalysisReport = (analysisId, format = 'pdf') => {
  return request({
    url: `/api/analysis/${analysisId}/export`,
    method: 'POST',
    data: { format },
    responseType: 'blob'
  })
}
