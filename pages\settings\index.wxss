/* pages/settings/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 设置区块 */
.section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

/* 设置列表 */
.setting-list {
  padding: 0 30rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.setting-name {
  font-size: 28rpx;
  color: #333;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 选择器样式 */
.picker-text {
  font-size: 28rpx;
  color: #666;
  padding-right: 30rpx;
  position: relative;
}

.picker-text::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16rpx;
  height: 16rpx;
  border-right: 4rpx solid #999;
  border-bottom: 4rpx solid #999;
  transform: translateY(-50%) rotate(-45deg);
}

/* 缓存大小 */
.cache-size {
  font-size: 28rpx;
  color: #999;
}

/* 清除缓存按钮 */
.clear-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 28rpx;
  color: #ff4d4f;
  background-color: #fff;
  border: none;
  border-radius: 0;
  margin: 20rpx 0;
}

.clear-btn::after {
  display: none;
}

/* 箭头图标 */
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}

/* 深色模式样式 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1f1f1f;
  }

  .section {
    background-color: #2c2c2c;
  }

  .section-title {
    color: #fff;
    border-bottom-color: #3d3d3d;
  }

  .setting-item {
    border-bottom-color: #3d3d3d;
  }

  .setting-name {
    color: #fff;
  }

  .picker-text {
    color: #999;
  }

  .clear-btn {
    background-color: #2c2c2c;
    color: #ff4d4f;
  }
} 