# 微信小程序包大小优化脚本
# 使用方法: PowerShell -ExecutionPolicy Bypass -File optimize.ps1

Write-Host "🚀 开始微信小程序包大小优化..." -ForegroundColor Green

# 检查初始大小
$initialSize = (Get-ChildItem -Path . -Recurse -File | Measure-Object -Property Length -Sum).Sum
Write-Host "📦 初始包大小: $([math]::Round($initialSize/1MB, 2)) MB" -ForegroundColor Yellow

# 1. 删除开发文档和临时文件
Write-Host "📝 清理文档和临时文件..." -ForegroundColor Cyan
$docsToRemove = @("*.md", "*.txt", "*.docx")
foreach ($pattern in $docsToRemove) {
    Get-ChildItem -Path . -Name $pattern -Recurse | ForEach-Object {
        if ($_ -notlike "*README*" -and $_ -notlike "*优化*") {
            Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
            Write-Host "  ❌ 删除: $_" -ForegroundColor Red
        }
    }
}

# 2. 删除空目录
Write-Host "📁 删除空目录..." -ForegroundColor Cyan
$emptyDirs = @("docs", "scripts", "workers", "images", "cloudfunctions")
foreach ($dir in $emptyDirs) {
    if (Test-Path $dir) {
        $itemCount = (Get-ChildItem -Path $dir -Recurse).Count
        if ($itemCount -eq 0) {
            Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "  🗑️ 删除空目录: $dir" -ForegroundColor Red
        }
    }
}

# 3. 优化图片资源
Write-Host "🖼️ 优化图片资源..." -ForegroundColor Cyan
Get-ChildItem -Path "assets" -Recurse -File -ErrorAction SilentlyContinue | Where-Object {
    $_.Extension -in @('.png', '.jpg', '.jpeg') -and $_.Length -gt 500KB
} | ForEach-Object {
    Write-Host "  ⚠️ 发现大文件: $($_.Name) ($([math]::Round($_.Length/1KB, 2)) KB)" -ForegroundColor Yellow
    # 可选择性删除超大文件
    # Remove-Item -Path $_.FullName -Force
}

# 4. 检查分包配置
Write-Host "📦 检查分包配置..." -ForegroundColor Cyan
if (Test-Path "app.json") {
    $appConfig = Get-Content "app.json" | ConvertFrom-Json
    if ($appConfig.subpackages) {
        Write-Host "  ✅ 分包配置已存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 建议配置分包以优化加载" -ForegroundColor Yellow
    }
}

# 5. 最终大小检查
$finalSize = (Get-ChildItem -Path . -Recurse -File | Measure-Object -Property Length -Sum).Sum
$reduction = (($initialSize - $finalSize) / $initialSize) * 100

Write-Host "`n📊 优化结果:" -ForegroundColor Green
Write-Host "  初始大小: $([math]::Round($initialSize/1MB, 2)) MB" -ForegroundColor White
Write-Host "  优化后大小: $([math]::Round($finalSize/1MB, 2)) MB" -ForegroundColor White  
Write-Host "  减少: $([math]::Round($reduction, 1))%" -ForegroundColor Green

if ($finalSize -le 2MB) {
    Write-Host "  ✅ 符合微信小程序2MB限制" -ForegroundColor Green
} else {
    Write-Host "  ❌ 仍超过2MB限制，需要进一步优化" -ForegroundColor Red
}

# 6. 生成优化建议
Write-Host "`n💡 优化建议:" -ForegroundColor Cyan
Write-Host "  1. 图片优先使用 webp 格式" -ForegroundColor White
Write-Host "  2. 单个图片控制在 200KB 以内" -ForegroundColor White
Write-Host "  3. 及时清理无用的开发文档" -ForegroundColor White
Write-Host "  4. 合理使用分包加载" -ForegroundColor White

Write-Host "`n🎉 优化完成！" -ForegroundColor Green 