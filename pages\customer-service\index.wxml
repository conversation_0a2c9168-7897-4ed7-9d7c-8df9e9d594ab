<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 客服信息 -->
  <view class="service-header">
    <image class="service-avatar" src="{{serviceInfo.avatar}}" mode="aspectFill"/>
    <view class="service-info">
      <text class="service-name">{{serviceInfo.name}}</text>
      <text class="service-status">在线</text>
    </view>
  </view>

  <!-- 消息列表 -->
  <scroll-view 
    class="message-list" 
    scroll-y 
    id="message-list"
    scroll-into-view="msg-{{messages.length - 1}}"
  >
    <!-- 常见问题 -->
    <view class="faq-section">
      <text class="faq-title">常见问题</text>
      <view class="faq-list">
        <view 
          class="faq-item"
          wx:for="{{faqList}}"
          wx:key="*this"
          bindtap="handleSelectFaq"
          data-question="{{item}}"
        >
          {{item}}
        </view>
      </view>
    </view>

    <!-- 消息内容 -->
    <view 
      class="message-item {{item.type}}" 
      wx:for="{{messages}}" 
      wx:key="time"
      id="msg-{{index}}"
    >
      <image 
        class="avatar" 
        src="{{item.type === 'service' ? serviceInfo.avatar : userInfo.avatarUrl}}" 
        mode="aspectFill"
      />
      <view class="message-content">
        <text class="name">{{item.type === 'service' ? serviceInfo.name : userInfo.nickName}}</text>
        <!-- 文本消息 -->
        <view class="content" wx:if="{{item.contentType === 'text' || !item.contentType}}">
          {{item.content}}
        </view>
        <!-- 图片消息 -->
        <image 
          class="content-image" 
          src="{{item.content}}" 
          mode="widthFix" 
          wx:if="{{item.contentType === 'image'}}"
          bindtap="previewImage"
          data-url="{{item.content}}"
        />
        <text class="time">{{item.time}}</text>
      </view>
    </view>

    <!-- 正在输入提示 -->
    <view class="typing-indicator" wx:if="{{isTyping}}">
      <view class="typing-dot"></view>
      <view class="typing-dot"></view>
      <view class="typing-dot"></view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-section">
    <!-- 输入框工具栏 -->
    <view class="toolbar">
      <view class="tool-item" bindtap="toggleEmoji">
        <image class="tool-icon" src="/assets/icons/emoji.png"/>
      </view>
      <view class="tool-item" bindtap="handleChooseImage">
        <image class="tool-icon" src="/assets/icons/image.png"/>
      </view>
      <view class="tool-item" bindtap="toggleMore">
        <image class="tool-icon" src="/assets/icons/more.png"/>
      </view>
    </view>

    <!-- 输入框 -->
    <view class="input-box">
      <input 
        class="input"
        value="{{inputContent}}"
        placeholder="请输入消息..."
        bindinput="handleInput"
        confirm-type="send"
        bindconfirm="handleSend"
      />
      <view class="send-btn {{inputContent ? 'active' : ''}}" bindtap="handleSend">
        发送
      </view>
    </view>

    <!-- 表情面板 -->
    <view class="emoji-panel" wx:if="{{showEmoji}}">
      <!-- 表情列表 -->
    </view>

    <!-- 更多面板 -->
    <view class="more-panel" wx:if="{{showMore}}">
      <view class="more-item" bindtap="handleChooseImage">
        <image class="more-icon" src="/assets/icons/image.png"/>
        <text>图片</text>
      </view>
      <view class="more-item">
        <image class="more-icon" src="/assets/icons/file.png"/>
        <text>文件</text>
      </view>
      <view class="more-item">
        <image class="more-icon" src="/assets/icons/location.png"/>
        <text>位置</text>
      </view>
    </view>
  </view>
</view> 