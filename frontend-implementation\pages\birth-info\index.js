// 出生信息页面
import { saveBirthInfo, getBirthInfo, updateBirthInfo, calculateLunarDate, getZodiacInfo, getConstellationInfo } from '../../api/birthInfo'
import { showToast, showLoading, hideLoading, showModal } from '../../utils/wx'

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      gender: '男',
      birth_year: new Date().getFullYear(),
      birth_month: new Date().getMonth() + 1,
      birth_day: new Date().getDate(),
      birth_hour: 12,
      birth_minute: 0,
      zodiac: '',
      lucky_number: null
    },
    
    // 计算结果
    lunarInfo: null,
    baziInfo: null,
    wuxingInfo: null,
    
    // UI状态
    loading: false,
    isEdit: false,
    showTimePicker: false,
    showDatePicker: false,
    
    // 选择器数据
    genderOptions: ['男', '女'],
    yearRange: [],
    monthRange: [],
    dayRange: [],
    hourRange: [],
    minuteRange: [],
    
    // 验证状态
    errors: {}
  },

  onLoad(options) {
    this.initDateRanges()
    
    // 如果是编辑模式
    if (options.edit) {
      this.setData({ isEdit: true })
      this.loadBirthInfo()
    }
  },

  /**
   * 初始化日期范围
   */
  initDateRanges() {
    const currentYear = new Date().getFullYear()
    const yearRange = []
    for (let i = currentYear; i >= 1900; i--) {
      yearRange.push(i)
    }
    
    const monthRange = Array.from({ length: 12 }, (_, i) => i + 1)
    const dayRange = Array.from({ length: 31 }, (_, i) => i + 1)
    const hourRange = Array.from({ length: 24 }, (_, i) => i)
    const minuteRange = Array.from({ length: 60 }, (_, i) => i)
    
    this.setData({
      yearRange,
      monthRange,
      dayRange,
      hourRange,
      minuteRange
    })
  },

  /**
   * 加载出生信息
   */
  async loadBirthInfo() {
    try {
      showLoading({ title: '加载中...' })
      const result = await getBirthInfo()
      
      if (result.status === 'success' && result.data.birth_info) {
        this.setData({
          formData: result.data.birth_info,
          lunarInfo: result.data.birth_info.lunar_info,
          baziInfo: result.data.birth_info.bazi,
          wuxingInfo: result.data.birth_info.wuxing
        })
      }
    } catch (error) {
      console.error('加载出生信息失败:', error)
      showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  /**
   * 表单输入处理
   */
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: null
    })
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const gender = this.data.genderOptions[e.detail.value]
    this.setData({
      'formData.gender': gender,
      'errors.gender': null
    })
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: parseInt(value),
      [`errors.${field}`]: null
    })
    
    // 如果是年月日变化，重新计算农历和其他信息
    if (['birth_year', 'birth_month', 'birth_day'].includes(field)) {
      this.calculateAdditionalInfo()
    }
  },

  /**
   * 时间选择
   */
  onTimeChange(e) {
    const { value } = e.detail
    const [hour, minute] = value.split(':').map(Number)
    
    this.setData({
      'formData.birth_hour': hour,
      'formData.birth_minute': minute,
      'errors.birth_hour': null,
      'errors.birth_minute': null
    })
  },

  /**
   * 计算附加信息（农历、生肖、星座等）
   */
  async calculateAdditionalInfo() {
    const { birth_year, birth_month, birth_day } = this.data.formData
    
    if (!birth_year || !birth_month || !birth_day) return
    
    try {
      // 计算农历信息
      const lunarResult = await calculateLunarDate({
        year: birth_year,
        month: birth_month,
        day: birth_day
      })
      
      // 获取生肖信息
      const zodiacResult = await getZodiacInfo(birth_year)
      
      // 获取星座信息
      const constellationResult = await getConstellationInfo(birth_month, birth_day)
      
      this.setData({
        lunarInfo: lunarResult.data,
        'formData.zodiac': zodiacResult.data.zodiac,
        'formData.constellation': constellationResult.data.constellation
      })
    } catch (error) {
      console.error('计算附加信息失败:', error)
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    const errors = {}
    
    if (!formData.name.trim()) {
      errors.name = '请输入姓名'
    }
    
    if (!formData.gender) {
      errors.gender = '请选择性别'
    }
    
    if (!formData.birth_year || formData.birth_year < 1900) {
      errors.birth_year = '请选择正确的出生年份'
    }
    
    if (!formData.birth_month || formData.birth_month < 1 || formData.birth_month > 12) {
      errors.birth_month = '请选择正确的出生月份'
    }
    
    if (!formData.birth_day || formData.birth_day < 1 || formData.birth_day > 31) {
      errors.birth_day = '请选择正确的出生日期'
    }
    
    if (formData.birth_hour < 0 || formData.birth_hour > 23) {
      errors.birth_hour = '请选择正确的出生时辰'
    }
    
    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  /**
   * 提交表单
   */
  async submitForm() {
    if (!this.validateForm()) {
      showToast({
        title: '请完善信息',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({ loading: true })
      showLoading({ title: '保存中...' })
      
      const apiMethod = this.data.isEdit ? updateBirthInfo : saveBirthInfo
      const result = await apiMethod(this.data.formData)
      
      if (result.status === 'success') {
        showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        // 保存成功后跳转
        setTimeout(() => {
          if (this.data.isEdit) {
            wx.navigateBack()
          } else {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
      } else {
        throw new Error(result.message || '保存失败')
      }
    } catch (error) {
      console.error('保存出生信息失败:', error)
      showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    showModal({
      title: '确认重置',
      content: '确定要重置所有信息吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              name: '',
              gender: '男',
              birth_year: new Date().getFullYear(),
              birth_month: new Date().getMonth() + 1,
              birth_day: new Date().getDate(),
              birth_hour: 12,
              birth_minute: 0,
              zodiac: '',
              lucky_number: null
            },
            lunarInfo: null,
            baziInfo: null,
            wuxingInfo: null,
            errors: {}
          })
        }
      }
    })
  }
})
