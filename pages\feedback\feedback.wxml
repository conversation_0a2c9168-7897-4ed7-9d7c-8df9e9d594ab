<!--问题反馈页面-->
<view class="page-container">
  <view class="header">
    <view class="title">意见反馈</view>
    <view class="subtitle">您的建议是我们前进的动力</view>
  </view>

  <form bindsubmit="submitFeedback">
    <!-- 反馈类型 -->
    <view class="form-section">
      <view class="section-title">反馈类型</view>
      <view class="type-selector">
        <view 
          class="type-item {{feedbackType === item.value ? 'active' : ''}}"
          wx:for="{{feedbackTypes}}"
          wx:key="value"
          bindtap="selectFeedbackType"
          data-type="{{item.value}}"
        >
          <view class="type-icon">{{item.icon}}</view>
          <text class="type-text">{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- 问题描述 -->
    <view class="form-section">
      <view class="section-title">
        <text>问题描述</text>
        <text class="required">*</text>
      </view>
      <textarea
        class="feedback-textarea"
        placeholder="请详细描述您遇到的问题或建议，我们会认真处理..."
        value="{{feedbackContent}}"
        bindinput="onContentInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
      />
      <view class="char-count">{{feedbackContent.length}}/500</view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <view class="section-title">联系方式（选填）</view>
      <view class="contact-inputs">
        <view class="input-group">
          <view class="input-label">微信号/手机号</view>
          <input
            class="contact-input"
            placeholder="方便我们联系您"
            value="{{contactInfo}}"
            bindinput="onContactInput"
          />
        </view>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">
        <text>截图上传</text>
        <text class="section-desc">（最多3张）</text>
      </view>
      <view class="image-upload">
        <view class="image-list">
          <view 
            class="image-item"
            wx:for="{{uploadedImages}}"
            wx:key="index"
          >
            <image 
              class="uploaded-image"
              src="{{item}}"
              mode="aspectFill"
              bindtap="previewImage"
              data-url="{{item}}"
            />
            <view 
              class="image-delete"
              bindtap="deleteImage"
              data-index="{{index}}"
            >×</view>
          </view>
          <view 
            class="image-upload-btn"
            wx:if="{{uploadedImages.length < 3}}"
            bindtap="chooseImage"
          >
            <view class="upload-icon">📷</view>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 历史反馈 -->
    <view class="form-section" wx:if="{{historyFeedbacks.length > 0}}">
      <view class="section-title">
        <text>历史反馈</text>
        <text class="section-more" bindtap="viewAllHistory">查看全部</text>
      </view>
      <view class="history-list">
        <view 
          class="history-item"
          wx:for="{{historyFeedbacks}}"
          wx:key="id"
          bindtap="viewFeedbackDetail"
          data-id="{{item.id}}"
        >
          <view class="history-content">
            <view class="history-title">{{item.title}}</view>
            <view class="history-desc">{{item.content}}</view>
            <view class="history-time">{{item.createTime}}</view>
          </view>
          <view class="history-status {{item.status}}">
            {{item.statusText}}
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn"
        form-type="submit"
        disabled="{{!canSubmit}}"
      >
        提交反馈
      </button>
      <view class="submit-tips">
        <text>提交后我们会在24小时内回复您</text>
      </view>
    </view>
  </form>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isSubmitting}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在提交...</text>
    </view>
  </view>

  <!-- 成功提示 -->
  <view class="success-modal" wx:if="{{showSuccessModal}}">
    <view class="modal-mask"></view>
    <view class="modal-content">
      <view class="success-icon">✅</view>
      <view class="success-title">提交成功</view>
      <view class="success-desc">感谢您的反馈，我们会尽快处理</view>
      <button class="success-btn" bindtap="closeSuccessModal">确定</button>
    </view>
  </view>
</view> 