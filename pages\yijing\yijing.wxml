<!--yijing.wxml-->
<view class="container">
  <!-- 使用出生信息组件 -->
  <birth-info bind:save="onBirthInfoSave"></birth-info>
  
  <view class="card">
    <view class="input-section">
      <!-- 显示出生信息 -->
      <view class="birth-info" wx:if="{{birthInfo}}">
        <view class="info-title">出生信息</view>
        <view class="info-content">
          <text class="info-item">姓名：{{birthInfo.name}}</text>
          <text class="info-item">性别：{{birthInfo.gender}}</text>
          <text class="info-item">出生日期：{{birthInfo.birthDate}}</text>
          <text class="info-item">出生时间：{{birthInfo.birthTime}}</text>
        </view>
      </view>

      <view class="input-group">
        <text class="label">问题描述</text>
        <textarea class="input" placeholder="请输入您想问的问题" bindinput="onQuestionInput" value="{{question}}"></textarea>
      </view>
      
      <button class="submit-btn" bindtap="onSubmit" loading="{{loading}}">开始占卜</button>
    </view>
  </view>

  <view class="result-section" wx:if="{{result}}">
    <view class="section-title">卦象结果</view>
    
    <view class="hexagram">
      <view class="hexagram-name">{{result.name}}</view>
      <view class="hexagram-lines">
        <view class="line {{line === 1 ? 'yang' : 'yin'}}" wx:for="{{result.lines}}" wx:key="index" wx:for-item="line"></view>
      </view>
    </view>
    
    <view class="interpretation">
      <view class="interpretation-title">卦象解读</view>
      <text class="interpretation-text">{{result.interpretation}}</text>
    </view>
    
    <view class="advice">
      <view class="advice-title">吉凶建议</view>
      <text class="advice-text">{{result.advice}}</text>
    </view>
  </view>
</view> 