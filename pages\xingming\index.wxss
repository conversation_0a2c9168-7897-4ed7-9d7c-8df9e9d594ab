.container {
  min-height: 100vh;
  background-color: #F8F9FD;
  padding-bottom: 120rpx;
}

/* 顶部banner */
.banner {
  position: relative;
  width: 100%;
  height: 360rpx;
  overflow: hidden;
}

.banner-bg {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  left: 40rpx;
  bottom: 40rpx;
  color: #fff;
  z-index: 1;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 表单区域 */
.form-section {
  margin: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.input {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

.picker {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.picker.placeholder {
  color: #999;
}

/* 性别选择器 */
.gender-picker {
  display: flex;
  gap: 20rpx;
}

.gender-option {
  flex: 1;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.gender-option.active {
  background: #FF69B4;
  color: #fff;
}

/* 提交按钮 */
.submit-section {
  margin: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF69B4 0%, #FF8C69 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.submit-btn.disabled {
  opacity: 0.6;
}

.price {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  font-weight: normal;
}

/* 结果区域 */
.result-section {
  margin: 30rpx;
}

.analysis-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 12rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 评分展示 */
.score-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin: 30rpx 0;
}

.score {
  font-size: 80rpx;
  font-weight: bold;
  color: #FF69B4;
}

.max-score {
  font-size: 32rpx;
  color: #999;
  margin-left: 8rpx;
}

.score-desc {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

/* 五格数理 */
.wuge-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.wuge-item {
  background: #F8F9FD;
  border-radius: 12rpx;
  padding: 20rpx;
}

.wuge-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.wuge-number {
  font-size: 40rpx;
  color: #FF69B4;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.wuge-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 三才配置 */
.sancai-chart {
  width: 100%;
  height: 300rpx;
  margin: 20rpx 0;
}

.sancai-canvas {
  width: 100%;
  height: 100%;
}

.sancai-analysis {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 八字分析 */
.bazi-grid {
  margin: 20rpx 0;
}

.bazi-row {
  display: flex;
  margin-bottom: 16rpx;
}

.bazi-row .label {
  width: 120rpx;
  margin-bottom: 0;
}

.bazi-row .value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.bazi-analysis {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 20rpx;
}

/* 吉凶分析 */
.jixiong-list {
  margin-top: 20rpx;
}

.jixiong-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
}

.jixiong-type {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.jixiong-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 建议方案 */
.advice-list {
  margin-top: 20rpx;
}

.advice-item {
  margin-bottom: 24rpx;
}

.advice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.advice-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 分享按钮 */
.share-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
}

.share-btn {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.share-icon {
  width: 36rpx;
  height: 36rpx;
}

.share-btn text {
  font-size: 28rpx;
  color: #666;
} 