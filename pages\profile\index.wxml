<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card" bindtap="handleLogin">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}" mode="aspectFill"/>
      <view class="info-right">
        <view class="nickname">{{userInfo.nickName}}</view>
        <view class="user-level">
          <text class="level-tag">{{userInfo.level}}</text>
          <text class="points">积分: {{userInfo.points}}</text>
        </view>
      </view>
    </view>
    <view class="card-bottom">
      <view class="stat-item">
        <text class="stat-num">{{statistics.orders}}</text>
        <text class="stat-label">订单</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{statistics.favorites}}</text>
        <text class="stat-label">收藏</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{statistics.points}}</text>
        <text class="stat-label">积分</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{statistics.coupons}}</text>
        <text class="stat-label">优惠券</text>
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="section">
    <view class="section-title">我的服务</view>
    <view class="function-grid">
      <view class="grid-item" 
            wx:for="{{functionList}}" 
            wx:key="id"
            bindtap="navigateToFunction"
            data-url="{{item.url}}">
        <image class="grid-icon" src="{{item.icon}}" mode="aspectFit"/>
        <text class="grid-text">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 设置列表 -->
  <view class="section">
    <view class="section-title">设置</view>
    <view class="setting-list">
      <view class="setting-item" 
            wx:for="{{settingList}}" 
            wx:key="id"
            bindtap="navigateToSetting"
            data-url="{{item.url}}">
        <view class="setting-left">
          <image class="setting-icon" src="{{item.icon}}" mode="aspectFit"/>
          <text class="setting-name">{{item.name}}</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png" mode="aspectFit"/>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo.nickName !== '未登录'}}">
    <button class="logout-btn" bindtap="handleLogout">退出登录</button>
  </view>
</view> 