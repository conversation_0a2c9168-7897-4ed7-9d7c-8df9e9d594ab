/* pages/community/community.wxss */
.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
}

.search-input icon {
  margin-right: 16rpx;
  color: #999;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 分类标签 */
.category-tabs {
  position: fixed;
  top: 120rpx;
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
  display: flex;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  overflow-x: auto;
}

.category-tab {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 28rpx;
  transition: all 0.3s;
}

.category-tab.active {
  color: #fff;
  background: #8a2be2;
}

/* 帖子列表 */
.post-list {
  margin-top: 220rpx;
  padding: 20rpx 30rpx;
}

.post-item {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.post-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.post-info {
  flex: 1;
}

.username {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.post-meta {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.category {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 12px;
  color: #fff;
  padding: 2px 8px;
  border-radius: 4px;
  background: #8a2be2;
}

.time {
  font-size: 12px;
  color: #999;
}

.post-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.category-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.category-tag.ziwei {
  background: #8a2be2;
}

.category-tag.yijing {
  background: #4b0082;
}

.category-tag.fengshui {
  background: #483d8b;
}

.category-tag.bazi {
  background: #9370db;
}

.category-tag.fortune {
  background: #7b68ee;
}

.post-content {
  margin-bottom: 15px;
}

.post-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.post-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.post-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.post-images image {
  width: 100%;
  height: 100px;
  border-radius: 8px;
}

.post-footer {
  display: flex;
  justify-content: space-around;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.action-item text {
  font-size: 12px;
  color: #999;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #8a2be2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-more text {
  font-size: 14px;
  color: #999;
}

/* 没有更多数据样式 */
.no-more {
  text-align: center;
  padding: 15px 0;
}

.no-more text {
  font-size: 14px;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-state image {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.empty-state text {
  font-size: 14px;
  color: #999;
}

/* 发布按钮样式 */
.publish-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  background: #8a2be2;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(138,43,226,0.3);
}

.publish-btn image {
  width: 24px;
  height: 24px;
}

/* 发布弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 90%;
  max-height: 80vh;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.close-icon {
  width: 20px;
  height: 20px;
  padding: 5px;
}

.modal-body {
  padding: 15px;
  flex: 1;
  overflow-y: auto;
}

.title-input {
  width: 100%;
  height: 40px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 0 12px;
  margin-bottom: 12px;
  font-size: 14px;
}

.content-input {
  width: 100%;
  height: 120px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  font-size: 14px;
}

/* 标签选择样式 */
.post-tags {
  display: flex;
  gap: 10px;
  margin-bottom: 12px;
}

.tag {
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 15px;
}

.tag.active {
  color: #fff;
  background-color: #8a2be2;
}

.image-uploader {
  margin-bottom: 12px;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.image-item {
  position: relative;
  width: 100%;
  padding-bottom: 100%;
}

.image-item image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.upload-btn {
  width: 100%;
  padding-bottom: 100%;
  position: relative;
  border: 1px dashed #ddd;
  border-radius: 8px;
}

.upload-btn image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
}

.category-selector {
  margin-bottom: 12px;
}

.picker {
  width: 100%;
  height: 40px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.modal-footer {
  display: flex;
  padding: 15px;
  border-top: 1px solid #f0f0f0;
}

.modal-footer button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  margin-right: 10px;
}

.confirm-btn {
  background: #8a2be2;
  color: #fff;
}