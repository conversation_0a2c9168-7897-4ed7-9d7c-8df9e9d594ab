// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 24节气数据
const solarTerms = [
  { name: '小寒', month: 1, day: 5 },
  { name: '大寒', month: 1, day: 20 },
  { name: '立春', month: 2, day: 4 },
  { name: '雨水', month: 2, day: 19 },
  { name: '惊蛰', month: 3, day: 5 },
  { name: '春分', month: 3, day: 20 },
  { name: '清明', month: 4, day: 5 },
  { name: '谷雨', month: 4, day: 20 },
  { name: '立夏', month: 5, day: 5 },
  { name: '小满', month: 5, day: 21 },
  { name: '芒种', month: 6, day: 5 },
  { name: '夏至', month: 6, day: 21 },
  { name: '小暑', month: 7, day: 7 },
  { name: '大暑', month: 7, day: 22 },
  { name: '立秋', month: 8, day: 7 },
  { name: '处暑', month: 8, day: 23 },
  { name: '白露', month: 9, day: 7 },
  { name: '秋分', month: 9, day: 23 },
  { name: '寒露', month: 10, day: 8 },
  { name: '霜降', month: 10, day: 23 },
  { name: '立冬', month: 11, day: 7 },
  { name: '小雪', month: 11, day: 22 },
  { name: '大雪', month: 12, day: 7 },
  { name: '冬至', month: 12, day: 22 }
]

// 云函数入口函数
exports.main = async (event, context) => {
  const { year, month, day } = event

  // 查找当前节气
  const currentTerm = solarTerms.find(term => term.month === month && term.day === day)
  
  // 查找下一个节气
  let nextTerm
  if (currentTerm) {
    const currentIndex = solarTerms.indexOf(currentTerm)
    nextTerm = solarTerms[(currentIndex + 1) % 24]
  } else {
    nextTerm = solarTerms.find(term => {
      if (term.month === month) {
        return term.day > day
      }
      return term.month > month
    }) || solarTerms[0]
  }

  // 计算下一个节气的日期
  let nextTermYear = year
  if (month === 12 && nextTerm.month === 1) {
    nextTermYear++
  }

  return {
    currentTerm: currentTerm ? {
      name: currentTerm.name,
      date: `${year}-${String(currentTerm.month).padStart(2, '0')}-${String(currentTerm.day).padStart(2, '0')}`,
      isToday: true
    } : null,
    nextTerm: {
      name: nextTerm.name,
      date: `${nextTermYear}-${String(nextTerm.month).padStart(2, '0')}-${String(nextTerm.day).padStart(2, '0')}`,
      isToday: false
    }
  }
} 