# AI聊天集成文档

## 目录
1. [功能概述](#功能概述)
2. [系统架构](#系统架构)
3. [前端实现](#前端实现)
4. [配置说明](#配置说明)
5. [API接口](#api接口)
6. [使用示例](#使用示例)
7. [注意事项](#注意事项)

## 功能概述

AI聊天功能集成了以下特性：
- 实时消息交互
- 打字机效果的AI回复
- 上下文感知对话（保留最近5条消息）
- 错误处理和用户反馈
- 自动滚动
- 消息时间戳
- 多环境配置支持

## 系统架构

### 目录结构
```
miniprogram-1/
├── pages/
│   └── ai-chat/
│       ├── ai-chat.js    # 聊天页面逻辑
│       ├── ai-chat.wxml  # 页面模板
│       └── ai-chat.wxss  # 页面样式
├── config/
│   └── config.js         # 环境配置
└── project.config.json   # 项目配置
```

## 前端实现

### 1. 聊天界面 (ai-chat.wxml)
```html
<view class="chat-container">
  <scroll-view 
    scroll-y="true" 
    class="message-list" 
    scroll-top="{{scrollTop}}"
    id="message-container"
  >
    <!-- 消息列表 -->
    <block wx:for="{{messages}}" wx:key="id">
      <view class="message-item {{item.type === 'user' ? 'user-message' : 'ai-message'}}">
        <view class="avatar">
          <image src="{{item.type === 'user' ? '/images/user-avatar.png' : '/images/ai-avatar.png'}}" />
        </view>
        <view class="message-content">
          <text class="message-text">{{item.content}}</text>
          <text class="message-time">{{item.timestamp}}</text>
        </view>
      </view>
    </block>
    
    <!-- 打字动画 -->
    <view class="message-item ai-message" wx:if="{{isTyping}}">
      <view class="avatar">
        <image src="/images/ai-avatar.png" />
      </view>
      <view class="message-content">
        <view class="typing-animation" wx:if="{{currentTypingMessage === ''}}">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
        <text class="message-text" wx:else>{{currentTypingMessage}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <input 
      class="message-input" 
      value="{{inputValue}}" 
      bindinput="onInputChange"
      placeholder="输入你想问的问题..."
      confirm-type="send"
      bindconfirm="sendMessage"
    />
    <button 
      class="send-button" 
      bindtap="sendMessage"
      disabled="{{!inputValue.trim()}}"
    >发送</button>
  </view>
</view>
```

### 2. 页面逻辑 (ai-chat.js)
```javascript
const app = getApp()
const config = require('../../config/config')

Page({
  data: {
    messages: [],
    inputValue: '',
    isTyping: false,
    currentTypingMessage: '',
    scrollTop: 0
  },

  // 发送消息
  async sendMessage() {
    const { inputValue, messages } = this.data
    if (!inputValue.trim()) return

    // 添加用户消息
    this.addMessage({
      type: 'user',
      content: inputValue
    })

    try {
      const response = await wx.request({
        url: `${config.apiUrl}/api/chat/send`,
        method: 'POST',
        data: {
          message: inputValue,
          context: messages.slice(-5).map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }))
        }
      })

      if (response.statusCode === 200) {
        this.typeMessage(response.data.response)
      }
    } catch (error) {
      this.handleError(error)
    }
  }
})
```

## 配置说明

### 环境配置 (config.js)
```javascript
const config = {
  development: {
    apiUrl: 'http://localhost:8000',
    env: 'development'
  },
  production: {
    apiUrl: 'https://your-production-api-domain.com',
    env: 'production'
  }
}

const getConfig = () => {
  const envVersion = __wxConfig.envVersion
  const isProd = envVersion === 'release'
  return isProd ? config.production : config.development
}

module.exports = getConfig()
```

### 项目配置 (project.config.json)
```json
{
  "setting": {
    "urlCheck": false
  },
  "requestDomains": [
    "http://localhost:8000",
    "https://your-production-api-domain.com"
  ]
}
```

## API接口

### 发送消息
- 接口：`/api/chat/send`
- 方法：POST
- 请求体：
```json
{
  "message": "用户输入的消息",
  "context": [
    {
      "role": "user" | "assistant",
      "content": "消息内容"
    }
  ]
}
```
- 响应体：
```json
{
  "response": "AI的回复内容"
}
```

## 使用示例

### 1. 基本使用
```javascript
// 发送消息
const chat = new AiChat()
await chat.sendMessage('请解释一下八字命理的基本概念')
```

### 2. 错误处理
```javascript
try {
  await chat.sendMessage('你好')
} catch (error) {
  wx.showToast({
    title: '网络错误，请重试',
    icon: 'none'
  })
}
```

## 注意事项

1. 环境配置
   - 开发环境需要在微信开发者工具中关闭域名校验
   - 生产环境需要配置合法域名

2. 性能优化
   - 消息上下文限制为最近5条，避免请求数据过大
   - 使用打字机效果时，建议控制单次响应文本长度

3. 安全性
   - 生产环境必须使用HTTPS
   - 建议实现请求频率限制
   - 敏感信息不要在前端存储

4. 错误处理
   - 网络错误
   - API调用失败
   - 响应超时
   - 数据格式错误

5. 调试方法
   - 开发环境可以使用console.log查看请求响应
   - 使用微信开发者工具的Network面板监控请求
   - 在app.js中可以设置debug模式 