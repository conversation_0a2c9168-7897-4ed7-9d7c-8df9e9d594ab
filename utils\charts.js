// 基础图表类
class Chart {
  constructor() {
    this.ctx = null
    this.width = 0
    this.height = 0
  }

  init(canvasId) {
    const query = wx.createSelectorQuery()
    return new Promise((resolve) => {
      query.select('#' + canvasId)
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node
          this.ctx = canvas.getContext('2d')
          this.width = res[0].width
          this.height = res[0].height
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvas.width = this.width * dpr
          canvas.height = this.height * dpr
          this.ctx.scale(dpr, dpr)
          resolve()
        })
    })
  }

  clear() {
    this.ctx.clearRect(0, 0, this.width, this.height)
  }

  dispose() {
    this.ctx = null
  }
}

// 五行分析图表
class WuxingChart extends Chart {
  async draw(canvasId, data) {
    await this.init(canvasId)
    this.clear()

    const centerX = this.width / 2
    const centerY = this.height / 2
    const radius = Math.min(centerX, centerY) * 0.8

    let startAngle = -Math.PI / 2
    const total = data.reduce((sum, item) => sum + item.value, 0)

    // 绘制饼图
    data.forEach(item => {
      const angle = (item.value / total) * Math.PI * 2
      this.ctx.beginPath()
      this.ctx.moveTo(centerX, centerY)
      this.ctx.arc(centerX, centerY, radius, startAngle, startAngle + angle)
      this.ctx.closePath()
      this.ctx.fillStyle = item.color
      this.ctx.fill()

      // 绘制文字
      const textAngle = startAngle + angle / 2
      const textRadius = radius * 0.7
      const textX = centerX + Math.cos(textAngle) * textRadius
      const textY = centerY + Math.sin(textAngle) * textRadius
      
      this.ctx.save()
      this.ctx.translate(textX, textY)
      this.ctx.rotate(textAngle + Math.PI / 2)
      this.ctx.fillStyle = '#fff'
      this.ctx.font = 'bold 14px sans-serif'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(item.name, 0, 0)
      this.ctx.restore()

      startAngle += angle
    })
  }
}

// 运势分析图表
class YunshiChart extends Chart {
  async draw(canvasId, data) {
    await this.init(canvasId)
    this.clear()

    const padding = 20
    const chartWidth = this.width - padding * 2
    const chartHeight = this.height - padding * 2

    // 计算坐标轴
    const xStep = chartWidth / (data.length - 1)
    const yMax = Math.max(...data.map(item => item.value))
    const yStep = chartHeight / yMax

    // 绘制坐标轴
    this.ctx.beginPath()
    this.ctx.strokeStyle = '#ddd'
    this.ctx.moveTo(padding, padding)
    this.ctx.lineTo(padding, this.height - padding)
    this.ctx.lineTo(this.width - padding, this.height - padding)
    this.ctx.stroke()

    // 绘制网格线
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i
      this.ctx.beginPath()
      this.ctx.strokeStyle = '#f0f0f0'
      this.ctx.moveTo(padding, y)
      this.ctx.lineTo(this.width - padding, y)
      this.ctx.stroke()

      // 绘制Y轴刻度
      this.ctx.fillStyle = '#999'
      this.ctx.textAlign = 'right'
      this.ctx.fillText(
        Math.round((5 - i) * (yMax / 5)),
        padding - 5,
        y + 4
      )
    }

    // 绘制曲线
    this.ctx.beginPath()
    this.ctx.strokeStyle = '#6236FF'
    this.ctx.lineWidth = 2
    data.forEach((item, index) => {
      const x = padding + xStep * index
      const y = this.height - padding - (item.value * yStep)
      if (index === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    })
    this.ctx.stroke()

    // 绘制数据点
    data.forEach((item, index) => {
      const x = padding + xStep * index
      const y = this.height - padding - (item.value * yStep)

      // 绘制圆点
      this.ctx.beginPath()
      this.ctx.fillStyle = '#fff'
      this.ctx.strokeStyle = '#6236FF'
      this.ctx.lineWidth = 2
      this.ctx.arc(x, y, 4, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.stroke()

      // 绘制X轴标签
      this.ctx.fillStyle = '#999'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(
        item.year || item.month || item.day,
        x,
        this.height - padding + 15
      )
    })
  }
}

module.exports = {
  WuxingChart,
  YunshiChart
} 