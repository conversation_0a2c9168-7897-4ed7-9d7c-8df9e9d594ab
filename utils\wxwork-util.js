/**
 * 企业微信工具类
 * 提供企业微信相关的API调用和工具函数
 */

// 导入API配置
const { API_CONFIG, API_ENDPOINTS, REQUEST_CONFIG, ERROR_CODES, ERROR_MESSAGES } = require('../config/api-config.js')

const wxworkUtil = {
  
  /**
   * HTTP请求封装
   * @param {string} url 请求URL
   * @param {object} options 请求选项
   * @returns {Promise} 请求结果
   */
  request(url, options = {}) {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        url: `${API_CONFIG.baseURL}${url}`,
        method: options.method || 'GET',
        timeout: options.timeout || API_CONFIG.timeout,
        header: {
          ...REQUEST_CONFIG.headers,
          ...options.header
        },
        ...options
      }

      if (API_CONFIG.debug) {
        console.log('API请求:', requestOptions)
      }

      wx.request({
        ...requestOptions,
        success: (res) => {
          if (API_CONFIG.debug) {
            console.log('API响应:', res)
          }

          if (res.statusCode === 200) {
            if (res.data.success || res.data.status === 'success') {
              resolve(res.data.data || res.data)
            } else {
              const error = new Error(res.data.detail || res.data.message || '请求失败')
              error.code = ERROR_CODES.SERVER_ERROR
              reject(error)
            }
          } else if (res.statusCode === 401) {
            const error = new Error(ERROR_MESSAGES[ERROR_CODES.AUTH_ERROR])
            error.code = ERROR_CODES.AUTH_ERROR
            reject(error)
          } else if (res.statusCode === 403) {
            const error = new Error(ERROR_MESSAGES[ERROR_CODES.PERMISSION_DENIED])
            error.code = ERROR_CODES.PERMISSION_DENIED
            reject(error)
          } else if (res.statusCode === 404) {
            const error = new Error(ERROR_MESSAGES[ERROR_CODES.NOT_FOUND])
            error.code = ERROR_CODES.NOT_FOUND
            reject(error)
          } else {
            const error = new Error(`HTTP ${res.statusCode}: ${res.data.detail || '请求失败'}`)
            error.code = ERROR_CODES.SERVER_ERROR
            reject(error)
          }
        },
        fail: (error) => {
          console.error('请求失败:', error)
          let errorMsg = ERROR_MESSAGES[ERROR_CODES.NETWORK_ERROR]
          let errorCode = ERROR_CODES.NETWORK_ERROR
          
          if (error.errMsg && error.errMsg.includes('timeout')) {
            errorMsg = ERROR_MESSAGES[ERROR_CODES.TIMEOUT]
            errorCode = ERROR_CODES.TIMEOUT
          }
          
          const err = new Error(errorMsg)
          err.code = errorCode
          reject(err)
        }
      })
    })
  },

  /**
   * 获取通讯录用户列表
   * @param {string} departmentId 部门ID，不传则获取所有用户
   * @returns {Promise} 用户列表
   */
  async getContactList(departmentId = '') {
    try {
      const url = departmentId 
        ? `${API_ENDPOINTS.WXWORK.CONTACTS}?department_id=${departmentId}`
        : API_ENDPOINTS.WXWORK.CONTACTS
      
      return await this.request(url)
    } catch (error) {
      console.error('获取通讯录失败:', error)
      throw error
    }
  },

  /**
   * 获取部门列表
   * @returns {Promise} 部门列表
   */
  async getDepartmentList() {
    try {
      return await this.request(API_ENDPOINTS.WXWORK.DEPARTMENTS)
    } catch (error) {
      console.error('获取部门列表失败:', error)
      throw error
    }
  },

  /**
   * 发送企业微信消息
   * @param {object} messageData 消息数据
   * @returns {Promise} 发送结果
   */
  async sendMessage(messageData) {
    try {
      return await this.request(API_ENDPOINTS.WXWORK.SEND_MESSAGE, {
        method: 'POST',
        data: messageData
      })
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  },

  /**
   * 获取当前聊天用户信息
   * @returns {Promise} 当前聊天用户信息
   */
  getCurrentChatUser() {
    return new Promise((resolve, reject) => {
      if (typeof wx.qy.getChatInfo === 'function') {
        wx.qy.getChatInfo({
          success: (res) => {
            console.log('获取聊天信息成功:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('获取聊天信息失败:', error)
            reject(error)
          }
        })
      } else {
        // 如果不在企业微信聊天环境，返回空数据
        resolve(null)
      }
    })
  },

  /**
   * 发送AI消息到企业微信聊天
   * @param {string} message AI生成的消息内容
   * @param {object} chatUser 聊天用户信息
   * @returns {Promise} 发送结果
   */
  sendAIMessageToChat(message, chatUser) {
    return new Promise((resolve, reject) => {
      if (typeof wx.qy.sendChatMessage === 'function') {
        wx.qy.sendChatMessage({
          msgtype: 'text',
          text: {
            content: message
          },
          success: (res) => {
            console.log('发送AI消息成功:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('发送AI消息失败:', error)
            reject(error)
          }
        })
      } else {
        // 降级到普通消息发送
        this.sendMessage({
          touser: chatUser.userid,
          msgtype: 'text',
          content: message
        }).then(resolve).catch(reject)
      }
    })
  },

  /**
   * 获取用户在小程序中的个人信息
   * @param {string} userId 企业微信用户ID
   * @returns {Promise} 用户在小程序中的信息
   */
  async getUserProfileInMiniapp(userId) {
    try {
      return await this.request(`${API_ENDPOINTS.WXWORK.USER_PROFILE}/${userId}`)
    } catch (error) {
      console.error('获取用户小程序资料失败:', error)
      // 如果获取失败，返回null而不是抛出错误
      return null
    }
  },

  /**
   * 保存用户在小程序中的个人信息
   * @param {object} profileData 用户资料数据
   * @returns {Promise} 保存结果
   */
  async saveUserProfileInMiniapp(profileData) {
    try {
      return await this.request(API_ENDPOINTS.WXWORK.USER_PROFILE, {
        method: 'POST',
        data: profileData
      })
    } catch (error) {
      console.error('保存用户小程序资料失败:', error)
      throw error
    }
  },

  /**
   * 生成基于用户信息的AI对话提示
   * @param {object} userInfo 用户信息
   * @param {object} userProfile 用户在小程序中的资料
   * @returns {string} AI对话提示
   */
  generateAIPromptForUser(userInfo, userProfile) {
    let prompt = `你好，我是恒琦易道企业版的AI助手。\n\n`
    
    // 基于用户企业微信信息
    if (userInfo) {
      prompt += `根据您的企业信息：\n`
      if (userInfo.name) prompt += `- 姓名：${userInfo.name}\n`
      if (userInfo.department && userInfo.department.length > 0) {
        prompt += `- 部门：${userInfo.department.join(', ')}\n`
      }
      if (userInfo.position) prompt += `- 职位：${userInfo.position}\n`
    }

    // 基于用户在小程序中的资料
    if (userProfile) {
      prompt += `\n根据您在小程序中的资料：\n`
      if (userProfile.birthInfo) {
        const { birthDate, birthTime, zodiac } = userProfile.birthInfo
        if (birthDate) prompt += `- 出生日期：${birthDate}\n`
        if (birthTime) prompt += `- 出生时间：${birthTime}\n`
        if (zodiac) prompt += `- 生肖：${zodiac}\n`
      }
      if (userProfile.interests && userProfile.interests.length > 0) {
        prompt += `- 兴趣偏好：${userProfile.interests.join(', ')}\n`
      }
      if (userProfile.recentUsage && userProfile.recentUsage.length > 0) {
        prompt += `- 最近使用的功能：${userProfile.recentUsage.join(', ')}\n`
      }
    }

    prompt += `\n我可以为您提供个性化的命理分析、运势预测、风水建议等服务。请问您希望了解什么？`
    
    return prompt
  },

  /**
   * 调用AI生成回复
   * @param {string} userMessage 用户消息
   * @param {object} context 上下文信息（用户信息、历史对话等）
   * @returns {Promise} AI回复
   */
  async generateAIReply(userMessage, context = {}) {
    try {
      const requestData = {
        message: userMessage,
        user_info: {
          userid: context.userInfo?.userid || '',
          name: context.userInfo?.name || '',
          department: context.userInfo?.department || [],
          position: context.userInfo?.position || '',
          mobile: context.userInfo?.mobile || '',
          email: context.userInfo?.email || '',
          avatar: context.userInfo?.avatar || '',
          status: context.userInfo?.status || 'offline'
        },
        user_profile: context.userProfile,
        chat_history: context.chatHistory || [],
        chat_type: context.type || 'enterprise_chat'
      }

      const response = await this.request(API_ENDPOINTS.WXWORK.CHAT, {
        method: 'POST',
        data: requestData
      })

      return response.reply || response.response
    } catch (error) {
      console.error('AI回复生成失败:', error)
      throw error
    }
  },

  /**
   * 监听企业微信聊天用户切换
   * @param {function} callback 用户切换时的回调函数
   */
  onChatUserChange(callback) {
    if (typeof wx.qy.onChatChange === 'function') {
      wx.qy.onChatChange((chatInfo) => {
        console.log('聊天用户切换:', chatInfo)
        callback(chatInfo)
      })
    }
  },

  /**
   * 检查用户权限
   * @param {string} permission 权限名称
   * @returns {boolean} 是否有权限
   */
  checkPermission(permission) {
    const app = getApp()
    return app.globalData.permissions[permission] || false
  },

  /**
   * 获取用户详细信息
   * @param {string} userId 用户ID
   * @returns {Promise} 用户详细信息
   */
  async getUserDetail(userId) {
    try {
      return await this.request(`${API_ENDPOINTS.WXWORK.USER_DETAIL}/${userId}`)
    } catch (error) {
      console.error('获取用户详细信息失败:', error)
      throw error
    }
  },

  /**
   * 创建企业会议
   * @param {object} meetingData 会议数据
   * @returns {Promise} 创建结果
   */
  async createMeeting(meetingData) {
    if (!this.checkPermission('createMeeting')) {
      throw new Error('没有创建会议的权限')
    }
    
    try {
      // 实际项目中可以扩展为调用会议相关API
      return await this.request('/api/meetings', {
        method: 'POST',
        data: meetingData
      })
    } catch (error) {
      console.error('创建会议失败:', error)
      throw error
    }
  },

  /**
   * 发起审批流程
   * @param {object} approvalData 审批数据
   * @returns {Promise} 审批结果
   */
  async createApproval(approvalData) {
    try {
      // 实际项目中可以扩展为调用审批相关API
      return await this.request('/api/approvals', {
        method: 'POST',
        data: approvalData
      })
    } catch (error) {
      console.error('创建审批失败:', error)
      throw error
    }
  },

  /**
   * 获取审批列表
   * @param {object} filter 筛选条件
   * @returns {Promise} 审批列表
   */
  async getApprovalList(filter = {}) {
    try {
      const queryParams = new URLSearchParams(filter).toString()
      const url = queryParams ? `/api/approvals?${queryParams}` : '/api/approvals'
      
      return await this.request(url)
    } catch (error) {
      console.error('获取审批列表失败:', error)
      throw error
    }
  },

  /**
   * 处理审批
   * @param {string} approvalId 审批ID
   * @param {string} action 操作（approve/reject）
   * @param {string} comment 备注
   * @returns {Promise} 处理结果
   */
  async handleApproval(approvalId, action, comment = '') {
    try {
      return await this.request(`/api/approvals/${approvalId}/handle`, {
        method: 'POST',
        data: { action, comment }
      })
    } catch (error) {
      console.error('处理审批失败:', error)
      throw error
    }
  },

  /**
   * 显示成功提示
   * @param {string} message 提示信息
   */
  showSuccess(message) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  },

  /**
   * 显示错误提示
   * @param {string} message 错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  },

  /**
   * 格式化用户信息显示
   * @param {object} userInfo 用户信息
   * @returns {string} 格式化后的显示名称
   */
  formatUserDisplay(userInfo) {
    if (!userInfo) return '未知用户'
    
    const { name, department, position } = userInfo
    let display = name || '未知用户'
    
    if (department && position) {
      display += ` (${department} - ${position})`
    } else if (department) {
      display += ` (${department})`
    } else if (position) {
      display += ` (${position})`
    }
    
    return display
  },

  /**
   * 检查是否为管理员
   * @returns {boolean} 是否为管理员
   */
  isAdmin() {
    return this.checkPermission('admin')
  }
}

module.exports = wxworkUtil 