<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 通用设置 -->
  <view class="section">
    <view class="section-title">通用设置</view>
    <view class="setting-list">
      <!-- 深色模式 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/dark-mode.png"/>
          <text class="setting-name">深色模式</text>
        </view>
        <switch 
          checked="{{generalSettings.darkMode}}"
          bindchange="toggleSetting"
          data-type="general"
          data-key="darkMode"
        />
      </view>

      <!-- 语言设置 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/language.png"/>
          <text class="setting-name">语言</text>
        </view>
        <picker 
          mode="selector" 
          range="{{languageOptions}}" 
          range-key="label"
          value="{{languageOptions.findIndex(item => item.value === generalSettings.language)}}"
          bindchange="changeLanguage"
        >
          <view class="picker-text">
            {{languageOptions.find(item => item.value === generalSettings.language).label}}
          </view>
        </picker>
      </view>

      <!-- 字体大小 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/font-size.png"/>
          <text class="setting-name">字体大小</text>
        </view>
        <picker 
          mode="selector" 
          range="{{fontSizeOptions}}" 
          range-key="label"
          value="{{fontSizeOptions.findIndex(item => item.value === generalSettings.fontSize)}}"
          bindchange="changeFontSize"
        >
          <view class="picker-text">
            {{fontSizeOptions.find(item => item.value === generalSettings.fontSize).label}}
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="section">
    <view class="section-title">隐私设置</view>
    <view class="setting-list">
      <!-- 在线状态 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/online.png"/>
          <text class="setting-name">显示在线状态</text>
        </view>
        <switch 
          checked="{{privacySettings.showOnline}}"
          bindchange="toggleSetting"
          data-type="privacy"
          data-key="showOnline"
        />
      </view>

      <!-- 允许搜索 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/search.png"/>
          <text class="setting-name">允许他人搜索</text>
        </view>
        <switch 
          checked="{{privacySettings.allowSearch}}"
          bindchange="toggleSetting"
          data-type="privacy"
          data-key="allowSearch"
        />
      </view>

      <!-- 最后在线时间 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/last-seen.png"/>
          <text class="setting-name">显示最后在线时间</text>
        </view>
        <switch 
          checked="{{privacySettings.showLastSeen}}"
          bindchange="toggleSetting"
          data-type="privacy"
          data-key="showLastSeen"
        />
      </view>
    </view>
  </view>

  <!-- 消息通知 -->
  <view class="section">
    <view class="section-title">消息通知</view>
    <view class="setting-list">
      <!-- 推送通知 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/push.png"/>
          <text class="setting-name">推送通知</text>
        </view>
        <switch 
          checked="{{notificationSettings.pushEnabled}}"
          bindchange="toggleSetting"
          data-type="notification"
          data-key="pushEnabled"
        />
      </view>

      <!-- 声音 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/sound.png"/>
          <text class="setting-name">声音</text>
        </view>
        <switch 
          checked="{{notificationSettings.sound}}"
          bindchange="toggleSetting"
          data-type="notification"
          data-key="sound"
        />
      </view>

      <!-- 震动 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/vibrate.png"/>
          <text class="setting-name">震动</text>
        </view>
        <switch 
          checked="{{notificationSettings.vibrate}}"
          bindchange="toggleSetting"
          data-type="notification"
          data-key="vibrate"
        />
      </view>

      <!-- 消息预览 -->
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/preview.png"/>
          <text class="setting-name">显示消息预览</text>
        </view>
        <switch 
          checked="{{notificationSettings.showPreview}}"
          bindchange="toggleSetting"
          data-type="notification"
          data-key="showPreview"
        />
      </view>
    </view>
  </view>

  <!-- 存储空间 -->
  <view class="section">
    <view class="section-title">存储空间</view>
    <view class="setting-list">
      <view class="setting-item">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/storage.png"/>
          <text class="setting-name">缓存大小</text>
        </view>
        <text class="cache-size">{{cacheSize}}</text>
      </view>
      <button class="clear-btn" bindtap="clearCache">清除缓存</button>
    </view>
  </view>

  <!-- 关于 -->
  <view class="section">
    <view class="section-title">关于</view>
    <view class="setting-list">
      <view class="setting-item" bindtap="navigateToAbout">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/about.png"/>
          <text class="setting-name">关于我们</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png"/>
      </view>
      <view class="setting-item" bindtap="navigateToTerms">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/terms.png"/>
          <text class="setting-name">用户协议</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png"/>
      </view>
      <view class="setting-item" bindtap="navigateToPrivacy">
        <view class="setting-left">
          <image class="setting-icon" src="/assets/icons/privacy-policy.png"/>
          <text class="setting-name">隐私政策</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png"/>
      </view>
    </view>
  </view>
</view> 