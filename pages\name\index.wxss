.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.form-section {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.input {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.radio-group {
  display: flex;
  gap: 30rpx;
}

.radio {
  font-size: 28rpx;
  color: #333;
}

.picker {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.analyze-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4a5568;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 40rpx 0;
}

.result-section {
  margin-top: 40rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.wuge-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.grid-item {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.grid-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.grid-number {
  font-size: 36rpx;
  color: #4a5568;
  display: block;
  margin-bottom: 8rpx;
}

.grid-meaning {
  font-size: 24rpx;
  color: #666;
}

.score-section {
  text-align: center;
  padding: 30rpx 0;
}

.total-score {
  font-size: 72rpx;
  font-weight: bold;
  color: #4a5568;
}

.max-score {
  font-size: 32rpx;
  color: #666;
}

.score-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-top: 20rpx;
}

.wuxing-analysis {
  margin-top: 20rpx;
}

.wuxing-item {
  margin-bottom: 20rpx;
}

.element-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.name-details {
  margin-top: 20rpx;
}

.detail-item {
  margin-bottom: 20rpx;
}

.aspect-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.aspect-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.fortune-analysis {
  margin-top: 20rpx;
}

.fortune-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.stars {
  color: #f0b90b;
  font-size: 28rpx;
}

.name-suggestions {
  margin-top: 20rpx;
}

.suggestion-item {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.suggested-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.suggestion-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
} 