# AI聊天界面样式指南

## 目录
1. [设计原则](#设计原则)
2. [颜色系统](#颜色系统)
3. [组件样式](#组件样式)
4. [动画效果](#动画效果)
5. [响应式设计](#响应式设计)
6. [自定义主题](#自定义主题)

## 设计原则

AI聊天界面遵循以下设计原则：
- 简洁清晰：界面布局简单直观
- 视觉反馈：操作有即时响应
- 一致性：样式和交互保持统一
- 易用性：操作简单，功能明确

## 颜色系统

### 主要颜色
```css
/* 主题色 */
--primary-color: #007AFF;      /* 主要按钮、链接 */
--secondary-color: #8a2be2;    /* 次要按钮、强调 */

/* 文本颜色 */
--text-primary: #333333;       /* 主要文本 */
--text-secondary: #666666;     /* 次要文本 */
--text-hint: #999999;          /* 提示文本 */

/* 背景色 */
--bg-primary: #ffffff;         /* 主要背景 */
--bg-secondary: #f5f5f5;       /* 次要背景 */
--bg-message: #007AFF;         /* 用户消息背景 */
```

## 组件样式

### 1. 消息气泡
```css
.message-text {
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 用户消息 */
.user-message .message-text {
  background-color: var(--bg-message);
  color: #fff;
}

/* AI消息 */
.ai-message .message-text {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}
```

### 2. 输入框
```css
.message-input {
  flex: 1;
  height: 72rpx;
  background-color: var(--bg-secondary);
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
```

### 3. 发送按钮
```css
.send-button {
  width: 120rpx;
  height: 72rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
  line-height: 72rpx;
}

.send-button[disabled] {
  background-color: #ccc;
}
```

## 动画效果

### 1. 打字机效果
```css
.typing-animation {
  display: flex;
  padding: 20rpx;
  background-color: var(--bg-primary);
  border-radius: 10rpx;
}

.dot {
  width: 10rpx;
  height: 10rpx;
  background-color: var(--text-hint);
  border-radius: 50%;
  margin: 0 6rpx;
  animation: typing 1s infinite ease-in-out;
}

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}
```

### 2. 消息动画
```css
.message-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 响应式设计

### 1. 布局适配
```css
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.message-list {
  flex: 1;
  overflow-y: auto;
}

.input-area {
  padding: 20rpx;
  background-color: var(--bg-primary);
  border-top: 1rpx solid #eee;
}
```

### 2. 文本自适应
```css
.message-content {
  max-width: 70%;  /* 限制消息气泡最大宽度 */
}

.message-text {
  word-wrap: break-word;  /* 长文本自动换行 */
}
```

## 自定义主题

### 1. 主题切换
```javascript
// 在app.js中定义主题切换方法
const themes = {
  light: {
    '--bg-primary': '#ffffff',
    '--bg-secondary': '#f5f5f5',
    '--text-primary': '#333333'
  },
  dark: {
    '--bg-primary': '#1c1c1e',
    '--bg-secondary': '#2c2c2e',
    '--text-primary': '#ffffff'
  }
}

// 切换主题
function switchTheme(theme) {
  const root = document.documentElement
  const themeVars = themes[theme]
  Object.entries(themeVars).forEach(([key, value]) => {
    root.style.setProperty(key, value)
  })
}
```

### 2. 自定义颜色
```css
/* 定义新主题 */
.theme-custom {
  --primary-color: #ff6b6b;     /* 自定义主色 */
  --secondary-color: #4ecdc4;   /* 自定义辅色 */
  --bg-message: #ff6b6b;        /* 自定义消息背景 */
}
```

## 最佳实践

1. 样式命名
   - 使用BEM命名规范
   - 避免过深的选择器嵌套
   - 使用有意义的类名

2. 性能优化
   - 合理使用CSS动画
   - 避免频繁的样式计算
   - 使用transform代替位置属性

3. 主题定制
   - 使用CSS变量实现主题切换
   - 提供完整的主题配置接口
   - 保持样式的一致性

4. 适配建议
   - 使用rpx单位适配不同设备
   - 考虑极端情况下的显示效果
   - 测试不同机型的兼容性 