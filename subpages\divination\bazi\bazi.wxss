/* subpages/divination/bazi/bazi.wxss */

.bazi-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.bazi-header {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.header-content {
  position: relative;
  height: 100%;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(153, 50, 204, 0.8));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 输入表单 */
.bazi-form {
  padding: 30rpx;
  margin-top: -60rpx;
  position: relative;
  z-index: 1;
}

.form-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.title-tips {
  font-size: 24rpx;
  color: #999;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 性别选择 */
.gender-select {
  display: flex;
  gap: 20rpx;
}

.gender-option {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 28rpx;
  color: #666;
}

.gender-option.active {
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
}

.gender-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

/* 选择器样式 */
.picker-display {
  height: 88rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 20rpx;
}

/* 分析按钮 */
.analyze-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(138, 43, 226, 0.3);
}

.analyze-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.analyze-btn[disabled] {
  opacity: 0.6;
}

/* 分析结果 */
.bazi-result {
  padding: 0 30rpx;
  margin-top: 30rpx;
}

.result-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 28rpx;
  color: #666;
}

/* 八字展示 */
.bazi-display {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.bazi-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.bazi-row:last-child {
  margin-bottom: 0;
}

.bazi-item {
  text-align: center;
  flex: 1;
}

.bazi-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.bazi-char {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
}

.bazi-char.tian {
  color: #8a2be2;
}

.bazi-char.di {
  color: #ff6347;
}

/* 五行分析 */
.wuxing-section,
.analysis-section,
.fortune-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 24rpx;
}

.wuxing-chart {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.wuxing-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.wuxing-bar {
  width: 60rpx;
  background: linear-gradient(to top, #8a2be2, #9932cc);
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  min-height: 20rpx;
  margin-bottom: 10rpx;
  transition: height 0.5s ease;
}

.wuxing-value {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  color: #666;
}

.wuxing-name {
  font-size: 24rpx;
  color: #666;
}

.wuxing-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 命理解析 */
.analysis-item {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.analysis-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #8a2be2;
  display: block;
  margin-bottom: 12rpx;
}

.analysis-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 运势趋势 */
.fortune-timeline {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.fortune-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.fortune-year {
  font-size: 24rpx;
  color: #666;
  width: 80rpx;
}

.fortune-bar {
  flex: 1;
  height: 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}

.fortune-fill {
  height: 100%;
  background: linear-gradient(to right, #8a2be2, #9932cc);
  border-radius: 10rpx;
  transition: width 0.5s ease;
}

.fortune-score {
  font-size: 24rpx;
  color: #8a2be2;
  width: 60rpx;
  text-align: right;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e8e8e8;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 历史记录 */
.history-section {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-more {
  font-size: 26rpx;
  color: #8a2be2;
}

.history-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.2s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f8f9fa;
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-name {
  font-size: 28rpx;
  color: #333;
}

.history-date {
  font-size: 24rpx;
  color: #999;
}

.history-arrow {
  font-size: 32rpx;
  color: #ccc;
} 