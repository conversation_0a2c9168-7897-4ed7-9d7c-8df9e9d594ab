// 姓名测试页面逻辑
Page({
  data: {
    surname: '', // 姓氏
    givenName: '', // 名字
    gender: 'male', // 性别
    isLoading: false,
    testResult: null,
    canTest: false
  },

  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '姓名测试'
    });
  },

  // 姓氏输入
  onSurnameInput(e) {
    this.setData({
      surname: e.detail.value
    });
    this.checkCanTest();
  },

  // 名字输入
  onGivenNameInput(e) {
    this.setData({
      givenName: e.detail.value
    });
    this.checkCanTest();
  },

  // 性别选择
  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    });
  },

  // 检查是否可以开始测试
  checkCanTest() {
    const { surname, givenName } = this.data;
    this.setData({
      canTest: surname.trim() && givenName.trim()
    });
  },

  // 开始姓名测试
  async startNameTest() {
    const { surname, givenName, gender } = this.data;
    
    if (!surname.trim() || !givenName.trim()) {
      wx.showToast({
        title: '请输入完整姓名',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });

    try {
      // 调用姓名分析API
      const result = await this.analyzeNameFortune(surname, givenName, gender);
      
      this.setData({
        testResult: result,
        isLoading: false
      });

      // 保存到历史记录
      this.saveToHistory(result);

    } catch (error) {
      console.error('姓名测试失败:', error);
      this.setData({ isLoading: false });
      
      wx.showToast({
        title: '测试失败，请重试',
        icon: 'none'
      });
    }
  },

  // 姓名分析算法
  async analyzeNameFortune(surname, givenName, gender) {
    // 获取字符笔画数
    const surnameStrokes = this.getCharacterStrokes(surname);
    const givenNameStrokes = this.getCharacterStrokes(givenName);
    
    // 计算五格数理
    const wuge = this.calculateWuge(surnameStrokes, givenNameStrokes);
    
    // 计算三才配置
    const sancai = this.calculateSancai(wuge);
    
    // 生成详细分析
    const analysis = this.generateAnalysis(wuge, sancai, gender);
    
    // 计算总分
    const totalScore = this.calculateTotalScore(wuge, sancai);
    
    // 生成改名建议（如果分数较低）
    const suggestions = totalScore < 70 ? this.generateSuggestions(surname, gender) : null;

    return {
      fullName: surname + givenName,
      totalScore,
      wuge,
      sancai,
      analysis,
      suggestions
    };
  },

  // 获取字符笔画数（简化版本）
  getCharacterStrokes(chars) {
    const strokeMap = {
      // 常用字笔画数映射表（简化版本）
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
      '王': 4, '李': 7, '张': 11, '刘': 15, '陈': 16, '杨': 13, '黄': 12, '赵': 14,
      '周': 8, '吴': 7, '徐': 10, '孙': 10, '朱': 6, '马': 10, '胡': 9, '郭': 15,
      '林': 8, '何': 7, '高': 10, '罗': 19, '郑': 19, '梁': 11, '谢': 17, '宋': 7,
      '唐': 10, '许': 11, '邓': 19, '冯': 12, '韩': 17, '曹': 11, '曾': 12, '彭': 12,
      '萧': 18, '蔡': 17, '潘': 16, '田': 5, '董': 15, '袁': 10, '于': 3, '余': 7,
      '叶': 15, '蒋': 17, '杜': 7, '苏': 22, '魏': 18, '程': 12, '吕': 7, '丁': 2,
      '沈': 8, '任': 6, '姚': 9, '卢': 16, '傅': 12, '钟': 17, '姜': 9, '崔': 11,
      '谭': 19, '廖': 14, '范': 15, '汪': 8, '陆': 16, '金': 8, '石': 5, '戴': 18,
      // 常用名字
      '伟': 11, '芳': 10, '娜': 10, '敏': 11, '静': 16, '丽': 19, '强': 12, '磊': 15,
      '军': 9, '洋': 10, '勇': 9, '艳': 24, '杰': 12, '涛': 18, '明': 8, '超': 12,
      '秀': 7, '英': 11, '华': 14, '慧': 15, '巧': 5, '美': 9, '娟': 10, '琳': 13,
      '素': 10, '云': 12, '莲': 17, '真': 10, '环': 18, '雪': 11, '荣': 14, '爱': 13
    };

    let totalStrokes = 0;
    for (let char of chars) {
      // 如果在映射表中找到，使用映射的笔画数，否则使用字符编码计算
      totalStrokes += strokeMap[char] || (char.charCodeAt(0) % 20 + 1);
    }
    return totalStrokes;
  },

  // 计算五格数理
  calculateWuge(surnameStrokes, givenNameStrokes) {
    const surname = Array.isArray(surnameStrokes) ? surnameStrokes : [surnameStrokes];
    const givenName = Array.isArray(givenNameStrokes) ? givenNameStrokes : [givenNameStrokes];
    
    // 天格：姓氏笔画数+1（单姓）或姓氏两字笔画数之和（复姓）
    const tiange = surname.reduce((sum, stroke) => sum + stroke, 0) + (surname.length === 1 ? 1 : 0);
    
    // 人格：姓氏最后一字 + 名字第一字
    const renge = surname[surname.length - 1] + givenName[0];
    
    // 地格：名字笔画数之和（如果单名则+1）
    const dige = givenName.reduce((sum, stroke) => sum + stroke, 0) + (givenName.length === 1 ? 1 : 0);
    
    // 外格：天格 + 地格 - 人格
    const waige = tiange + dige - renge;
    
    // 总格：所有笔画数之和
    const zongge = surname.reduce((sum, stroke) => sum + stroke, 0) + givenName.reduce((sum, stroke) => sum + stroke, 0);

    return [
      { name: '天格', number: tiange, level: this.getNumberLevel(tiange), levelText: this.getLevelText(tiange) },
      { name: '人格', number: renge, level: this.getNumberLevel(renge), levelText: this.getLevelText(renge) },
      { name: '地格', number: dige, level: this.getNumberLevel(dige), levelText: this.getLevelText(dige) },
      { name: '外格', number: waige, level: this.getNumberLevel(waige), levelText: this.getLevelText(waige) },
      { name: '总格', number: zongge, level: this.getNumberLevel(zongge), levelText: this.getLevelText(zongge) }
    ];
  },

  // 计算三才配置
  calculateSancai(wuge) {
    const wuxing = ['木', '火', '土', '金', '水'];
    const tiange = wuge[0].number % 10;
    const renge = wuge[1].number % 10;
    const dige = wuge[2].number % 10;

    const tian = wuxing[Math.floor((tiange - 1) / 2) % 5];
    const ren = wuxing[Math.floor((renge - 1) / 2) % 5];
    const di = wuxing[Math.floor((dige - 1) / 2) % 5];

    return {
      tian,
      ren,
      di,
      analysis: this.getSancaiAnalysis(tian, ren, di)
    };
  },

  // 获取数字等级
  getNumberLevel(number) {
    const luckyNumbers = [1, 3, 5, 6, 7, 8, 11, 13, 15, 16, 17, 18, 21, 23, 24, 25, 29, 31, 32, 33, 35, 37, 39, 41, 45, 47, 48, 52, 57, 61, 63, 65, 67, 68, 81];
    const unluckyNumbers = [2, 4, 9, 10, 12, 14, 19, 20, 22, 26, 27, 28, 30, 34, 36, 38, 40, 42, 43, 44, 46, 49, 50, 51, 53, 54, 55, 56, 58, 59, 60, 62, 64, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80];
    
    if (luckyNumbers.includes(number)) return 'good';
    if (unluckyNumbers.includes(number)) return 'bad';
    return 'normal';
  },

  // 获取等级文字
  getLevelText(number) {
    const level = this.getNumberLevel(number);
    switch (level) {
      case 'good': return '吉';
      case 'bad': return '凶';
      default: return '平';
    }
  },

  // 获取三才分析
  getSancaiAnalysis(tian, ren, di) {
    const analyses = {
      '木木木': '性格坚强，不屈不挠，有深厚的仁德',
      '木木火': '成功顺调，希望易达，身心健全',
      '木火土': '颇有向上发展的生机，目的容易达到而成功',
      '火土金': '可获得意外成功发展，有名利双收的运气',
      '土金水': '成功运佳，可以发展，但有固执倾向'
    };
    
    const key = tian + ren + di;
    return analyses[key] || '三才配置一般，需要努力经营才能获得成功';
  },

  // 生成详细分析
  generateAnalysis(wuge, sancai, gender) {
    return [
      {
        title: '性格特征',
        content: this.getPersonalityAnalysis(wuge[1].number, gender),
        score: this.getScore(wuge[1].level)
      },
      {
        title: '事业财运',
        content: this.getCareerAnalysis(wuge[4].number),
        score: this.getScore(wuge[4].level)
      },
      {
        title: '婚姻感情',
        content: this.getMarriageAnalysis(wuge[2].number, gender),
        score: this.getScore(wuge[2].level)
      },
      {
        title: '健康状况',
        content: this.getHealthAnalysis(wuge[0].number),
        score: this.getScore(wuge[0].level)
      }
    ];
  },

  // 获取性格分析
  getPersonalityAnalysis(number, gender) {
    const genderText = gender === 'male' ? '他' : '她';
    const analyses = {
      1: `${genderText}具有领导才能，性格坚强独立`,
      2: `${genderText}性格温和，善于与人合作`,
      3: `${genderText}富有创造力，表达能力强`,
      4: `${genderText}做事踏实，但可能过于保守`,
      5: `${genderText}追求自由，喜欢变化和冒险`
    };
    return analyses[number % 5 + 1] || `${genderText}性格温和稳重，为人正直诚信`;
  },

  // 获取事业分析
  getCareerAnalysis(number) {
    const analyses = {
      1: '事业运势强劲，容易获得成功',
      2: '需要与人合作才能在事业上有所建树',
      3: '适合从事创意性工作，发展前景良好',
      4: '事业发展稳定，但进展相对缓慢',
      5: '事业多变，需要把握机会'
    };
    return analyses[number % 5 + 1] || '事业发展平稳，需要持续努力';
  },

  // 获取婚姻分析
  getMarriageAnalysis(number, gender) {
    const genderText = gender === 'male' ? '他' : '她';
    const analyses = {
      1: `${genderText}在感情中比较主动，容易找到合适的伴侣`,
      2: `${genderText}重视家庭和谐，婚姻生活美满`,
      3: `${genderText}感情丰富，但需要学会专一`,
      4: `${genderText}对感情认真负责，但表达能力需要提升`,
      5: `${genderText}向往自由的感情生活，需要理解的伴侣`
    };
    return analyses[number % 5 + 1] || `${genderText}感情运势一般，需要主动经营`;
  },

  // 获取健康分析
  getHealthAnalysis(number) {
    const analyses = {
      1: '身体健康状况良好，精力充沛',
      2: '需要注意情绪调节，避免过度忧虑',
      3: '活力充沛，但要注意劳逸结合',
      4: '体质较弱，需要加强锻炼',
      5: '精神状态良好，但要注意规律作息'
    };
    return analyses[number % 5 + 1] || '健康状况一般，需要注意保养';
  },

  // 根据等级获取分数
  getScore(level) {
    switch (level) {
      case 'good': return Math.floor(Math.random() * 20) + 80; // 80-99
      case 'bad': return Math.floor(Math.random() * 30) + 30; // 30-59
      default: return Math.floor(Math.random() * 20) + 60; // 60-79
    }
  },

  // 计算总分
  calculateTotalScore(wuge, sancai) {
    const scores = wuge.map(item => this.getScore(item.level));
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    return Math.round(average);
  },

  // 生成改名建议
  generateSuggestions(surname, gender) {
    const maleNames = ['志强', '建华', '志明', '俊杰', '文博'];
    const femaleNames = ['雅琳', '诗涵', '梦洁', '雨婷', '思怡'];
    const names = gender === 'male' ? maleNames : femaleNames;
    
    return names.map((name, index) => ({
      name: surname + name,
      score: Math.floor(Math.random() * 10) + 85,
      reason: '五格配置协调，三才搭配良好'
    }));
  },

  // 保存到历史记录
  saveToHistory(result) {
    try {
      const history = wx.getStorageSync('nameTestHistory') || [];
      const newRecord = {
        ...result,
        timestamp: new Date().getTime(),
        date: new Date().toLocaleDateString()
      };
      
      history.unshift(newRecord);
      // 只保留最近10条记录
      if (history.length > 10) {
        history.splice(10);
      }
      
      wx.setStorageSync('nameTestHistory', history);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  // 保存结果
  saveResult() {
    wx.showToast({
      title: '结果已保存',
      icon: 'success'
    });
  },

  // 分享结果
  shareResult() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 重新测试
  resetTest() {
    this.setData({
      surname: '',
      givenName: '',
      gender: 'male',
      testResult: null,
      canTest: false
    });
  },

  // 页面分享
  onShareAppMessage() {
    const { testResult } = this.data;
    return {
      title: testResult ? `我的姓名测试结果：${testResult.fullName} ${testResult.totalScore}分` : '来测试一下你的姓名吧',
      path: '/pages/name-test/name-test'
    };
  }
}); 