<!--pages/wxwork-chat/wxwork-chat.wxml-->
<view class="container">
  <!-- 顶部用户信息栏 -->
  <view class="user-info-bar" wx:if="{{currentChatUser}}">
    <view class="user-avatar">
      <image src="{{currentChatUser.avatar || '/assets/images/default-avatar.png'}}" class="avatar-image"></image>
      <view class="online-status {{currentChatUser.status}}"></view>
    </view>
    <view class="user-details">
      <view class="user-name">{{currentChatUser.name}}</view>
      <view class="user-info">
        <text wx:if="{{currentChatUser.department}}">{{currentChatUser.department}} · </text>
        <text wx:if="{{currentChatUser.position}}">{{currentChatUser.position}}</text>
      </view>
    </view>
    <view class="action-buttons">
      <button class="action-btn refresh-btn" bindtap="refreshUserInfo" size="mini">
        <icon type="refresh" size="16"></icon>
      </button>
      <button class="action-btn profile-btn" bindtap="viewUserProfile" size="mini">
        <icon type="person" size="16"></icon>
      </button>
    </view>
  </view>

  <!-- AI聊天消息列表 -->
  <scroll-view class="message-list" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}">
    <!-- 智能问候语 -->
    <view class="message-item ai-message" wx:if="{{welcomeMessage}}" id="msg-welcome">
      <view class="message-avatar">
        <image src="/assets/images/ai-avatar.png" class="avatar"></image>
      </view>
      <view class="message-content">
        <view class="message-bubble ai-bubble">
          <text class="message-text">{{welcomeMessage}}</text>
        </view>
        <view class="message-time">{{welcomeTime}}</view>
      </view>
    </view>

    <!-- 聊天消息 -->
    <view class="message-item {{item.type === 'ai' ? 'ai-message' : 'user-message'}}" 
          wx:for="{{messages}}" 
          wx:key="id"
          id="msg-{{item.id}}">
      <view class="message-avatar" wx:if="{{item.type === 'ai'}}">
        <image src="/assets/images/ai-avatar.png" class="avatar"></image>
      </view>
      
      <view class="message-content">
        <view class="message-bubble {{item.type === 'ai' ? 'ai-bubble' : 'user-bubble'}}">
          <text class="message-text">{{item.content}}</text>
          
          <!-- AI消息操作按钮 -->
          <view class="message-actions" wx:if="{{item.type === 'ai'}}">
            <button class="action-btn send-to-chat-btn" 
                    bindtap="sendToWxWorkChat" 
                    data-message="{{item.content}}"
                    size="mini">
              发送到聊天
            </button>
            <button class="action-btn copy-btn" 
                    bindtap="copyMessage" 
                    data-message="{{item.content}}"
                    size="mini">
              复制
            </button>
          </view>
        </view>
        <view class="message-time">{{item.time}}</view>
      </view>

      <view class="message-avatar" wx:if="{{item.type === 'user'}}">
        <image src="{{currentChatUser.avatar || '/assets/images/default-avatar.png'}}" class="avatar"></image>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-message" wx:if="{{isTyping}}" id="msg-loading">
      <view class="message-avatar">
        <image src="/assets/images/ai-avatar.png" class="avatar"></image>
      </view>
      <view class="message-content">
        <view class="message-bubble ai-bubble">
          <view class="typing-indicator">
            <text>AI正在思考</text>
            <view class="dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快速功能按钮 -->
  <view class="quick-actions" wx:if="{{!inputFocused}}">
    <scroll-view class="quick-scroll" scroll-x="true">
      <view class="quick-item" bindtap="askForFortune">
        <icon type="star" size="20"></icon>
        <text>今日运势</text>
      </view>
      <view class="quick-item" bindtap="askForBazi">
        <icon type="clock" size="20"></icon>
        <text>八字分析</text>
      </view>
      <view class="quick-item" bindtap="askForFengshui">
        <icon type="home" size="20"></icon>
        <text>风水建议</text>
      </view>
      <view class="quick-item" bindtap="askForNameTest">
        <icon type="person" size="20"></icon>
        <text>姓名测试</text>
      </view>
      <view class="quick-item" bindtap="askForMarriage">
        <icon type="heart" size="20"></icon>
        <text>婚姻配对</text>
      </view>
    </scroll-view>
  </view>

  <!-- 输入框 -->
  <view class="input-area">
    <view class="input-wrapper">
      <input class="message-input" 
             placeholder="请输入您的问题..."
             value="{{inputText}}"
             bindinput="onInputChange"
             bindconfirm="sendMessage"
             bindfocus="onInputFocus"
             bindblur="onInputBlur"
             confirm-type="send"
             maxlength="500"/>
      <button class="send-btn {{inputText ? 'active' : ''}}" 
              bindtap="sendMessage"
              disabled="{{!inputText || isTyping}}">
        发送
      </button>
    </view>
  </view>

  <!-- 用户资料弹窗 -->
  <view class="user-profile-modal {{showProfileModal ? 'show' : ''}}" wx:if="{{showProfileModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">用户资料</text>
        <icon class="modal-close" type="cancel" size="20" bindtap="closeProfileModal"></icon>
      </view>
      
      <view class="modal-body">
        <!-- 企业微信信息 -->
        <view class="profile-section">
          <view class="section-title">企业信息</view>
          <view class="info-item">
            <text class="label">姓名：</text>
            <text class="value">{{currentChatUser.name}}</text>
          </view>
          <view class="info-item" wx:if="{{currentChatUser.department}}">
            <text class="label">部门：</text>
            <text class="value">{{currentChatUser.department}}</text>
          </view>
          <view class="info-item" wx:if="{{currentChatUser.position}}">
            <text class="label">职位：</text>
            <text class="value">{{currentChatUser.position}}</text>
          </view>
          <view class="info-item" wx:if="{{currentChatUser.mobile}}">
            <text class="label">手机：</text>
            <text class="value">{{currentChatUser.mobile}}</text>
          </view>
        </view>

        <!-- 小程序资料 -->
        <view class="profile-section" wx:if="{{userProfile}}">
          <view class="section-title">小程序资料</view>
          <view class="info-item" wx:if="{{userProfile.birthInfo}}">
            <text class="label">出生信息：</text>
            <text class="value">{{userProfile.birthInfo.birthDate}} {{userProfile.birthInfo.birthTime}}</text>
          </view>
          <view class="info-item" wx:if="{{userProfile.zodiac}}">
            <text class="label">生肖：</text>
            <text class="value">{{userProfile.zodiac}}</text>
          </view>
          <view class="info-item" wx:if="{{userProfile.interests}}">
            <text class="label">兴趣：</text>
            <text class="value">{{userProfile.interests.join(', ')}}</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn" bindtap="generatePersonalizedChat">生成个性化对话</button>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="mask" wx:if="{{showProfileModal}}" bindtap="closeProfileModal"></view>
</view> 