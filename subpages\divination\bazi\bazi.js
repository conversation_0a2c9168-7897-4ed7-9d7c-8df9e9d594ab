Page({
  /**
   * 页面的初始数据
   */
  data: {
    name: '',
    gender: 'male',
    birthDate: '',
    birthTime: '',
    location: '',
    analyzing: false,
    showResult: false,
    resultData: null,
    historyList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadHistory()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 输入姓名
   */
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    })
  },

  /**
   * 选择性别
   */
  selectGender(e) {
    const gender = e.currentTarget.dataset.gender
    this.setData({
      gender: gender
    })
  },

  /**
   * 选择出生日期
   */
  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    })
  },

  /**
   * 选择出生时间
   */
  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    })
  },

  /**
   * 输入出生地点
   */
  onLocationInput(e) {
    this.setData({
      location: e.detail.value
    })
  },

  /**
   * 分析八字
   */
  analyzeBazi() {
    // 验证输入
    if (!this.data.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return
    }

    if (!this.data.birthDate) {
      wx.showToast({
        title: '请选择出生日期',
        icon: 'none'
      })
      return
    }

    if (!this.data.birthTime) {
      wx.showToast({
        title: '请选择出生时间',
        icon: 'none'
      })
      return
    }

    // 开始分析
    this.setData({
      analyzing: true
    })

    // 模拟分析过程
    setTimeout(() => {
      const result = this.calculateBazi()
      
      this.setData({
        analyzing: false,
        showResult: true,
        resultData: result
      })

      // 保存到历史记录
      this.saveToHistory(result)

      // 滚动到结果区域
      wx.pageScrollTo({
        selector: '.bazi-result',
        duration: 300
      })
    }, 2000)
  },

  /**
   * 计算八字（模拟）
   */
  calculateBazi() {
    // 模拟天干地支
    const tianGan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
    const diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']

    // 随机生成八字（实际应根据算法计算）
    const randomTianGan = () => tianGan[Math.floor(Math.random() * tianGan.length)]
    const randomDiZhi = () => diZhi[Math.floor(Math.random() * diZhi.length)]

    // 生成结果数据
    const result = {
      name: this.data.name,
      gender: this.data.gender,
      birthDate: this.data.birthDate,
      birthTime: this.data.birthTime,
      location: this.data.location,
      tianGan: [
        { label: '年', value: randomTianGan() },
        { label: '月', value: randomTianGan() },
        { label: '日', value: randomTianGan() },
        { label: '时', value: randomTianGan() }
      ],
      diZhi: [
        { label: '年', value: randomDiZhi() },
        { label: '月', value: randomDiZhi() },
        { label: '日', value: randomDiZhi() },
        { label: '时', value: randomDiZhi() }
      ],
      wuxing: [
        { name: '金', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },
        { name: '木', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },
        { name: '水', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },
        { name: '火', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },
        { name: '土', count: Math.floor(Math.random() * 4) + 1, percentage: 0 }
      ],
      wuxingDesc: '',
      personality: '',
      career: '',
      wealth: '',
      love: '',
      health: '',
      fortune: []
    }

    // 计算五行百分比
    const totalWuxing = result.wuxing.reduce((sum, item) => sum + item.count, 0)
    result.wuxing.forEach(item => {
      item.percentage = (item.count / totalWuxing) * 100
    })

    // 找出最多和最少的五行
    const maxWuxing = result.wuxing.reduce((max, item) => item.count > max.count ? item : max)
    const minWuxing = result.wuxing.reduce((min, item) => item.count < min.count ? item : min)

    result.wuxingDesc = `您的五行中${maxWuxing.name}最旺，${minWuxing.name}较弱。建议在生活中适当补充${minWuxing.name}元素，以达到五行平衡。`

    // 生成性格分析
    const personalities = [
      '您性格温和，待人诚恳，具有很强的责任心和进取心。',
      '您聪明机智，思维敏捷，善于把握机会，具有领导才能。',
      '您性格坚毅，做事踏实，有耐心和毅力，能够坚持到底。',
      '您性格开朗，乐观向上，人缘极佳，善于与人交往。'
    ]
    result.personality = personalities[Math.floor(Math.random() * personalities.length)]

    // 生成事业分析
    const careers = [
      '您适合从事管理、领导类工作，能够在事业上取得较大成就。近期有贵人相助，事业运势上升。',
      '您适合从事创意、艺术类工作，能够充分发挥自己的才华。今年是事业发展的关键期。',
      '您适合从事技术、研究类工作，专注和细心是您的优势。稳扎稳打必有所成。',
      '您适合从事商业、贸易类工作，善于把握商机。财运亨通，投资需谨慎。'
    ]
    result.career = careers[Math.floor(Math.random() * careers.length)]

    // 生成财运分析
    const wealths = [
      '您的财运较好，正财稳定，偏财运也不错。建议合理理财，避免冲动消费。',
      '您的财运平稳，收入稳定增长。今年有意外之财，但需注意守财。',
      '您的财运起伏较大，需要谨慎投资。建议以稳健理财为主。',
      '您的财运极佳，财源广进。适合开拓新的收入渠道，但切勿贪心。'
    ]
    result.wealth = wealths[Math.floor(Math.random() * wealths.length)]

    // 生成感情分析
    const loves = [
      '感情运势良好，单身者有望遇到心仪对象。已婚者感情稳定，家庭和睦。',
      '桃花运旺盛，异性缘佳。但需要慎重选择，避免烂桃花。',
      '感情需要用心经营，多些理解和包容。适合在今年步入婚姻殿堂。',
      '感情运平稳，需要主动出击。多参加社交活动，增加遇见良缘的机会。'
    ]
    result.love = loves[Math.floor(Math.random() * loves.length)]

    // 生成健康建议
    const healths = [
      '身体状况良好，但需注意劳逸结合。建议多运动，保持良好作息。',
      '需要注意肠胃健康，饮食要规律。适当补充维生素，增强免疫力。',
      '注意心血管健康，避免熬夜和过度劳累。定期体检很重要。',
      '整体健康状况不错，但要注意情绪管理。保持心情愉快有助健康。'
    ]
    result.health = healths[Math.floor(Math.random() * healths.length)]

    // 生成近期运势
    const currentYear = new Date().getFullYear()
    for (let i = 0; i < 5; i++) {
      result.fortune.push({
        year: currentYear + i,
        score: Math.floor(Math.random() * 30) + 70 // 70-100分
      })
    }

    return result
  },

  /**
   * 保存结果
   */
  saveResult() {
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    })
  },

  /**
   * 分享结果
   */
  shareResult() {
    wx.showToast({
      title: '请长按保存图片',
      icon: 'none'
    })
  },

  /**
   * 保存到历史记录
   */
  saveToHistory(result) {
    const history = wx.getStorageSync('baziHistory') || []
    const newRecord = {
      id: Date.now(),
      name: result.name,
      date: new Date().toLocaleDateString(),
      data: result
    }
    
    history.unshift(newRecord)
    // 只保留最近10条
    if (history.length > 10) {
      history.pop()
    }
    
    wx.setStorageSync('baziHistory', history)
    this.loadHistory()
  },

  /**
   * 加载历史记录
   */
  loadHistory() {
    const history = wx.getStorageSync('baziHistory') || []
    this.setData({
      historyList: history.slice(0, 3) // 只显示最近3条
    })
  },

  /**
   * 查看历史记录
   */
  viewHistory(e) {
    const id = e.currentTarget.dataset.id
    const history = wx.getStorageSync('baziHistory') || []
    const record = history.find(item => item.id === id)
    
    if (record) {
      this.setData({
        showResult: true,
        resultData: record.data
      })
      
      wx.pageScrollTo({
        selector: '.bazi-result',
        duration: 300
      })
    }
  },

  /**
   * 查看全部历史
   */
  viewAllHistory() {
    wx.navigateTo({
      url: '/pages/history/history?type=bazi'
    })
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    return {
      title: '八字分析 - 解析生辰八字，洞悉命运玄机',
      path: '/subpages/divination/bazi/bazi'
    }
  }
}) 