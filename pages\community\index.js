const app = getApp()

Page({
  data: {
    // 帖子列表
    posts: [],
    // 当前选中的分类
    currentCategory: 'all',
    // 分类列表
    categories: [
      { id: 'all', name: '全部' },
      { id: 'bazi', name: '八字' },
      { id: 'feng<PERSON><PERSON>', name: '风水' },
      { id: 'marriage', name: '姻缘' },
      { id: 'yijing', name: '易经' }
    ],
    // 加载状态
    loading: false,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    page: 1,
    // 每页数量
    pageSize: 10
  },

  onLoad() {
    this.loadPosts()
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2  // 社区的 tabBar 索引
      })
    }
  },

  // 切换分类
  switchCategory(e) {
    const { category } = e.currentTarget.dataset
    this.setData({
      currentCategory: category,
      posts: [],
      page: 1,
      hasMore: true
    }, () => {
      this.loadPosts()
    })
  },

  // 加载帖子列表
  async loadPosts() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    try {
      const res = await wx.cloud.callFunction({
        name: 'getPosts',
        data: {
          category: this.data.currentCategory,
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      })

      const { posts, hasMore } = res.result
      
      this.setData({
        posts: [...this.data.posts, ...posts],
        hasMore,
        page: this.data.page + 1,
        loading: false
      })
    } catch (err) {
      console.error('加载帖子失败:', err)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 点赞
  async handleLike(e) {
    const { postId, index } = e.currentTarget.dataset
    
    try {
      await wx.cloud.callFunction({
        name: 'likePost',
        data: { postId }
      })

      // 更新本地点赞状态
      const posts = [...this.data.posts]
      posts[index].liked = !posts[index].liked
      posts[index].likes = posts[index].liked ? 
        posts[index].likes + 1 : posts[index].likes - 1

      this.setData({ posts })
    } catch (err) {
      console.error('点赞失败:', err)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 评论
  handleComment(e) {
    const { postId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/post-detail/index?id=${postId}`
    })
  },

  // 分享
  handleShare(e) {
    const { postId } = e.currentTarget.dataset
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 发帖
  navigateToPost() {
    wx.navigateTo({
      url: '/pages/post-create/index'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      posts: [],
      page: 1,
      hasMore: true
    }, () => {
      this.loadPosts().then(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadPosts()
  },

  // 分享
  onShareAppMessage(res) {
    if (res.from === 'button') {
      const { postId, index } = res.target.dataset
      const post = this.data.posts[index]
      return {
        title: post.content.substring(0, 20) + '...',
        path: `/pages/post-detail/index?id=${postId}`,
        imageUrl: post.images ? post.images[0] : ''
      }
    }
    return {
      title: '命理社区',
      path: '/pages/community/index'
    }
  }
}) 