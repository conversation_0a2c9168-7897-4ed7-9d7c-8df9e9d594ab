/* pages/profile/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #07c160, #0ab856);
  padding: 40rpx 30rpx;
  color: #fff;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
}

.info-right {
  margin-left: 20rpx;
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-level {
  display: flex;
  align-items: center;
}

.level-tag {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.points {
  font-size: 24rpx;
  opacity: 0.9;
}

.card-bottom {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-num {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 功能区域 */
.section {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.grid-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.grid-text {
  font-size: 26rpx;
  color: #333;
}

/* 设置列表 */
.setting-list {
  background-color: #fff;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.setting-name {
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}

/* 退出登录 */
.logout-section {
  margin: 60rpx 20rpx;
}

.logout-btn {
  background-color: #fff;
  color: #ff4d4f;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: normal;
} 