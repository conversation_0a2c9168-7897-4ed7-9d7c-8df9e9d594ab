/* 姓名测试页面样式 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 60rpx;
}

.header {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 输入区域 */
.input-section {
  margin: 40rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.input-group:last-of-type {
  margin-bottom: 60rpx;
}

.label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.name-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  background: #f9f9f9;
}

.name-input:focus {
  border-color: #667eea;
  background: white;
}

.gender-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 30rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.test-btn[disabled] {
  background: #ccc;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  color: white;
}

.loading-icon {
  margin-bottom: 40rpx;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  opacity: 0.9;
}

/* 结果区域 */
.result-section {
  margin: 40rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.result-header {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.test-name {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.overall-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.score-label {
  font-size: 28rpx;
  opacity: 0.9;
}

.score-value {
  font-size: 64rpx;
  font-weight: bold;
}

/* 五格数理 */
.wuge-section {
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.wuge-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
}

.wuge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 12rpx;
}

.wuge-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.wuge-number {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.wuge-level {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  color: white;
}

.wuge-level.good {
  background: #52c41a;
}

.wuge-level.bad {
  background: #ff4d4f;
}

.wuge-level.normal {
  background: #faad14;
}

/* 三才配置 */
.sancai-section {
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.sancai-result {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.sancai-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.sancai-label {
  font-size: 24rpx;
  color: #666;
}

.sancai-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.sancai-analysis {
  background: #f6f8ff;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 详细分析 */
.analysis-section {
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.analysis-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 12rpx;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.analysis-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  margin-bottom: 15rpx;
}

.analysis-score {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.score-bar {
  flex: 1;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #ff4d4f 100%);
  transition: width 0.3s ease;
}

/* 改名建议 */
.suggestion-section {
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #fff7e6;
  border-radius: 12rpx;
  border-left: 6rpx solid #faad14;
}

.suggestion-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.suggestion-score {
  font-size: 28rpx;
  color: #fa8c16;
  font-weight: bold;
}

.suggestion-reason {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 40rpx;
}

.action-buttons button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.save-btn {
  background: #52c41a;
  color: white;
}

.share-btn {
  background: #1890ff;
  color: white;
}

.retest-btn {
  background: #f0f0f0;
  color: #333;
} 