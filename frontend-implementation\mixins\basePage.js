// 页面基础混入
import store from '../store/index'
import { showToast, showLoading, hideLoading, getStorageSync } from '../utils/wx'

/**
 * 基础页面混入
 * 提供通用的页面功能和状态管理
 */
export const basePageMixin = {
  data: {
    // 基础状态
    loading: false,
    error: null,
    
    // 用户状态
    isLogin: false,
    userInfo: null,
    
    // 系统信息
    systemInfo: null,
    
    // 网络状态
    networkStatus: 'online'
  },

  onLoad(options) {
    // 初始化页面状态
    this.initPageState()
    
    // 订阅状态变化
    this.subscribeStoreChanges()
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 调用子页面的onLoad
    if (this.pageOnLoad) {
      this.pageOnLoad(options)
    }
  },

  onShow() {
    // 更新页面状态
    this.updatePageState()
    
    // 调用子页面的onShow
    if (this.pageOnShow) {
      this.pageOnShow()
    }
  },

  onHide() {
    // 调用子页面的onHide
    if (this.pageOnHide) {
      this.pageOnHide()
    }
  },

  onUnload() {
    // 取消状态订阅
    this.unsubscribeStoreChanges()
    
    // 调用子页面的onUnload
    if (this.pageOnUnload) {
      this.pageOnUnload()
    }
  },

  /**
   * 初始化页面状态
   */
  initPageState() {
    const userState = store.getState('user')
    const appState = store.getState('app')
    
    this.setData({
      isLogin: userState.isLogin,
      userInfo: userState.userInfo,
      systemInfo: appState.systemInfo,
      networkStatus: appState.networkStatus
    })
  },

  /**
   * 更新页面状态
   */
  updatePageState() {
    const userState = store.getState('user')
    const appState = store.getState('app')
    
    this.setData({
      isLogin: userState.isLogin,
      userInfo: userState.userInfo,
      networkStatus: appState.networkStatus
    })
  },

  /**
   * 订阅状态变化
   */
  subscribeStoreChanges() {
    // 订阅用户状态变化
    this.unsubscribeUser = store.subscribe('user', (userState) => {
      this.setData({
        isLogin: userState.isLogin,
        userInfo: userState.userInfo
      })
    })
    
    // 订阅应用状态变化
    this.unsubscribeApp = store.subscribe('app', (appState) => {
      this.setData({
        loading: appState.loading,
        networkStatus: appState.networkStatus
      })
    })
  },

  /**
   * 取消状态订阅
   */
  unsubscribeStoreChanges() {
    if (this.unsubscribeUser) {
      this.unsubscribeUser()
    }
    if (this.unsubscribeApp) {
      this.unsubscribeApp()
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = getStorageSync('token')
    const userInfo = getStorageSync('userInfo')
    
    if (!token || !userInfo) {
      // 如果页面需要登录
      if (this.requireLogin) {
        this.redirectToLogin()
      }
    }
  },

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/index'
      })
    }, 1500)
  },

  /**
   * 显示加载状态
   * @param {string} title 加载提示文字
   */
  showLoading(title = '加载中...') {
    this.setData({ loading: true })
    showLoading({ title })
  },

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    this.setData({ loading: false })
    hideLoading()
  },

  /**
   * 显示错误信息
   * @param {string} message 错误信息
   * @param {string} icon 图标类型
   */
  showError(message, icon = 'none') {
    this.setData({ error: message })
    showToast({
      title: message,
      icon
    })
  },

  /**
   * 清除错误状态
   */
  clearError() {
    this.setData({ error: null })
  },

  /**
   * 安全的API调用
   * @param {function} apiCall API调用函数
   * @param {object} options 配置选项
   */
  async safeApiCall(apiCall, options = {}) {
    const {
      showLoading: shouldShowLoading = true,
      loadingText = '加载中...',
      showError: shouldShowError = true,
      onSuccess,
      onError,
      onFinally
    } = options

    try {
      if (shouldShowLoading) {
        this.showLoading(loadingText)
      }

      const result = await apiCall()

      if (onSuccess) {
        onSuccess(result)
      }

      return result
    } catch (error) {
      console.error('API调用失败:', error)

      if (shouldShowError) {
        this.showError(error.message || '操作失败')
      }

      if (onError) {
        onError(error)
      }

      throw error
    } finally {
      if (shouldShowLoading) {
        this.hideLoading()
      }

      if (onFinally) {
        onFinally()
      }
    }
  },

  /**
   * 防抖函数
   * @param {function} func 要防抖的函数
   * @param {number} delay 延迟时间
   */
  debounce(func, delay = 300) {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
    
    this.debounceTimer = setTimeout(() => {
      func.call(this)
    }, delay)
  },

  /**
   * 节流函数
   * @param {function} func 要节流的函数
   * @param {number} delay 延迟时间
   */
  throttle(func, delay = 300) {
    if (this.throttleTimer) {
      return
    }
    
    this.throttleTimer = setTimeout(() => {
      func.call(this)
      this.throttleTimer = null
    }, delay)
  },

  /**
   * 格式化日期
   * @param {Date|string} date 日期
   * @param {string} format 格式
   */
  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date)
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  },

  /**
   * 相对时间格式化
   * @param {Date|string} date 日期
   */
  formatRelativeTime(date) {
    const now = new Date()
    const target = new Date(date)
    const diff = now - target
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    
    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < week) {
      return `${Math.floor(diff / day)}天前`
    } else if (diff < month) {
      return `${Math.floor(diff / week)}周前`
    } else {
      return this.formatDate(date, 'YYYY-MM-DD')
    }
  },

  /**
   * 数字格式化
   * @param {number} num 数字
   * @param {number} precision 精度
   */
  formatNumber(num, precision = 0) {
    if (typeof num !== 'number') {
      return num
    }
    
    return num.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    return {
      title: this.shareTitle || '卦里乾坤 - 专业命理分析',
      path: this.sharePath || '/pages/index/index',
      imageUrl: this.shareImageUrl || '/images/share-default.png'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: this.shareTitle || '卦里乾坤 - 专业命理分析',
      query: this.shareQuery || '',
      imageUrl: this.shareImageUrl || '/images/share-timeline.png'
    }
  }
}

/**
 * 创建页面的便捷方法
 * @param {object} pageConfig 页面配置
 */
export const createPage = (pageConfig) => {
  // 合并基础混入和页面配置
  const mergedConfig = {
    ...basePageMixin,
    ...pageConfig
  }
  
  // 处理生命周期方法
  const lifecycleMethods = ['onLoad', 'onShow', 'onHide', 'onUnload']
  
  lifecycleMethods.forEach(method => {
    if (pageConfig[method]) {
      mergedConfig[`page${method.charAt(2).toUpperCase()}${method.slice(3)}`] = pageConfig[method]
    }
  })
  
  return Page(mergedConfig)
}

export default basePageMixin
