<!--聊天列表页面-->
<view class="page-container">
  <!-- 头部搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <view class="search-icon">🔍</view>
      <input 
        class="search-input"
        placeholder="搜索聊天记录"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="searchChats"
      />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 快速功能 -->
  <view class="quick-functions">
    <view class="function-item" bindtap="startNewChat">
      <view class="function-icon new-chat">💬</view>
      <text class="function-text">新建对话</text>
    </view>
    <view class="function-item" bindtap="viewFavoriteChats">
      <view class="function-icon favorite">⭐</view>
      <text class="function-text">收藏对话</text>
    </view>
    <view class="function-item" bindtap="exportChatHistory">
      <view class="function-icon export">📋</view>
      <text class="function-text">导出记录</text>
    </view>
  </view>

  <!-- 聊天列表 -->
  <view class="chat-list-container">
    <!-- 固定对话 -->
    <view class="pinned-section" wx:if="{{pinnedChats.length > 0}}">
      <view class="section-title">📌 置顶对话</view>
      <view class="chat-list">
        <view 
          class="chat-item pinned"
          wx:for="{{pinnedChats}}"
          wx:key="id"
          bindtap="openChat"
          data-id="{{item.id}}"
          bindlongpress="showChatOptions"
          data-chat="{{item}}"
        >
          <view class="chat-avatar">
            <image src="{{item.avatar || '/assets/icons/ai-avatar.png'}}" mode="aspectFill" />
            <view class="chat-status {{item.status}}"></view>
          </view>
          <view class="chat-content">
            <view class="chat-header">
              <text class="chat-title">{{item.title}}</text>
              <text class="chat-time">{{item.lastMessageTime}}</text>
            </view>
            <view class="chat-preview">
              <text class="last-message">{{item.lastMessage}}</text>
              <view class="chat-badges">
                <view class="unread-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount}}</view>
                <view class="chat-type">{{item.chatType}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 普通对话 -->
    <view class="normal-section">
      <view class="section-title" wx:if="{{pinnedChats.length > 0}}">💬 全部对话</view>
      
      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view 
          class="filter-tab {{activeFilter === item.value ? 'active' : ''}}"
          wx:for="{{filterTabs}}"
          wx:key="value"
          bindtap="switchFilter"
          data-filter="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>

      <!-- 聊天列表 -->
      <view class="chat-list">
        <view 
          class="chat-item {{item.isPinned ? 'pinned' : ''}}"
          wx:for="{{filteredChats}}"
          wx:key="id"
          bindtap="openChat"
          data-id="{{item.id}}"
          bindlongpress="showChatOptions"
          data-chat="{{item}}"
        >
          <view class="chat-avatar">
            <image src="{{item.avatar || '/assets/icons/ai-avatar.png'}}" mode="aspectFill" />
            <view class="chat-status {{item.status}}"></view>
            <view class="chat-type-badge">{{item.chatTypeIcon}}</view>
          </view>
          <view class="chat-content">
            <view class="chat-header">
              <text class="chat-title">{{item.title}}</text>
              <text class="chat-time">{{item.lastMessageTime}}</text>
            </view>
            <view class="chat-preview">
              <text class="last-message">{{item.lastMessage}}</text>
              <view class="chat-badges">
                <view class="unread-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount}}</view>
                <view class="favorite-icon" wx:if="{{item.isFavorite}}">⭐</view>
              </view>
            </view>
          </view>
          <view class="chat-actions" wx:if="{{item.showActions}}">
            <view class="action-btn pin" bindtap="togglePin" data-id="{{item.id}}">
              {{item.isPinned ? '取消置顶' : '置顶'}}
            </view>
            <view class="action-btn delete" bindtap="deleteChat" data-id="{{item.id}}">删除</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{filteredChats.length === 0 && !isLoading}}">
        <view class="empty-icon">💭</view>
        <view class="empty-title">暂无聊天记录</view>
        <view class="empty-desc">开始您的第一次AI对话吧</view>
        <button class="start-chat-btn" bindtap="startNewChat">开始对话</button>
      </view>

      <!-- 加载状态 -->
      <view class="loading-state" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>

  <!-- 底部工具栏 -->
  <view class="bottom-toolbar">
    <view class="toolbar-item" bindtap="selectAll">
      <view class="toolbar-icon">✓</view>
      <text>全选</text>
    </view>
    <view class="toolbar-item" bindtap="batchDelete">
      <view class="toolbar-icon">🗑️</view>
      <text>删除</text>
    </view>
    <view class="toolbar-item" bindtap="batchExport">
      <view class="toolbar-icon">📤</view>
      <text>导出</text>
    </view>
    <view class="toolbar-item" bindtap="clearAllChats">
      <view class="toolbar-icon">🧹</view>
      <text>清空</text>
    </view>
  </view>

  <!-- 操作弹窗 -->
  <view class="action-modal" wx:if="{{showActionModal}}">
    <view class="modal-mask" bindtap="hideActionModal"></view>
    <view class="modal-content">
      <view class="modal-title">{{selectedChat.title}}</view>
      <view class="action-list">
        <view class="action-item" bindtap="togglePinChat">
          <view class="action-icon">📌</view>
          <text>{{selectedChat.isPinned ? '取消置顶' : '置顶对话'}}</text>
        </view>
        <view class="action-item" bindtap="toggleFavoriteChat">
          <view class="action-icon">⭐</view>
          <text>{{selectedChat.isFavorite ? '取消收藏' : '收藏对话'}}</text>
        </view>
        <view class="action-item" bindtap="renameChat">
          <view class="action-icon">✏️</view>
          <text>重命名</text>
        </view>
        <view class="action-item" bindtap="exportSingleChat">
          <view class="action-icon">📋</view>
          <text>导出记录</text>
        </view>
        <view class="action-item danger" bindtap="confirmDeleteChat">
          <view class="action-icon">🗑️</view>
          <text>删除对话</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 重命名弹窗 -->
  <view class="rename-modal" wx:if="{{showRenameModal}}">
    <view class="modal-mask" bindtap="hideRenameModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>重命名对话</text>
        <view class="close-btn" bindtap="hideRenameModal">×</view>
      </view>
      <view class="modal-body">
        <input 
          class="rename-input"
          placeholder="请输入新的对话名称"
          value="{{newChatName}}"
          bindinput="onRenameInput"
          maxlength="20"
        />
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="hideRenameModal">取消</button>
        <button class="confirm-btn" bindtap="confirmRename">确定</button>
      </view>
    </view>
  </view>
</view> 