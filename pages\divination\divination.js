// pages/divination/divination.js
const app = getApp()
const divinationCalculator = require('../../utils/divination')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    selectedType: 'yijing',
    question: '',
    isLoading: false,
    divinationResult: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('事件占卜页面加载')
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('事件占卜页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('事件占卜页面显示')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('事件占卜页面隐藏')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('事件占卜页面卸载')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('事件占卜页面下拉刷新')
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('事件占卜页面上拉触底')
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '玄学占卜',
      path: '/pages/divination/divination',
      imageUrl: '/images/share.png'
    }
  },

  /**
   * 选择占卜类型
   */
  selectType(e) {
    const type = e.currentTarget.dataset.type
    console.log('选择占卜类型:', type)
    this.setData({
      selectedType: type,
      divinationResult: null
    })
  },

  /**
   * 问题输入处理
   */
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    })
  },

  /**
   * 开始占卜
   */
  startDivination() {
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入您的问题',
        icon: 'none'
      })
      return
    }

    console.log('开始占卜，问题:', this.data.question, '类型:', this.data.selectedType)
    this.setData({ isLoading: true })
    
    wx.showLoading({
      title: '正在占卜...',
    })

    try {
      // 根据选择的占卜方式进行计算
      let result
      switch (this.data.selectedType) {
        case 'yijing':
          result = divinationCalculator.calculateYijing(this.data.question)
          break
        case 'tarot':
          result = divinationCalculator.calculateTarot(this.data.question)
          break
        case 'ziwei':
          result = divinationCalculator.calculateZiwei(this.data.question)
          break
        default:
          throw new Error('不支持的占卜类型')
      }

      console.log('占卜结果:', result)
      this.setData({
        divinationResult: result,
        isLoading: false
      })

      wx.hideLoading()
    } catch (error) {
      console.error('占卜出错：', error)
      wx.hideLoading()
      wx.showToast({
        title: '占卜出错，请重试',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  }
})