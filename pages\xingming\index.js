const app = getApp()
const { BaziCalculator } = require('../../utils/bazi/calculator')
const { SancaiChart } = require('../../utils/charts')
const { XingmingCalculator } = require('../../utils/xingming/calculator')

Page({
  data: {
    // 基本信息
    surname: '',
    givenName: '',
    gender: '',
    birthDate: '',
    birthTime: '',
    canSubmit: false,
    price: 28,

    // 结果展示
    showResult: false,
    totalScore: 0,
    scoreDesc: '',
    wugeList: [],
    sancaiAnalysis: '',
    baziInfo: {
      year: '',
      month: '',
      day: '',
      time: ''
    },
    baziAnalysis: '',
    jixiongList: [],
    adviceList: []
  },

  onLoad() {
    // 初始化三才图表
    this.sancaiChart = new SancaiChart()
  },

  // 姓氏输入
  onSurnameInput(e) {
    this.setData({
      surname: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 名字输入
  onGivenNameInput(e) {
    this.setData({
      givenName: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 性别选择
  onGenderSelect(e) {
    const { gender } = e.currentTarget.dataset
    this.setData({ gender })
    this.checkCanSubmit()
  },

  // 出生日期选择
  onBirthDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 出生时间选择
  onBirthTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { surname, givenName, gender, birthDate, birthTime } = this.data
    const canSubmit = surname && givenName && gender && birthDate && birthTime
    this.setData({ canSubmit })
  },

  // 开始分析
  async onAnalyze() {
    if (!this.data.canSubmit) return

    wx.showLoading({ title: '正在分析...' })

    try {
      // 计算八字
      const baziCalculator = new BaziCalculator({
        name: this.data.surname + this.data.givenName,
        gender: this.data.gender,
        birthDate: this.data.birthDate,
        birthTime: this.data.birthTime
      })

      const baziResult = await baziCalculator.calculate()

      // 姓名测算
      const calculator = new XingmingCalculator({
        surname: this.data.surname,
        givenName: this.data.givenName,
        gender: this.data.gender,
        bazi: baziResult
      })

      const result = await calculator.calculate()

      // 更新结果
      this.setData({
        showResult: true,
        totalScore: result.score,
        scoreDesc: result.description,
        wugeList: result.wugeList,
        sancaiAnalysis: result.sancaiAnalysis,
        baziInfo: {
          year: `${baziResult.year.gan}${baziResult.year.zhi}`,
          month: `${baziResult.month.gan}${baziResult.month.zhi}`,
          day: `${baziResult.day.gan}${baziResult.day.zhi}`,
          time: `${baziResult.time.gan}${baziResult.time.zhi}`
        },
        baziAnalysis: result.baziAnalysis,
        jixiongList: result.jixiongList,
        adviceList: result.adviceList
      })

      // 绘制三才图表
      this.drawSancaiChart(result.sancaiData)

      wx.hideLoading()
    } catch (error) {
      console.error('分析失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      })
    }
  },

  // 绘制三才图表
  drawSancaiChart(data) {
    this.sancaiChart.draw('sancaiCanvas', data)
  },

  // 分享功能
  onShareAppMessage() {
    const { surname, givenName, totalScore } = this.data
    return {
      title: `${surname}${givenName}的姓名测算结果：${totalScore}分`,
      path: '/pages/xingming/index',
      imageUrl: '/assets/images/share-cover.jpg'
    }
  },

  // 页面卸载
  onUnload() {
    // 清理图表实例
    if (this.sancaiChart) {
      this.sancaiChart.dispose()
    }
  }
}) 