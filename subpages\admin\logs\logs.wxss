/* subpages/admin/logs/logs.wxss */

.logs-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

.logs-header {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.logs-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8a2be2;
  display: block;
  margin-bottom: 20rpx;
  text-align: center;
}

.logs-filters {
  display: flex;
  gap: 10rpx;
  justify-content: center;
}

.filter-btn {
  background: #f8f9fa;
  color: #666;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
}

.filter-btn:not(.active):active {
  background: #e9ecef;
}

.logs-content {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.log-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #ddd;
  transition: all 0.3s ease;
}

.log-item.error {
  border-left-color: #f44336;
  background: #fef5f5;
}

.log-item.warning {
  border-left-color: #ff9800;
  background: #fff8f0;
}

.log-item.info {
  border-left-color: #2196F3;
  background: #f0f8ff;
}

.log-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.log-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.log-time {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
}

.log-level {
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #ddd;
  color: #666;
}

.log-item.error .log-level {
  background: #f44336;
  color: white;
}

.log-item.warning .log-level {
  background: #ff9800;
  color: white;
}

.log-item.info .log-level {
  background: #2196F3;
  color: white;
}

.log-message {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: block;
}

.log-source {
  font-size: 20rpx;
  color: #999;
  font-family: monospace;
  display: block;
}

.empty-logs {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.logs-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  padding: 20rpx;
}

.action-btn {
  border: none;
  border-radius: 20rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e8e8e8;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
} 