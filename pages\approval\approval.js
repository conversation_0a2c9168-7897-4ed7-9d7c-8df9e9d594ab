const app = getApp()
const wxworkUtil = app.globalData.wxworkUtil

Page({
  data: {
    currentTab: 0,
    approvals: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 筛选条件
    statusOptions: ['全部', '待审批', '已通过', '已拒绝', '已撤销'],
    statusIndex: 0,
    dateFilter: '',
    
    // 审批操作弹窗
    showModal: false,
    modalAction: '',
    currentApprovalId: '',
    comment: ''
  },

  onLoad() {
    this.loadApprovals()
  },

  onShow() {
    // 每次显示页面时刷新当前标签页的数据
    this.refreshCurrentTab()
  },

  // 切换标签页
  switchTab(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentTab: index,
      approvals: [],
      page: 1,
      hasMore: true
    })
    this.loadApprovals()
  },

  // 刷新当前标签页
  refreshCurrentTab() {
    this.setData({
      approvals: [],
      page: 1,
      hasMore: true
    })
    this.loadApprovals()
  },

  // 加载审批列表
  async loadApprovals() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    try {
      const filter = this.buildFilter()
      const result = await wxworkUtil.getApprovalList(filter)
      
      const newApprovals = result.list || []
      const allApprovals = this.data.page === 1 ? newApprovals : [...this.data.approvals, ...newApprovals]
      
      this.setData({
        approvals: allApprovals,
        hasMore: newApprovals.length === this.data.pageSize,
        loading: false,
        page: this.data.page + 1
      })
    } catch (error) {
      console.error('加载审批列表失败:', error)
      wxworkUtil.showError('加载审批列表失败')
      this.setData({ loading: false })
    }
  },

  // 构建筛选条件
  buildFilter() {
    const { currentTab, statusIndex, dateFilter, page, pageSize } = this.data
    
    let type = ''
    switch (currentTab) {
      case 0: type = 'initiated'; break
      case 1: type = 'pending'; break
      case 2: type = 'handled'; break
    }

    let status = ''
    if (statusIndex > 0) {
      const statusMap = ['', 'pending', 'approved', 'rejected', 'cancelled']
      status = statusMap[statusIndex]
    }

    return {
      type,
      status,
      date: dateFilter,
      page,
      pageSize
    }
  },

  // 状态筛选变化
  onStatusChange(e) {
    const statusIndex = e.detail.value
    this.setData({ statusIndex })
    this.refreshCurrentTab()
  },

  // 日期筛选变化
  onDateChange(e) {
    const dateFilter = e.detail.value
    this.setData({ dateFilter })
    this.refreshCurrentTab()
  },

  // 查看审批详情
  viewApprovalDetail(e) {
    const approval = e.currentTarget.dataset.approval
    wx.navigateTo({
      url: `/pages/approval-detail/approval-detail?id=${approval.id}`
    })
  },

  // 处理审批
  handleApproval(e) {
    const { id, action } = e.currentTarget.dataset
    this.setData({
      showModal: true,
      modalAction: action,
      currentApprovalId: id,
      comment: ''
    })
  },

  // 关闭审批操作弹窗
  closeModal() {
    this.setData({
      showModal: false,
      modalAction: '',
      currentApprovalId: '',
      comment: ''
    })
  },

  // 审批意见输入
  onCommentInput(e) {
    this.setData({ comment: e.detail.value })
  },

  // 确认审批操作
  async confirmApproval() {
    const { currentApprovalId, modalAction, comment } = this.data
    
    if (!currentApprovalId) return

    try {
      wx.showLoading({ title: '处理中...' })
      
      await wxworkUtil.handleApproval(currentApprovalId, modalAction, comment)
      
      wx.hideLoading()
      wxworkUtil.showSuccess(`审批${modalAction === 'approve' ? '通过' : '拒绝'}成功`)
      
      this.closeModal()
      this.refreshCurrentTab()
    } catch (error) {
      wx.hideLoading()
      console.error('审批操作失败:', error)
      wxworkUtil.showError('审批操作失败')
    }
  },

  // 创建审批
  createApproval() {
    wx.navigateTo({
      url: '/pages/approval-create/approval-create'
    })
  },

  // 加载更多
  loadMore() {
    this.loadApprovals()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshCurrentTab()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '恒琦易道企业版 - 审批流程',
      path: '/pages/approval/approval',
      imageUrl: '/assets/images/share-approval.png'
    }
  },

  // 批量操作（管理员功能）
  batchApprove() {
    if (!wxworkUtil.isAdmin()) {
      wxworkUtil.showError('只有管理员可以批量操作')
      return
    }

    wx.showModal({
      title: '批量审批',
      content: '确定要批量通过所有待审批项目吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '处理中...' })
            
            const pendingApprovals = this.data.approvals.filter(item => item.status === 'pending')
            const promises = pendingApprovals.map(item => 
              wxworkUtil.handleApproval(item.id, 'approve', '批量审批通过')
            )
            
            await Promise.all(promises)
            
            wx.hideLoading()
            wxworkUtil.showSuccess('批量审批完成')
            this.refreshCurrentTab()
          } catch (error) {
            wx.hideLoading()
            console.error('批量审批失败:', error)
            wxworkUtil.showError('批量审批失败')
          }
        }
      }
    })
  }
}) 