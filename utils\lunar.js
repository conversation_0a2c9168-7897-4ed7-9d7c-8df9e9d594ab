// 农历数据
const LUNAR_INFO = [
  0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
  0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
  0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
  0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
  0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
  0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5d0, 0x14573, 0x052d0, 0x0a9a8, 0x0e950, 0x06aa0,
  0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
  0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b5a0, 0x195a6,
  0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
  0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0,
  0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5
]

// 农历月份
const LUNAR_MONTHS = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊']
// 农历日期
const LUNAR_DAYS = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
  '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
  '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十']

const solarTerm = ['小寒', '大寒', '立春', '雨水', '惊蛰', '春分', '清明', '谷雨', '立夏', '小满', '芒种', '夏至', '小暑', '大暑', '立秋', '处暑', '白露', '秋分', '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'];

const Gan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const Zhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
const Animals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

const nStr1 = ['日', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
const nStr2 = ['初', '十', '廿', '卅'];

// 宜忌数据
const yijiData = {
  '0': { yi: ['祭祀', '祈福', '求嗣', '开光', '出行'], ji: ['开市', '入宅', '动土', '破土'] },
  '1': { yi: ['嫁娶', '纳采', '订盟', '祭祀', '祈福'], ji: ['开市', '安床', '安葬', '入宅'] },
  '2': { yi: ['祈福', '求嗣', '开光', '塑绘', '斋醮'], ji: ['嫁娶', '开市', '交易', '安葬'] },
  '3': { yi: ['沐浴', '捕捉', '畋猎', '理发', '整手足甲'], ji: ['祈福', '嫁娶', '入宅', '安葬'] },
  '4': { yi: ['破屋', '坏垣', '求医', '治病', '余事勿取'], ji: ['嫁娶', '开市', '交易', '祈福'] },
  '5': { yi: ['祭祀', '解除', '破屋', '坏垣', '余事勿取'], ji: ['诸事不宜'] },
  '6': { yi: ['嫁娶', '纳采', '订盟', '祭祀', '祈福'], ji: ['开市', '安床', '安葬', '入宅'] }
};

class LunarCalendar {
  constructor() {
    this.baseDate = new Date(1900, 0, 31)
  }

  // 公历转农历 - 简化版本
  solarToLunar(year, month, day) {
    // 简化的农历转换，使用近似算法
    // 这里使用一个简化的实现，主要用于显示

    // 计算距离1900年的年数
    const yearOffset = year - 1900

    // 简化的农历年计算
    let lunarYear = 1900 + yearOffset

    // 简化的月份计算（公历月份减1，因为农历通常比公历晚）
    let lunarMonth = month
    let lunarDay = day

    // 简单的调整逻辑
    if (day > 15) {
      // 如果是月中后，可能是下个农历月
      lunarMonth = month
      lunarDay = day - 15
    } else {
      // 如果是月初，可能是上个农历月
      if (month === 1) {
        lunarMonth = 12
        lunarYear = year - 1
      } else {
        lunarMonth = month - 1
      }
      lunarDay = day + 15
    }

    // 确保月份在有效范围内
    if (lunarMonth > 12) {
      lunarMonth = 1
      lunarYear++
    }
    if (lunarMonth < 1) {
      lunarMonth = 12
      lunarYear--
    }

    // 确保日期在有效范围内
    if (lunarDay > 30) {
      lunarDay = 30
    }
    if (lunarDay < 1) {
      lunarDay = 1
    }

    return {
      year: lunarYear,
      month: lunarMonth,
      day: lunarDay,
      isLeap: false
    }
  }

  // 获取农历年的总天数
  getLunarYearDays(lunarYear) {
    let sum = 348
    for (let i = 0x8000; i > 0x8; i >>= 1) {
      sum += (lunarYear & i) ? 1 : 0
    }
    return sum + this.getLeapDays(lunarYear)
  }

  // 获取农历年闰月的天数
  getLeapDays(lunarYear) {
    if (this.getLeapMonth(lunarYear)) {
      return (lunarYear & 0x10000) ? 30 : 29
    }
    return 0
  }

  // 获取农历年闰月月份
  getLeapMonth(lunarYear) {
    return lunarYear & 0xf
  }

  // 获取农历年每月的天数
  getMonthDays(lunarYear) {
    const monthDays = []
    for (let i = 0; i < 13; i++) {
      monthDays[i] = (lunarYear & (0x10000 >> i)) ? 30 : 29
    }
    return monthDays
  }

  // 获取农历月份名称
  getLunarMonthName(month) {
    if (month < 1 || month > 12) {
      return '正' // 默认返回正月
    }
    return LUNAR_MONTHS[month - 1]
  }

  // 获取农历日期名称
  getLunarDayName(day) {
    if (day < 1 || day > 30) {
      return '初一' // 默认返回初一
    }
    return LUNAR_DAYS[day - 1] || '初一'
  }

  // 获取今日信息
  getTodayInfo() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()

    // 获取星期
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    const weekday = weekdays[now.getDay()]

    // 获取农历信息
    const lunarInfo = this.solarToLunar(year, month, day)

    // 获取干支年
    const gzYear = this.getGanZhiYear(lunarInfo.year)

    // 获取生肖
    const animal = Animals[(lunarInfo.year - 4) % 12]

    // 获取农历月份和日期的中文名称
    const monthCn = this.getLunarMonthName(lunarInfo.month)
    const dayCn = this.getLunarDayName(lunarInfo.day)

    // 获取宜忌信息
    const yiji = this.getYiJi(year, month, day)

    // 获取节气信息
    const term = this.getSolarTerm(year, month, day)

    return {
      solar: {
        year,
        month,
        day,
        weekday
      },
      lunar: {
        year: lunarInfo.year,
        month: lunarInfo.month,
        day: lunarInfo.day,
        monthCn,
        dayCn,
        gzYear,
        animal,
        isLeap: lunarInfo.isLeap,
        yiji,
        term
      }
    }
  }

  // 获取干支年
  getGanZhiYear(lunarYear) {
    const ganIndex = (lunarYear - 4) % 10
    const zhiIndex = (lunarYear - 4) % 12
    return Gan[ganIndex] + Zhi[zhiIndex]
  }

  // 获取宜忌信息
  getYiJi(year, month, day) {
    // 简化的宜忌算法，基于日期计算
    const dayOfYear = this.getDayOfYear(year, month, day)
    const index = dayOfYear % 7
    return yijiData[index.toString()] || yijiData['0']
  }

  // 获取一年中的第几天
  getDayOfYear(year, month, day) {
    const date = new Date(year, month - 1, day)
    const start = new Date(year, 0, 1)
    return Math.floor((date - start) / (24 * 60 * 60 * 1000)) + 1
  }

  // 获取节气信息
  getSolarTerm(year, month, day) {
    // 简化的节气判断
    const solarTerms = {
      '2-4': '立春', '2-19': '雨水',
      '3-5': '惊蛰', '3-20': '春分',
      '4-4': '清明', '4-20': '谷雨',
      '5-5': '立夏', '5-21': '小满',
      '6-5': '芒种', '6-21': '夏至',
      '7-7': '小暑', '7-22': '大暑',
      '8-7': '立秋', '8-23': '处暑',
      '9-7': '白露', '9-23': '秋分',
      '10-8': '寒露', '10-23': '霜降',
      '11-7': '立冬', '11-22': '小雪',
      '12-7': '大雪', '12-22': '冬至',
      '1-5': '小寒', '1-20': '大寒'
    }

    const key = `${month}-${day}`
    return solarTerms[key] || null
  }
}

// 创建单例实例
const lunar = new LunarCalendar()

// 导出模块
module.exports = {
  // 导出实例的所有方法
  solarToLunar: lunar.solarToLunar.bind(lunar),
  getLunarYearDays: lunar.getLunarYearDays.bind(lunar),
  getLeapDays: lunar.getLeapDays.bind(lunar),
  getLeapMonth: lunar.getLeapMonth.bind(lunar),
  getMonthDays: lunar.getMonthDays.bind(lunar),
  getLunarMonthName: lunar.getLunarMonthName.bind(lunar),
  getLunarDayName: lunar.getLunarDayName.bind(lunar),
  getTodayInfo: lunar.getTodayInfo.bind(lunar),
  getGanZhiYear: lunar.getGanZhiYear.bind(lunar),
  getYiJi: lunar.getYiJi.bind(lunar),
  getDayOfYear: lunar.getDayOfYear.bind(lunar),
  getSolarTerm: lunar.getSolarTerm.bind(lunar),

  // 保持向后兼容，也导出实例本身
  lunar
}