/* wuxing.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

/* 输入区域样式 */
.input-section {
  background-color: var(--card-background);
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.input-group {
  margin-bottom: 20rpx;
}

.picker {
  padding: 20rpx;
  background-color: var(--primary-lightest);
  border-radius: 12rpx;
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.radio {
  margin-right: 30rpx;
  color: var(--text-primary);
}

.calculate-btn {
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 结果区域样式 */
.result-section {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 五行分布图样式 */
.wuxing-chart {
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.distribution-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.element-name {
  width: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.element-strength {
  width: 80rpx;
  font-size: 28rpx;
  color: #666;
  text-align: right;
}

.strength-bar {
  flex: 1;
  height: 30rpx;
  background-color: #eee;
  border-radius: 15rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

/* 分析区域样式 */
.analysis-section {
  margin-top: 30rpx;
}

.analysis-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.item-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.item-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 五行关系样式 */
.relationships-section {
  margin-top: 30rpx;
}

.relationships-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.relationship-item {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.relationship-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.relationship-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
} 