<!--pages/profile/profile.wxml-->
<view class="page-container">
  <!-- 加载组件 -->
  <loading show="{{isLoading}}" text="加载中..." />
  
  <!-- 顶部背景 -->
  <view class="profile-header">
    <image class="header-bg" src="https://img.freepik.com/free-vector/gradient-purple-aesthetic-background_23-2149123739.jpg" mode="aspectFill" />
    <view class="header-overlay"></view>
    
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-main">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl || 'https://img.icons8.com/fluency/96/user-male-circle.png'}}" 
          mode="aspectFill"
          bindtap="editAvatar"
        />
      <view class="user-info">
          <view class="user-name" bindtap="editNickname">
            {{userInfo.nickName || '点击登录'}}
            <text class="edit-icon" wx:if="{{userInfo.userId}}">✏️</text>
          </view>
          <view class="user-desc" wx:if="{{userInfo.userId}}">
            <text class="user-id">ID: {{userInfo.userId}}</text>
            <text class="user-level">{{userLevel.name}}</text>
          </view>
          <button class="login-btn gradient-btn" wx:if="{{!userInfo.userId}}" bindtap="login">
            立即登录
          </button>
        </view>
      </view>
      
      <!-- 用户统计 -->
      <view class="user-stats">
        <view class="stat-item" bindtap="goToPoints">
          <text class="stat-value">{{userPoints || 0}}</text>
          <text class="stat-label">积分</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" bindtap="goToCollection">
          <text class="stat-value">{{userStats.collections || 0}}</text>
          <text class="stat-label">收藏</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" bindtap="goToHistory">
          <text class="stat-value">{{userStats.history || 0}}</text>
          <text class="stat-label">测算</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到卡片 -->
  <view class="sign-card" wx:if="{{!todaySignIn}}" bindtap="handleSignIn">
    <view class="sign-icon">✨</view>
    <view class="sign-content">
      <text class="sign-title">每日签到</text>
      <text class="sign-desc">已连续签到 {{signInDays}} 天</text>
    </view>
    <view class="sign-action">签到领积分</view>
  </view>

  <!-- 常用功能 -->
  <view class="quick-section">
    <view class="section-header">
      <text class="section-title">常用功能</text>
    </view>
    <view class="quick-grid">
      <view class="quick-item" bindtap="navigateTo" data-url="/pages/my-posts/my-posts">
        <view class="quick-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
          📝
        </view>
        <text class="quick-text">我的帖子</text>
        <text class="quick-badge" wx:if="{{userStats.posts}}">{{userStats.posts}}</text>
      </view>
      <view class="quick-item" bindtap="navigateTo" data-url="/pages/my-favorites/my-favorites">
        <view class="quick-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
          ⭐
        </view>
        <text class="quick-text">我的收藏</text>
        <text class="quick-badge" wx:if="{{userStats.collections}}">{{userStats.collections}}</text>
      </view>
      <view class="quick-item" bindtap="navigateTo" data-url="/pages/my-history/my-history">
        <view class="quick-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
          📊
        </view>
        <text class="quick-text">测算记录</text>
        <text class="quick-badge" wx:if="{{userStats.history}}">{{userStats.history}}</text>
      </view>
      <view class="quick-item" bindtap="navigateTo" data-url="/pages/sign-in/sign-in">
        <view class="quick-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
          📅
        </view>
        <text class="quick-text">签到中心</text>
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="menu-list">
    <!-- 个人管理 -->
    <view class="menu-group">
      <view class="group-title">个人管理</view>
      <view class="menu-item" bindtap="editBirthInfo">
        <text class="menu-icon">🎂</text>
          <text class="menu-text">出生信息</text>
        <text class="menu-value">{{birthInfo.name || '未设置'}}</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="editPersonalInfo">
        <text class="menu-icon">👤</text>
          <text class="menu-text">个人资料</text>
        <text class="menu-arrow">›</text>
        </view>
      <view class="menu-item" bindtap="goToMessages">
        <text class="menu-icon">💬</text>
        <text class="menu-text">消息中心</text>
        <text class="menu-badge" wx:if="{{unreadCount}}">{{unreadCount}}</text>
        <text class="menu-arrow">›</text>
    </view>
  </view>

    <!-- 积分等级 -->
    <view class="menu-group">
      <view class="group-title">积分等级</view>
      <view class="menu-item" bindtap="goToPoints">
        <text class="menu-icon">💎</text>
          <text class="menu-text">积分中心</text>
        <text class="menu-value">{{userPoints}}分</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="goToLevel">
        <text class="menu-icon">🏆</text>
        <text class="menu-text">等级特权</text>
        <text class="menu-value">{{userLevel.name}}</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/recharge/recharge">
        <text class="menu-icon">💳</text>
          <text class="menu-text">充值记录</text>
        <text class="menu-arrow">›</text>
    </view>
  </view>

    <!-- 设置相关 -->
    <view class="menu-group">
      <view class="group-title">设置</view>
      <view class="menu-item" bindtap="switchTheme">
        <text class="menu-icon">🎨</text>
          <text class="menu-text">主题模式</text>
        <text class="menu-value">{{currentTheme}}</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/settings/settings">
        <text class="menu-icon">⚙️</text>
          <text class="menu-text">更多设置</text>
        <text class="menu-arrow">›</text>
    </view>
  </view>

    <!-- 帮助反馈 -->
    <view class="menu-group">
      <view class="group-title">帮助与支持</view>
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/customer-service/customer-service">
        <text class="menu-icon">🎧</text>
          <text class="menu-text">联系客服</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/feedback/feedback">
        <text class="menu-icon">💌</text>
          <text class="menu-text">意见反馈</text>
        <text class="menu-arrow">›</text>
      </view>
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/about/about">
        <text class="menu-icon">ℹ️</text>
          <text class="menu-text">关于我们</text>
        <text class="menu-value">v{{appVersion}}</text>
        <text class="menu-arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo.userId}}">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 底部空白 -->
  <view class="bottom-space"></view>
</view>
