// 命理解读工具类
const interpretationData = {
  // 紫微星落宫解读
  '紫微': {
    '命宫': '紫微入命宫，主贵气，性格刚毅，有领导才能，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '紫微入兄弟宫，主兄弟富贵，但易与兄弟争权夺利。与兄弟姐妹关系复杂，既有竞争又有合作，需注意平衡。',
    '夫妻': '紫微入夫妻宫，主配偶富贵，但易有婚姻波折。婚姻生活较为复杂，需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '紫微入子女宫，主子女富贵，但易与子女有代沟。子女教育需注意方式方法，避免过于专制导致亲子关系紧张。',
    '财帛': '紫微入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '紫微入疾厄宫，主身体康健，但易有头部疾病。需注意保养头部，避免过度劳累导致头痛、失眠等问题。',
    '迁移': '紫微入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免冒险行为，保持谨慎态度。',
    '仆役': '紫微入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '紫微入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '紫微入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '紫微入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '紫微入父母宫，主父母富贵，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 天机星落宫解读
  '天机': {
    '命宫': '天机入命宫，主聪明才智，思维敏捷，但易多疑。为人机智灵活，善于思考，但有时过于敏感，易陷入多疑状态。',
    '兄弟': '天机入兄弟宫，主兄弟聪明，但易与兄弟有分歧。与兄弟姐妹关系需注意沟通，避免因多疑导致关系紧张。',
    '夫妻': '天机入夫妻宫，主配偶聪明，但易有婚姻波折。婚姻生活需注意信任，避免因多疑导致婚姻危机。',
    '子女': '天机入子女宫，主子女聪明，但易与子女有代沟。子女教育需注意方式方法，避免因多疑导致亲子关系紧张。',
    '财帛': '天机入财帛宫，主财运多变，但易有投机之虞。理财需谨慎，避免因投机取巧导致损失。',
    '疾厄': '天机入疾厄宫，主身体多变，但易有神经系统疾病。需注意保养神经系统，避免因过度紧张导致神经衰弱。',
    '迁移': '天机入迁移宫，主外出多变，但易有意外之虞。外出需注意安全，避免因多变导致意外。',
    '仆役': '天机入仆役宫，主下属多变，但易与下属有矛盾。管理下属需注意方式方法，避免因多变导致团队矛盾。',
    '官禄': '天机入官禄宫，主官运多变，但易有官非之虞。事业发展需谨慎，避免因多变导致职场危机。',
    '田宅': '天机入田宅宫，主房产多变，但易有房产纠纷。房产投资需谨慎，避免因多变导致损失。',
    '福德': '天机入福德宫，主福气多变，但易有精神压力。需注意心理健康，避免因多变导致精神紧张。',
    '父母': '天机入父母宫，主父母多变，但易与父母有代沟。与父母关系需注意沟通，避免因多变导致家庭矛盾。'
  },
  // 太阳星落宫解读
  '太阳': {
    '命宫': '太阳入命宫，主光明磊落，性格开朗，但易刚愎自用。为人正直，光明磊落，但有时过于自信，不易接受他人意见。',
    '兄弟': '太阳入兄弟宫，主兄弟光明，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '太阳入夫妻宫，主配偶光明，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因刚愎自用导致婚姻危机。',
    '子女': '太阳入子女宫，主子女光明，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '太阳入财帛宫，主财运光明，但易有破财之虞。理财需谨慎，避免因冲动消费导致损失。',
    '疾厄': '太阳入疾厄宫，主身体光明，但易有心脏疾病。需注意保养心脏，避免因过度劳累导致心脏问题。',
    '迁移': '太阳入迁移宫，主外出光明，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '太阳入仆役宫，主下属光明，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '太阳入官禄宫，主官运光明，但易有官非之虞。事业发展需谨慎，避免因刚愎自用导致职场危机。',
    '田宅': '太阳入田宅宫，主房产光明，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '太阳入福德宫，主福气光明，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '太阳入父母宫，主父母光明，但易与父母有代沟。与父母关系需注意沟通，避免因专制导致家庭矛盾。'
  },
  // 武曲星落宫解读
  '武曲': {
    '命宫': '武曲入命宫，主财运亨通，性格刚毅，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '武曲入兄弟宫，主兄弟财运，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '武曲入夫妻宫，主配偶财运，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '武曲入子女宫，主子女财运，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '武曲入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '武曲入疾厄宫，主身体康健，但易有骨骼疾病。需注意保养骨骼，避免因过度劳累导致骨骼问题。',
    '迁移': '武曲入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '武曲入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '武曲入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '武曲入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '武曲入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '武曲入父母宫，主父母财运，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 天梁星落宫解读
  '天梁': {
    '命宫': '天梁入命宫，主贵人相助，性格温和，但易优柔寡断。为人正直，善良，但有时过于优柔寡断，不易做出决定。',
    '兄弟': '天梁入兄弟宫，主兄弟贵人，但易与兄弟有分歧。与兄弟姐妹关系需注意沟通，避免因优柔寡断导致关系紧张。',
    '夫妻': '天梁入夫妻宫，主配偶贵人，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因优柔寡断导致婚姻危机。',
    '子女': '天梁入子女宫，主子女贵人，但易与子女有代沟。子女教育需注意方式方法，避免因优柔寡断导致亲子关系紧张。',
    '财帛': '天梁入财帛宫，主财运稳定，但易有破财之虞。理财需谨慎，避免因优柔寡断导致损失。',
    '疾厄': '天梁入疾厄宫，主身体稳定，但易有慢性疾病。需注意保养身体，避免因优柔寡断导致健康问题。',
    '迁移': '天梁入迁移宫，主外出稳定，但易有意外之虞。外出需注意安全，避免因优柔寡断导致意外。',
    '仆役': '天梁入仆役宫，主下属稳定，但易与下属有矛盾。管理下属需注意方式方法，避免因优柔寡断导致团队矛盾。',
    '官禄': '天梁入官禄宫，主官运稳定，但易有官非之虞。事业发展需谨慎，避免因优柔寡断导致职场危机。',
    '田宅': '天梁入田宅宫，主房产稳定，但易有房产纠纷。房产投资需谨慎，避免因优柔寡断导致损失。',
    '福德': '天梁入福德宫，主福气稳定，但易有精神压力。需注意心理健康，避免因优柔寡断导致精神紧张。',
    '父母': '天梁入父母宫，主父母贵人，但易与父母有代沟。与父母关系需注意沟通，避免因优柔寡断导致家庭矛盾。'
  },
  // 天府星落宫解读
  '天府': {
    '命宫': '天府入命宫，主富贵，性格温和，但易优柔寡断。为人正直，善良，但有时过于优柔寡断，不易做出决定。',
    '兄弟': '天府入兄弟宫，主兄弟富贵，但易与兄弟有分歧。与兄弟姐妹关系需注意沟通，避免因优柔寡断导致关系紧张。',
    '夫妻': '天府入夫妻宫，主配偶富贵，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因优柔寡断导致婚姻危机。',
    '子女': '天府入子女宫，主子女富贵，但易与子女有代沟。子女教育需注意方式方法，避免因优柔寡断导致亲子关系紧张。',
    '财帛': '天府入财帛宫，主财运稳定，但易有破财之虞。理财需谨慎，避免因优柔寡断导致损失。',
    '疾厄': '天府入疾厄宫，主身体稳定，但易有慢性疾病。需注意保养身体，避免因优柔寡断导致健康问题。',
    '迁移': '天府入迁移宫，主外出稳定，但易有意外之虞。外出需注意安全，避免因优柔寡断导致意外。',
    '仆役': '天府入仆役宫，主下属稳定，但易与下属有矛盾。管理下属需注意方式方法，避免因优柔寡断导致团队矛盾。',
    '官禄': '天府入官禄宫，主官运稳定，但易有官非之虞。事业发展需谨慎，避免因优柔寡断导致职场危机。',
    '田宅': '天府入田宅宫，主房产稳定，但易有房产纠纷。房产投资需谨慎，避免因优柔寡断导致损失。',
    '福德': '天府入福德宫，主福气稳定，但易有精神压力。需注意心理健康，避免因优柔寡断导致精神紧张。',
    '父母': '天府入父母宫，主父母富贵，但易与父母有代沟。与父母关系需注意沟通，避免因优柔寡断导致家庭矛盾。'
  },
  // 太阴星落宫解读
  '太阴': {
    '命宫': '太阴入命宫，主阴柔，性格温和，但易优柔寡断。为人正直，善良，但有时过于优柔寡断，不易做出决定。',
    '兄弟': '太阴入兄弟宫，主兄弟阴柔，但易与兄弟有分歧。与兄弟姐妹关系需注意沟通，避免因优柔寡断导致关系紧张。',
    '夫妻': '太阴入夫妻宫，主配偶阴柔，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因优柔寡断导致婚姻危机。',
    '子女': '太阴入子女宫，主子女阴柔，但易与子女有代沟。子女教育需注意方式方法，避免因优柔寡断导致亲子关系紧张。',
    '财帛': '太阴入财帛宫，主财运稳定，但易有破财之虞。理财需谨慎，避免因优柔寡断导致损失。',
    '疾厄': '太阴入疾厄宫，主身体稳定，但易有慢性疾病。需注意保养身体，避免因优柔寡断导致健康问题。',
    '迁移': '太阴入迁移宫，主外出稳定，但易有意外之虞。外出需注意安全，避免因优柔寡断导致意外。',
    '仆役': '太阴入仆役宫，主下属稳定，但易与下属有矛盾。管理下属需注意方式方法，避免因优柔寡断导致团队矛盾。',
    '官禄': '太阴入官禄宫，主官运稳定，但易有官非之虞。事业发展需谨慎，避免因优柔寡断导致职场危机。',
    '田宅': '太阴入田宅宫，主房产稳定，但易有房产纠纷。房产投资需谨慎，避免因优柔寡断导致损失。',
    '福德': '太阴入福德宫，主福气稳定，但易有精神压力。需注意心理健康，避免因优柔寡断导致精神紧张。',
    '父母': '太阴入父母宫，主父母阴柔，但易与父母有代沟。与父母关系需注意沟通，避免因优柔寡断导致家庭矛盾。'
  },
  // 贪狼星落宫解读
  '贪狼': {
    '命宫': '贪狼入命宫，主欲望强烈，性格刚毅，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '贪狼入兄弟宫，主兄弟欲望强烈，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '贪狼入夫妻宫，主配偶欲望强烈，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '贪狼入子女宫，主子女欲望强烈，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '贪狼入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '贪狼入疾厄宫，主身体康健，但易有骨骼疾病。需注意保养骨骼，避免因过度劳累导致骨骼问题。',
    '迁移': '贪狼入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '贪狼入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '贪狼入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '贪狼入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '贪狼入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '贪狼入父母宫，主父母欲望强烈，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 巨门星落宫解读
  '巨门': {
    '命宫': '巨门入命宫，主口才出众，性格刚毅，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '巨门入兄弟宫，主兄弟口才出众，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '巨门入夫妻宫，主配偶口才出众，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '巨门入子女宫，主子女口才出众，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '巨门入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '巨门入疾厄宫，主身体康健，但易有骨骼疾病。需注意保养骨骼，避免因过度劳累导致骨骼问题。',
    '迁移': '巨门入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '巨门入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '巨门入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '巨门入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '巨门入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '巨门入父母宫，主父母口才出众，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 廉贞星落宫解读
  '廉贞': {
    '命宫': '廉贞入命宫，主正直，性格刚毅，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '廉贞入兄弟宫，主兄弟正直，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '廉贞入夫妻宫，主配偶正直，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '廉贞入子女宫，主子女正直，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '廉贞入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '廉贞入疾厄宫，主身体康健，但易有骨骼疾病。需注意保养骨骼，避免因过度劳累导致骨骼问题。',
    '迁移': '廉贞入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '廉贞入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '廉贞入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '廉贞入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '廉贞入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '廉贞入父母宫，主父母正直，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 破军星落宫解读
  '破军': {
    '命宫': '破军入命宫，主破旧立新，性格刚毅，但易刚愎自用。为人正直，有责任感，但有时过于固执己见，不易接受他人意见。',
    '兄弟': '破军入兄弟宫，主兄弟破旧立新，但易与兄弟争权夺利。与兄弟姐妹关系需注意沟通，避免因争权夺利导致关系紧张。',
    '夫妻': '破军入夫妻宫，主配偶破旧立新，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因固执导致婚姻危机。',
    '子女': '破军入子女宫，主子女破旧立新，但易与子女有代沟。子女教育需注意方式方法，避免因专制导致亲子关系紧张。',
    '财帛': '破军入财帛宫，主财运亨通，但易有破财之虞。理财能力较强，但需注意风险控制，避免因冲动投资导致损失。',
    '疾厄': '破军入疾厄宫，主身体康健，但易有骨骼疾病。需注意保养骨骼，避免因过度劳累导致骨骼问题。',
    '迁移': '破军入迁移宫，主外出顺利，但易有意外之虞。外出需注意安全，避免因冒险导致意外。',
    '仆役': '破军入仆役宫，主下属得力，但易与下属有矛盾。管理下属需注意方式方法，避免因专制导致团队矛盾。',
    '官禄': '破军入官禄宫，主官运亨通，但易有官非之虞。事业发展顺利，但需注意人际关系，避免因刚愎自用导致职场危机。',
    '田宅': '破军入田宅宫，主房产丰厚，但易有房产纠纷。房产投资需谨慎，避免因冲动购买导致损失。',
    '福德': '破军入福德宫，主福气深厚，但易有精神压力。需注意心理健康，避免因过度追求完美导致精神紧张。',
    '父母': '破军入父母宫，主父母破旧立新，但易与父母有代沟。与父母关系需注意沟通，避免因固执导致家庭矛盾。'
  },
  // 天相星落宫解读
  '天相': {
    '命宫': '天相入命宫，主相得益彰，性格温和，但易优柔寡断。为人正直，善良，但有时过于优柔寡断，不易做出决定。',
    '兄弟': '天相入兄弟宫，主兄弟相得益彰，但易与兄弟有分歧。与兄弟姐妹关系需注意沟通，避免因优柔寡断导致关系紧张。',
    '夫妻': '天相入夫妻宫，主配偶相得益彰，但易有婚姻波折。婚姻生活需注意沟通与包容，避免因优柔寡断导致婚姻危机。',
    '子女': '天相入子女宫，主子女相得益彰，但易与子女有代沟。子女教育需注意方式方法，避免因优柔寡断导致亲子关系紧张。',
    '财帛': '天相入财帛宫，主财运稳定，但易有破财之虞。理财需谨慎，避免因优柔寡断导致损失。',
    '疾厄': '天相入疾厄宫，主身体稳定，但易有慢性疾病。需注意保养身体，避免因优柔寡断导致健康问题。',
    '迁移': '天相入迁移宫，主外出稳定，但易有意外之虞。外出需注意安全，避免因优柔寡断导致意外。',
    '仆役': '天相入仆役宫，主下属稳定，但易与下属有矛盾。管理下属需注意方式方法，避免因优柔寡断导致团队矛盾。',
    '官禄': '天相入官禄宫，主官运稳定，但易有官非之虞。事业发展需谨慎，避免因优柔寡断导致职场危机。',
    '田宅': '天相入田宅宫，主房产稳定，但易有房产纠纷。房产投资需谨慎，避免因优柔寡断导致损失。',
    '福德': '天相入福德宫，主福气稳定，但易有精神压力。需注意心理健康，避免因优柔寡断导致精神紧张。',
    '父母': '天相入父母宫，主父母相得益彰，但易与父母有代沟。与父母关系需注意沟通，避免因优柔寡断导致家庭矛盾。'
  }
}

// 辅星解读
const minorStarInterpretation = {
  '擎羊': '主刚毅，但易刚愎自用。',
  '陀罗': '主阴柔，但易优柔寡断。',
  '火星': '主热情，但易冲动。',
  '铃星': '主聪明，但易多疑。',
  '文昌': '主文才，但易书呆子。',
  '文曲': '主口才，但易话多。',
  '左辅': '主贵人，但易依赖。',
  '右弼': '主贵人，但易依赖。',
  '天池': '主智慧，但易多虑。',
  '天才': '主才华，但易自负。',
  '天寿': '主长寿，但易懒散。',
  '天虚': '主空虚，但易迷茫。',
  '天哭': '主悲伤，但易抑郁。',
  '天玄': '主神秘，但易迷信。',
  '天明': '主光明，但易刚愎自用。',
  '天福': '主福气，但易懒惰。',
  '天禄': '主财运，但易贪婪。',
  '天财': '主财运，但易贪婪。',
  '天伤': '主伤害，但易自伤。',
  '天使': '主善良，但易软弱。',
  '天刑': '主刑罚，但易自罚。',
  '天医': '主健康，但易依赖。',
  '天灾': '主灾难，但易自毁。'
}

// 宫位解读
const palaceInterpretation = {
  '命宫': '命宫代表一个人的性格、气质、命运等基本特征。',
  '兄弟': '兄弟宫代表与兄弟姐妹的关系、兄弟姐妹的性格特征等。',
  '夫妻': '夫妻宫代表婚姻、配偶的性格特征、婚姻生活等。',
  '子女': '子女宫代表子女的性格特征、子女的教育、子女的未来等。',
  '财帛': '财帛宫代表财运、理财能力、财富积累等。',
  '疾厄': '疾厄宫代表身体健康、疾病、意外等。',
  '迁移': '迁移宫代表外出、旅行、搬迁、移民等。',
  '仆役': '仆役宫代表下属、同事、朋友等。',
  '官禄': '官禄宫代表事业、工作、职位、权力等。',
  '田宅': '田宅宫代表房产、土地、家居环境等。',
  '福德': '福德宫代表福气、精神生活、内心世界等。',
  '父母': '父母宫代表父母、长辈、家庭背景等。'
}

// 紫微斗数命理解读服务
class InterpretationService {
  constructor() {
    this.wuxingTraits = {
      JIN: {
        personality: '果断、刚毅、重义气',
        career: '金融、法律、管理',
        health: '注意呼吸系统，保持心情舒畅',
        direction: '西方',
        color: '白色',
        season: '秋',
        organs: ['肺', '大肠'],
        taste: '辛',
        emotion: '悲'
      },
      MU: {
        personality: '仁慈、温和、有创造力',
        career: '教育、艺术、设计',
        health: '保护肝脏，保持情绪稳定',
        direction: '东方',
        color: '青色',
        season: '春',
        organs: ['肝', '胆'],
        taste: '酸',
        emotion: '怒'
      },
      SHUI: {
        personality: '智慧、灵活、善于变通',
        career: '科技、传媒、咨询',
        health: '注意肾脏，保持充足睡眠',
        direction: '北方',
        color: '黑色',
        season: '冬',
        organs: ['肾', '膀胱'],
        taste: '咸',
        emotion: '恐'
      },
      HUO: {
        personality: '热情、开朗、充满活力',
        career: '销售、服务、娱乐',
        health: '保护心脏，避免过度兴奋',
        direction: '南方',
        color: '红色',
        season: '夏',
        organs: ['心', '小肠'],
        taste: '苦',
        emotion: '喜'
      },
      TU: {
        personality: '稳重、踏实、重信用',
        career: '建筑、农业、房地产',
        health: '注意脾胃，保持规律饮食',
        direction: '中央',
        color: '黄色',
        season: '长夏',
        organs: ['脾', '胃'],
        taste: '甘',
        emotion: '思'
      }
    }
  }

  // 获取主星落宫解读
  getMainStarInterpretation(star, palace) {
    return interpretationData[star]?.[palace] || `${star}星入${palace}，需要进一步分析。`
  }

  // 获取辅星解读
  getMinorStarInterpretation(star) {
    return minorStarInterpretation[star] || `${star}星，需要进一步分析。`
  }

  // 获取宫位解读
  getPalaceInterpretation(palace) {
    return palaceInterpretation[palace] || `${palace}，需要进一步分析。`
  }

  // 生成综合命理解读
  generateComprehensiveInterpretation(palaces) {
    const interpretation = []
    
    // 1. 命宫解读
    const mingGong = palaces.find(p => p.isMingGong)
    interpretation.push({
      title: '命宫解读',
      content: this.interpretMingGong(mingGong)
    })
    
    // 2. 身宫解读
    const shenGong = palaces.find(p => p.isShenGong)
    interpretation.push({
      title: '身宫解读',
      content: this.interpretShenGong(shenGong)
    })
    
    // 3. 事业解读
    const careerPalace = palaces.find(p => p.name === '官禄')
    interpretation.push({
      title: '事业解读',
      content: this.interpretCareer(careerPalace)
    })
    
    // 4. 财运解读
    const wealthPalace = palaces.find(p => p.name === '财帛')
    interpretation.push({
      title: '财运解读',
      content: this.interpretWealth(wealthPalace)
    })
    
    // 5. 感情解读
    const relationshipPalace = palaces.find(p => p.name === '夫妻')
    interpretation.push({
      title: '感情解读',
      content: this.interpretRelationship(relationshipPalace)
    })
    
    // 6. 健康解读
    const healthPalace = palaces.find(p => p.name === '疾厄')
    interpretation.push({
      title: '健康解读',
      content: this.interpretHealth(healthPalace)
    })
    
    // 7. 十四主星组合解读
    const starCombinations = this.interpretStarCombinations(palaces)
    interpretation.push({
      title: '十四主星组合解读',
      content: starCombinations
    })
    
    // 8. 大限流年解读
    const fortune = this.interpretFortune(palaces)
    interpretation.push({
      title: '大限流年解读',
      content: fortune
    })
    
    return interpretation
  }

  // 解读命宫
  interpretMingGong(palace) {
    if (!palace) return '无法解读命宫'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '命宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主吉利。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需注意。'
    }
    
    return interpretation
  }

  // 解读身宫
  interpretShenGong(palace) {
    if (!palace) return '无法解读身宫'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '身宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主吉利。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需注意。'
    }
    
    return interpretation
  }

  // 解读事业
  interpretCareer(palace) {
    if (!palace) return '无法解读事业'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '官禄宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主事业顺遂。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需谨慎。'
    }
    
    return interpretation
  }

  // 解读财运
  interpretWealth(palace) {
    if (!palace) return '无法解读财运'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '财帛宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主财运亨通。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需谨慎理财。'
    }
    
    return interpretation
  }

  // 解读感情
  interpretRelationship(palace) {
    if (!palace) return '无法解读感情'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '夫妻宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主感情美满。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需注意感情。'
    }
    
    return interpretation
  }

  // 解读健康
  interpretHealth(palace) {
    if (!palace) return '无法解读健康'
    
    const mainStars = palace.stars.filter(s => s.type === '主星')
    const luckyStars = palace.stars.filter(s => s.nature === '吉')
    const evilStars = palace.stars.filter(s => s.nature === '凶')
    
    let interpretation = '疾厄宫主星：' + mainStars.map(s => s.name).join('、') + '。'
    
    if (luckyStars.length > 0) {
      interpretation += '吉星：' + luckyStars.map(s => s.name).join('、') + '，主身体健康。'
    }
    
    if (evilStars.length > 0) {
      interpretation += '煞星：' + evilStars.map(s => s.name).join('、') + '，需注意保养。'
    }
    
    return interpretation
  }

  // 解读十四主星组合
  interpretStarCombinations(palaces) {
    const combinations = []
    
    // 紫微星系组合
    const ziweiStars = this.findStarCombination(palaces, ['紫微', '天机', '太阳'])
    if (ziweiStars.length > 0) {
      combinations.push({
        type: '紫微星系',
        stars: ziweiStars,
        meaning: '主贵气，性格刚毅，有领导才能。适合从事管理、领导工作，但需注意避免刚愎自用。'
      })
    }
    
    // 武曲星系组合
    const wuquStars = this.findStarCombination(palaces, ['武曲', '天同', '廉贞'])
    if (wuquStars.length > 0) {
      combinations.push({
        type: '武曲星系',
        stars: wuquStars,
        meaning: '主财运，性格刚毅，善于理财。适合从事金融、财务工作，但需注意避免过于固执。'
      })
    }
    
    // 天府星系组合
    const tianfuStars = this.findStarCombination(palaces, ['天府', '太阴', '贪狼'])
    if (tianfuStars.length > 0) {
      combinations.push({
        type: '天府星系',
        stars: tianfuStars,
        meaning: '主富贵，性格温和，善于交际。适合从事商业、贸易工作，但需注意避免过于贪心。'
      })
    }
    
    // 天相星系组合
    const tianxiangStars = this.findStarCombination(palaces, ['天相', '天梁', '七杀'])
    if (tianxiangStars.length > 0) {
      combinations.push({
        type: '天相星系',
        stars: tianxiangStars,
        meaning: '主官禄，性格正直，善于管理。适合从事公职、管理工作，但需注意避免过于刚直。'
      })
    }
    
    return combinations.map(c => `${c.type}：${c.stars.join('、')}，${c.meaning}`).join('\n')
  }

  // 查找星耀组合
  findStarCombination(palaces, starNames) {
    const stars = []
    palaces.forEach(palace => {
      palace.stars.forEach(star => {
        if (starNames.includes(star.name)) {
          stars.push(star.name)
        }
      })
    })
    return stars
  }

  // 解读大限流年
  interpretFortune(palaces) {
    const fortune = []
    
    // 计算大限
    const mingGong = palaces.find(p => p.isMingGong)
    if (mingGong) {
      const mainStars = mingGong.stars.filter(s => s.type === '主星')
      const luckyStars = mingGong.stars.filter(s => s.nature === '吉')
      const evilStars = mingGong.stars.filter(s => s.nature === '凶')
      
      fortune.push(`命宫主星：${mainStars.map(s => s.name).join('、')}，`)
      if (luckyStars.length > 0) {
        fortune.push(`吉星：${luckyStars.map(s => s.name).join('、')}，主吉利。`)
      }
      if (evilStars.length > 0) {
        fortune.push(`煞星：${evilStars.map(s => s.name).join('、')}，需注意。`)
      }
    }
    
    // 计算流年
    const currentYear = new Date().getFullYear()
    for (let i = 0; i < 10; i++) {
      const year = currentYear + i
      const palace = palaces[(year - currentYear) % 12]
      if (palace) {
        fortune.push(`\n${year}年：${palace.name}宫，主星：${palace.stars.filter(s => s.type === '主星').map(s => s.name).join('、')}。`)
      }
    }
    
    return fortune.join('')
  }

  generateWuxingInterpretation(result) {
    const { wuxing, strength, bazi } = result
    const traits = this.wuxingTraits[wuxing]

    return {
      general: `您的主要五行属性为${wuxing}，具有${traits.personality}的性格特点。`,
      personality: `性格特点：${traits.personality}`,
      career: `适合的职业：${traits.career}`,
      health: `养生建议：${traits.health}`,
      direction: `吉位：${traits.direction}`,
      color: `幸运色：${traits.color}`,
      season: `旺相季节：${traits.season}`,
      organs: `相关脏腑：${traits.organs.join('、')}`,
      taste: `适合口味：${traits.taste}`,
      emotion: `主要情绪：${traits.emotion}`
    }
  }
}

module.exports = new InterpretationService() 