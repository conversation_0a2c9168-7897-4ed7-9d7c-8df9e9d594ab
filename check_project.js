const fs = require('fs');
const path = require('path');

// 检查文件是否存在
function checkFile(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// 需要检查的关键文件
const criticalFiles = [
  'app.json',
  'app.js',
  'app.wxss',
  'custom-tab-bar/index.js',
  'custom-tab-bar/index.json',
  'custom-tab-bar/index.wxml',
  'custom-tab-bar/index.wxss',
  'subpages/admin/logs/logs.wxml',
  'subpages/admin/logs/logs.js',
  'subpages/admin/logs/logs.json',
  'subpages/admin/logs/logs.wxss'
];

console.log('=== 项目文件检查 ===\n');

let allFilesExist = true;

criticalFiles.forEach(file => {
  const exists = checkFile(file);
  const status = exists ? '✓' : '✗';
  console.log(`${status} ${file}`);
  if (!exists) allFilesExist = false;
});

console.log('\n=== 检查结果 ===');
if (allFilesExist) {
  console.log('✓ 所有关键文件都已存在');
} else {
  console.log('✗ 存在缺失文件');
}

// 特别检查logs页面
const logsFiles = [
  'subpages/admin/logs/logs.wxml',
  'subpages/admin/logs/logs.js',
  'subpages/admin/logs/logs.json',
  'subpages/admin/logs/logs.wxss'
];

console.log('\n=== Logs页面文件 ===');
logsFiles.forEach(file => {
  const exists = checkFile(file);
  const status = exists ? '✓' : '✗';
  const size = exists ? fs.statSync(file).size : 0;
  console.log(`${status} ${file} (${size} bytes)`);
});

console.log('\n检查完成！'); 