// logs.js
const app = getApp()

Page({
  data: {
    logs: []
  },
  onLoad() {
    try {
      const logs = wx.getStorageSync('logs') || []
      this.setData({
        logs: logs.map(log => {
          return {
            date: app.globalData.util.formatTime(new Date(log)),
            timeStamp: log
          }
        })
      })
      console.log('日志加载成功')
    } catch (error) {
      console.error('日志加载失败:', error)
      this.setData({
        logs: []
      })
    }
  }
})
