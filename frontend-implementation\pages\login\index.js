// 登录页面
import { wxLogin } from '../../api/auth'
import { setStorageSync, getStorageSync, showToast, showLoading, hideLoading } from '../../utils/wx'

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    loading: false
  },

  onLoad() {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 检查是否已登录
    this.checkLoginStatus()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = getStorageSync('token')
    const userInfo = getStorageSync('userInfo')
    
    if (token && userInfo) {
      // 已登录，跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  /**
   * 微信登录
   */
  async wxLogin() {
    try {
      this.setData({ loading: true })
      showLoading({ title: '登录中...' })

      // 获取登录code
      const loginRes = await wx.login()
      if (!loginRes.code) {
        throw new Error('获取登录code失败')
      }

      // 调用登录接口
      const result = await wxLogin(loginRes.code, this.data.userInfo)
      
      if (result.status === 'success') {
        // 保存登录信息
        setStorageSync('token', result.data.token)
        setStorageSync('refreshToken', result.data.refresh_token)
        setStorageSync('userInfo', result.data.user)

        showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
        // 获取用户信息后自动登录
        this.wxLogin()
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        showToast({
          title: '需要授权才能使用',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 快速登录（不获取用户信息）
   */
  quickLogin() {
    this.wxLogin()
  },

  /**
   * 跳转到隐私政策
   */
  goToPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    })
  },

  /**
   * 跳转到用户协议
   */
  goToAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/index'
    })
  }
})
