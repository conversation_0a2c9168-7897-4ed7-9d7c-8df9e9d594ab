<!--每日运势页面-->
<view class="page-container">
  <!-- 头部日期选择 -->
  <view class="date-header">
    <view class="date-selector">
      <view class="date-nav" bindtap="prevDate">
        <text class="nav-icon">‹</text>
      </view>
      <picker 
        mode="date"
        value="{{selectedDate}}"
        bindchange="onDateChange"
      >
        <view class="current-date">
          <text class="date-text">{{formattedDate}}</text>
          <text class="lunar-date">{{lunarDate}}</text>
        </view>
      </picker>
      <view class="date-nav" bindtap="nextDate">
        <text class="nav-icon">›</text>
      </view>
    </view>
    <view class="today-btn" bindtap="goToToday" wx:if="{{!isToday}}">今天</view>
  </view>

  <!-- 运势概览 -->
  <view class="fortune-overview">
    <view class="overview-header">
      <view class="fortune-title">今日运势</view>
      <view class="overall-score">
        <text class="score-number">{{dailyFortune.overallScore}}</text>
        <text class="score-unit">分</text>
      </view>
    </view>
    <view class="fortune-summary">
      <text class="summary-text">{{dailyFortune.summary}}</text>
    </view>
    <view class="fortune-keyword">
      <text class="keyword-label">今日关键词：</text>
      <text class="keyword-text">{{dailyFortune.keyword}}</text>
    </view>
  </view>

  <!-- 运势详情 -->
  <view class="fortune-details">
    <!-- 四大运势 -->
    <view class="main-fortunes">
      <view class="fortune-item" wx:for="{{dailyFortune.mainFortunes}}" wx:key="type">
        <view class="fortune-header">
          <view class="fortune-icon">{{item.icon}}</view>
          <view class="fortune-info">
            <text class="fortune-name">{{item.name}}</text>
            <view class="fortune-rating">
              <view class="stars">
                <text 
                  class="star {{index < item.rating ? 'active' : ''}}"
                  wx:for="{{5}}" 
                  wx:key="index"
                  wx:for-index="index"
                >★</text>
              </view>
              <text class="rating-text">{{item.ratingText}}</text>
            </view>
          </view>
          <view class="fortune-score">{{item.score}}分</view>
        </view>
        <view class="fortune-content">
          <text class="fortune-desc">{{item.description}}</text>
        </view>
        <view class="fortune-suggestion" wx:if="{{item.suggestion}}">
          <text class="suggestion-label">建议：</text>
          <text class="suggestion-text">{{item.suggestion}}</text>
        </view>
      </view>
    </view>

    <!-- 幸运信息 -->
    <view class="lucky-info">
      <view class="section-title">今日幸运</view>
      <view class="lucky-grid">
        <view class="lucky-item">
          <view class="lucky-label">幸运数字</view>
          <view class="lucky-value number">{{dailyFortune.luckyNumber}}</view>
        </view>
        <view class="lucky-item">
          <view class="lucky-label">幸运颜色</view>
          <view class="lucky-value color">
            <view class="color-dot" style="background: {{dailyFortune.luckyColor.hex}}"></view>
            <text>{{dailyFortune.luckyColor.name}}</text>
          </view>
        </view>
        <view class="lucky-item">
          <view class="lucky-label">幸运方位</view>
          <view class="lucky-value direction">{{dailyFortune.luckyDirection}}</view>
        </view>
        <view class="lucky-item">
          <view class="lucky-label">幸运时间</view>
          <view class="lucky-value time">{{dailyFortune.luckyTime}}</view>
        </view>
      </view>
    </view>

    <!-- 时辰运势 -->
    <view class="hourly-fortune">
      <view class="section-title">
        <text>时辰运势</text>
        <text class="section-desc">点击查看详情</text>
      </view>
      <scroll-view class="hour-scroll" scroll-x>
        <view class="hour-list">
          <view 
            class="hour-item {{item.isNow ? 'current' : ''}} {{item.level}}"
            wx:for="{{dailyFortune.hourlyFortunes}}" 
            wx:key="hour"
            bindtap="showHourDetail"
            data-hour="{{item}}"
          >
            <view class="hour-time">{{item.time}}</view>
            <view class="hour-name">{{item.name}}</view>
            <view class="hour-level">{{item.levelText}}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 今日宜忌 -->
    <view class="daily-taboos">
      <view class="section-title">今日宜忌</view>
      <view class="taboo-content">
        <view class="taboo-section good">
          <view class="taboo-header">
            <view class="taboo-icon">✓</view>
            <text class="taboo-title">宜</text>
          </view>
          <view class="taboo-list">
            <text 
              class="taboo-item"
              wx:for="{{dailyFortune.suitable}}" 
              wx:key="*this"
            >{{item}}</text>
          </view>
        </view>
        <view class="taboo-section bad">
          <view class="taboo-header">
            <view class="taboo-icon">✗</view>
            <text class="taboo-title">忌</text>
          </view>
          <view class="taboo-list">
            <text 
              class="taboo-item"
              wx:for="{{dailyFortune.forbidden}}" 
              wx:key="*this"
            >{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 运势趋势 -->
    <view class="fortune-trend">
      <view class="section-title">
        <text>本周运势趋势</text>
        <text class="section-more" bindtap="viewWeeklyFortune">查看详情</text>
      </view>
      <view class="trend-chart">
        <canvas 
          canvas-id="fortuneChart"
          class="chart-canvas"
          bindtouchstart="onChartTouch"
        ></canvas>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn share" bindtap="shareFortune">
      <view class="btn-icon">📤</view>
      <text>分享运势</text>
    </button>
    <button class="action-btn reminder" bindtap="setReminder">
      <view class="btn-icon">⏰</view>
      <text>设置提醒</text>
    </button>
    <button class="action-btn history" bindtap="viewHistory">
      <view class="btn-icon">📊</view>
      <text>历史运势</text>
    </button>
  </view>

  <!-- 时辰详情弹窗 -->
  <view class="hour-detail-modal" wx:if="{{showHourDetail}}">
    <view class="modal-mask" bindtap="hideHourDetail"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>{{selectedHour.name}}时</text>
        <text class="hour-time-range">{{selectedHour.timeRange}}</text>
        <view class="close-btn" bindtap="hideHourDetail">×</view>
      </view>
      <view class="modal-body">
        <view class="hour-fortune-level {{selectedHour.level}}">
          <text class="level-text">{{selectedHour.levelText}}</text>
        </view>
        <view class="hour-description">
          <text>{{selectedHour.description}}</text>
        </view>
        <view class="hour-suggestions" wx:if="{{selectedHour.suggestions}}">
          <view class="suggestions-title">建议：</view>
          <view class="suggestions-list">
            <text 
              class="suggestion-item"
              wx:for="{{selectedHour.suggestions}}" 
              wx:key="*this"
            >• {{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在获取运势...</text>
    </view>
  </view>
</view> 