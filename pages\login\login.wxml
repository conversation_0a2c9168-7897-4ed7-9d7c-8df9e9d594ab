<!--登录页面-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="background-decoration">
    <view class="decoration-circle circle1"></view>
    <view class="decoration-circle circle2"></view>
    <view class="decoration-circle circle3"></view>
  </view>

  <!-- 顶部Logo和标题 -->
  <view class="header-section">
    <image class="app-logo" src="/assets/images/logo.png" mode="aspectFit" />
    <view class="app-title">恒琦易道</view>
    <view class="app-subtitle">专业的命理测算平台</view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 微信一键登录 -->
    <view class="quick-login-section">
      <view class="section-title">
        <text>快速登录</text>
        <text class="section-desc">使用微信账号快速登录</text>
      </view>
      
      <!-- 微信授权登录按钮 -->
      <button 
        class="wx-login-btn"
        open-type="getUserProfile"
        bindgetuserprofile="onGetUserProfile"
        wx:if="{{!userInfo.nickName}}"
      >
        <view class="btn-content">
          <image class="wx-icon" src="/assets/icons/wechat.png" mode="aspectFit" />
          <text class="btn-text">微信授权登录</text>
        </view>
      </button>

      <!-- 用户信息显示 -->
      <view class="user-info-card" wx:if="{{userInfo.nickName}}">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
        <view class="user-details">
          <text class="user-nickname">{{userInfo.nickName}}</text>
          <text class="user-desc">微信用户</text>
        </view>
        <view class="auth-status success">已授权</view>
      </view>

      <!-- 手机号授权 -->
      <view class="phone-auth-section" wx:if="{{userInfo.nickName}}">
        <view class="auth-item">
          <view class="auth-info">
            <view class="auth-icon">📱</view>
            <view class="auth-content">
              <text class="auth-title">手机号授权</text>
              <text class="auth-desc">{{phoneNumber || '用于接收重要通知'}}</text>
            </view>
          </view>
          <button 
            class="auth-btn {{phoneNumber ? 'authorized' : ''}}"
            open-type="getPhoneNumber"
            bindgetphonenumber="onGetPhoneNumber"
            wx:if="{{!phoneNumber}}"
          >
            获取手机号
          </button>
          <view class="auth-status success" wx:else>已授权</view>
        </view>
      </view>
    </view>

    <!-- 服务条款 -->
    <view class="terms-section" wx:if="{{userInfo.nickName}}">
      <view class="terms-content">
        <checkbox-group bindchange="onTermsChange">
          <label class="terms-checkbox">
            <checkbox value="agreed" checked="{{termsAgreed}}" />
            <text class="terms-text">
              我已阅读并同意
              <text class="link" bindtap="viewUserAgreement">《用户协议》</text>
              和
              <text class="link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
            </text>
          </label>
        </checkbox-group>
      </view>
    </view>

    <!-- 完成登录按钮 -->
    <button 
      class="complete-login-btn"
      bindtap="completeLogin"
      disabled="{{!canCompleteLogin}}"
      wx:if="{{userInfo.nickName}}"
    >
      {{loginButtonText}}
    </button>

    <!-- 登录状态提示 -->
    <view class="login-tips">
      <view class="tip-item">
        <view class="tip-icon">🔒</view>
        <text class="tip-text">我们将保护您的隐私信息</text>
      </view>
      <view class="tip-item">
        <view class="tip-icon">⚡</view>
        <text class="tip-text">登录后享受完整功能</text>
      </view>
      <view class="tip-item">
        <view class="tip-icon">🎁</view>
        <text class="tip-text">新用户免费获得积分奖励</text>
      </view>
    </view>
  </view>

  <!-- 其他登录方式 -->
  <view class="other-login-section">
    <view class="section-divider">
      <view class="divider-line"></view>
      <text class="divider-text">其他方式</text>
      <view class="divider-line"></view>
    </view>
    
    <view class="other-login-methods">
      <view class="login-method" bindtap="visitorLogin">
        <view class="method-icon">👤</view>
        <text class="method-text">游客体验</text>
      </view>
      <view class="login-method" bindtap="viewDemo">
        <view class="method-icon">👁️</view>
        <text class="method-text">查看演示</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>

  <!-- 登录成功提示 -->
  <view class="success-modal" wx:if="{{showSuccessModal}}">
    <view class="modal-mask"></view>
    <view class="modal-content">
      <view class="success-animation">
        <view class="success-circle">
          <view class="success-checkmark">✓</view>
        </view>
      </view>
      <view class="success-title">登录成功</view>
      <view class="success-desc">
        欢迎来到恒琦易道，{{userInfo.nickName}}
      </view>
      <view class="success-rewards" wx:if="{{loginRewards.length > 0}}">
        <text class="rewards-title">登录奖励</text>
        <view class="rewards-list">
          <view class="reward-item" wx:for="{{loginRewards}}" wx:key="type">
            <view class="reward-icon">{{item.icon}}</view>
            <text class="reward-text">{{item.text}}</text>
          </view>
        </view>
      </view>
      <button class="enter-app-btn" bindtap="enterApp">进入应用</button>
    </view>
  </view>

  <!-- 权限说明弹窗 -->
  <view class="permission-modal" wx:if="{{showPermissionModal}}">
    <view class="modal-mask" bindtap="hidePermissionModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>权限说明</text>
        <view class="close-btn" bindtap="hidePermissionModal">×</view>
      </view>
      <view class="modal-body">
        <view class="permission-list">
          <view class="permission-item">
            <view class="permission-icon">👤</view>
            <view class="permission-info">
              <text class="permission-title">微信用户信息</text>
              <text class="permission-desc">用于个性化服务和身份识别</text>
            </view>
          </view>
          <view class="permission-item">
            <view class="permission-icon">📱</view>
            <view class="permission-info">
              <text class="permission-title">手机号码</text>
              <text class="permission-desc">用于账号安全和重要通知</text>
            </view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="understand-btn" bindtap="hidePermissionModal">我知道了</button>
      </view>
    </view>
  </view>
</view> 