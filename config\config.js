// config/config.js
// 应用配置文件

const config = {
  // API基础配置
  baseUrl: 'https://your-api-domain.com/api',
  timeout: 10000,
  
  // 功能开关
  features: {
    enableTypingEffect: true,
    maxMessageHistory: 50,
    enableNotification: true,
    enableShare: true
  },
  
  // 页面配置
  pages: {
    maxRecentUsed: 5,
    pageSize: 20
  },
  
  // 积分配置
  points: {
    signInReward: 10,
    shareReward: 5,
    inviteReward: 100
  },
  
  // 微信相关配置
  wx: {
    appId: 'wx59e55b1fe57dabe2'
  },
  
  // 环境配置
  env: {
    debug: true,
    version: '1.0.0'
  }
}

module.exports = config 