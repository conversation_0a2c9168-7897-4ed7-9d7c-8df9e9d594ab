/* pages/customer-service/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 客服信息 */
.service-header {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.service-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
}

.service-status {
  font-size: 24rpx;
  color: #07c160;
  display: block;
  margin-top: 4rpx;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 30rpx;
}

/* 常见问题 */
.faq-section {
  margin-bottom: 40rpx;
}

.faq-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
  display: block;
}

.faq-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.faq-item {
  background-color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #333;
}

/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.message-item.service {
  flex-direction: row;
}

.message-item.user {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin: 0 20rpx;
}

.message-content {
  max-width: 60%;
}

.message-item.service .message-content {
  margin-right: 100rpx;
}

.message-item.user .message-content {
  margin-left: 100rpx;
  align-items: flex-end;
}

.name {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.content {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.message-item.user .content {
  background-color: #07c160;
  color: #fff;
}

.content-image {
  max-width: 400rpx;
  border-radius: 12rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 正在输入提示 */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 20rpx;
  gap: 8rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #999;
  border-radius: 50%;
  animation: typing 1s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10rpx);
  }
}

/* 输入区域 */
.input-section {
  background-color: #fff;
  border-top: 2rpx solid #f0f0f0;
  padding: 20rpx;
}

/* 工具栏 */
.toolbar {
  display: flex;
  gap: 30rpx;
  padding: 10rpx 0;
}

.tool-item {
  padding: 10rpx;
}

.tool-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 输入框 */
.input-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.input {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.send-btn {
  padding: 16rpx 40rpx;
  border-radius: 36rpx;
  background-color: #e0e0e0;
  color: #fff;
  font-size: 28rpx;
}

.send-btn.active {
  background-color: #07c160;
}

/* 表情面板 */
.emoji-panel {
  height: 400rpx;
  background-color: #fff;
  padding: 20rpx;
}

/* 更多面板 */
.more-panel {
  height: 400rpx;
  background-color: #fff;
  padding: 40rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx;
}

.more-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.more-icon {
  width: 80rpx;
  height: 80rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
}

.more-item text {
  font-size: 24rpx;
  color: #666;
} 