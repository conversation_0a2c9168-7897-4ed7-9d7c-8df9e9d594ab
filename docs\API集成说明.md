# 企业微信小程序与ChatBot项目API集成说明

## 概述

本文档详细说明如何将"恒琦易道"企业微信小程序与 `@chatbot_project` 的AI服务集成，实现从云函数调用到HTTP API调用的迁移。

## 🏗️ 集成架构

```
┌─────────────────────┐    HTTP API    ┌─────────────────────┐
│   企业微信小程序     │ ────────────── │   ChatBot API       │
│   (miniprogram-1)   │                │   (FastAPI)         │
│                     │ ←────────────── │                     │
└─────────────────────┘    JSON 响应    └─────────────────────┘
```

## 🔧 主要改进内容

### 1. API服务扩展

#### 1.1 新增企业微信API模块
- **文件**: `/D:/chatbot_project/wxwork_api.py`
- **核心接口**:
  - `POST /api/wxwork/chat` - 企业微信聊天AI回复
  - `GET /api/wxwork/contacts` - 获取企业通讯录
  - `GET /api/wxwork/departments` - 获取部门列表
  - `GET /api/wxwork/user/{user_id}` - 获取用户详细信息
  - `GET /api/wxwork/user-profile/{user_id}` - 获取用户小程序资料
  - `POST /api/wxwork/send-message` - 发送企业微信消息

### 2. 小程序API调用改造

#### 2.1 配置管理
- **文件**: `miniprogram-1/config/api-config.js`
- **支持多环境配置** (development/testing/production)
- **统一API端点管理**
- **错误码标准化**

#### 2.2 工具类改造  
- **文件**: `miniprogram-1/utils/wxwork-util.js`
- ✅ 移除云函数依赖
- ✅ 添加HTTP请求封装
- ✅ 统一错误处理机制
- ✅ 支持多环境配置

## 🚀 快速开始

### 1. 启动API服务
```bash
cd /D:/chatbot_project
pip install -r requirements.txt
uvicorn FastAPI:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 配置小程序
```javascript
// miniprogram-1/config/api-config.js
const CURRENT_ENV = 'development'  // 设置环境
```

### 3. 验证集成
访问: http://localhost:8000/docs 查看API文档
在小程序中测试AI聊天功能

## 📊 功能映射表

| 功能 | 原云函数 | 新API端点 | 状态 |
|------|---------|-----------|------|
| AI聊天回复 | generateAIReply | POST /api/wxwork/chat | ✅ 已完成 |
| 获取通讯录 | getContactList | GET /api/wxwork/contacts | ✅ 已完成 |
| 获取部门 | getDepartmentList | GET /api/wxwork/departments | ✅ 已完成 |
| 用户详情 | getUserDetail | GET /api/wxwork/user/{id} | ✅ 已完成 |
| 用户资料 | getUserProfileInMiniapp | GET /api/wxwork/user-profile/{id} | ✅ 已完成 |
| 发送消息 | sendWxWorkMessage | POST /api/wxwork/send-message | ✅ 已完成 |

## 🔄 核心数据流程

### AI聊天流程
```
用户发送消息 → 获取用户信息 → 构建上下文 → 调用AI API → 返回个性化回复
```

### 用户切换流程  
```
用户切换事件 → 获取新用户信息 → 生成欢迎消息 → 重置对话上下文
```

## ⚠️ 错误处理

```javascript
try {
  const response = await wxworkUtil.generateAIReply(message, context)
} catch (error) {
  switch (error.code) {
    case 'NETWORK_ERROR':
      wxworkUtil.showError('网络连接失败')
      break
    case 'TIMEOUT':
      wxworkUtil.showError('请求超时')
      break
    default:
      wxworkUtil.showError('服务异常')
  }
}
```

## 🧪 测试清单

- [ ] API连通性测试
- [ ] AI聊天功能测试
- [ ] 用户信息获取测试
- [ ] 错误处理测试
- [ ] 网络异常测试
- [ ] 用户切换测试

## 📈 性能优化

- ✅ 请求超时控制
- ✅ 错误重试机制
- ✅ 响应缓存支持
- ✅ 调试日志管理

## 🔒 安全措施

- ✅ HTTPS支持
- ✅ 域名白名单
- ✅ 参数验证
- ✅ 错误信息脱敏

## 🚀 部署指南

### 生产环境配置
```javascript
// 更新API配置
production: {
  baseURL: 'https://api.yourcompany.com',
  timeout: 20000,
  debug: false
}
```

### 企业微信配置
```json
// app.json
"wxwork": {
  "allowedDomains": [
    "api.yourcompany.com"
  ]
}
```

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成API服务集成
- ✅ 实现企业微信聊天功能
- ✅ 添加多环境配置支持
- ✅ 统一错误处理机制

## 🎯 后续规划

- [ ] 支持语音消息
- [ ] 添加图片识别  
- [ ] 实现群聊助手
- [ ] 性能监控优化

## 📞 技术支持

如遇到问题，请检查：
1. API服务是否正常运行
2. 网络域名配置是否正确
3. 企业微信权限是否开启
4. 控制台错误日志

---

**集成完成！🎉** 

现在您可以在企业微信环境中享受智能化的AI聊天体验了。 