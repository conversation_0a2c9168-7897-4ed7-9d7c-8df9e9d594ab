// 紫微斗数计算工具类
const lunar = require('./lunar')

// 紫微斗数计算器
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
const PALACES = ['命宫', '兄弟', '夫妻', '子女', '财帛', '疾厄', '迁移', '仆役', '官禄', '田宅', '福德', '父母']

// 紫微星系
const ZIWEI_STARS = {
  '紫微': { type: '主星', element: '土', nature: '吉' },
  '天机': { type: '主星', element: '木', nature: '吉' },
  '太阳': { type: '主星', element: '火', nature: '吉' },
  '武曲': { type: '主星', element: '金', nature: '吉' },
  '天同': { type: '主星', element: '水', nature: '吉' },
  '廉贞': { type: '主星', element: '火', nature: '凶' },
  '天府': { type: '主星', element: '土', nature: '吉' },
  '太阴': { type: '主星', element: '水', nature: '吉' },
  '贪狼': { type: '主星', element: '木', nature: '凶' },
  '巨门': { type: '主星', element: '土', nature: '吉' },
  '天相': { type: '主星', element: '金', nature: '吉' },
  '天梁': { type: '主星', element: '木', nature: '吉' },
  '七杀': { type: '主星', element: '金', nature: '凶' },
  '破军': { type: '主星', element: '水', nature: '凶' }
}

// 辅佐星系
const ASSISTANT_STARS = {
  '左辅': { type: '辅佐', element: '土', nature: '吉' },
  '右弼': { type: '辅佐', element: '土', nature: '吉' },
  '文昌': { type: '辅佐', element: '木', nature: '吉' },
  '文曲': { type: '辅佐', element: '水', nature: '吉' },
  '禄存': { type: '辅佐', element: '土', nature: '吉' },
  '天马': { type: '辅佐', element: '火', nature: '吉' },
  '天魁': { type: '辅佐', element: '木', nature: '吉' },
  '天钺': { type: '辅佐', element: '金', nature: '吉' }
}

// 吉星系
const LUCKY_STARS = {
  '天德': { type: '吉星', element: '土', nature: '吉' },
  '月德': { type: '吉星', element: '水', nature: '吉' },
  '天喜': { type: '吉星', element: '火', nature: '吉' },
  '红鸾': { type: '吉星', element: '火', nature: '吉' },
  '龙池': { type: '吉星', element: '水', nature: '吉' },
  '凤阁': { type: '吉星', element: '木', nature: '吉' }
}

// 煞星系
const EVIL_STARS = {
  '火星': { type: '煞星', element: '火', nature: '凶' },
  '铃星': { type: '煞星', element: '金', nature: '凶' },
  '地空': { type: '煞星', element: '土', nature: '凶' },
  '地劫': { type: '煞星', element: '土', nature: '凶' },
  '天刑': { type: '煞星', element: '金', nature: '凶' },
  '天姚': { type: '煞星', element: '水', nature: '凶' }
}

// 紫微星落宫表
const ZIWEI_POSITION_TABLE = {
  '寅': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '卯': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 },
  '辰': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '巳': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 },
  '午': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '未': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 },
  '申': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '酉': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 },
  '戌': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '亥': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 },
  '子': { '初一': 1, '初二': 2, '初三': 3, '初四': 4, '初五': 5, '初六': 6, '初七': 7, '初八': 8, '初九': 9, '初十': 10,
    '十一': 11, '十二': 12, '十三': 1, '十四': 2, '十五': 3, '十六': 4, '十七': 5, '十八': 6, '十九': 7, '二十': 8,
    '廿一': 9, '廿二': 10, '廿三': 11, '廿四': 12, '廿五': 1, '廿六': 2, '廿七': 3, '廿八': 4, '廿九': 5, '三十': 6 },
  '丑': { '初一': 7, '初二': 8, '初三': 9, '初四': 10, '初五': 11, '初六': 12, '初七': 1, '初八': 2, '初九': 3, '初十': 4,
    '十一': 5, '十二': 6, '十三': 7, '十四': 8, '十五': 9, '十六': 10, '十七': 11, '十八': 12, '十九': 1, '二十': 2,
    '廿一': 3, '廿二': 4, '廿三': 5, '廿四': 6, '廿五': 7, '廿六': 8, '廿七': 9, '廿八': 10, '廿九': 11, '三十': 12 }
}

// 命宫计算表
const MING_GONG_TABLE = {
  '寅': { '子': 1, '丑': 2, '寅': 3, '卯': 4, '辰': 5, '巳': 6, '午': 7, '未': 8, '申': 9, '酉': 10, '戌': 11, '亥': 12 },
  '卯': { '子': 12, '丑': 1, '寅': 2, '卯': 3, '辰': 4, '巳': 5, '午': 6, '未': 7, '申': 8, '酉': 9, '戌': 10, '亥': 11 },
  '辰': { '子': 11, '丑': 12, '寅': 1, '卯': 2, '辰': 3, '巳': 4, '午': 5, '未': 6, '申': 7, '酉': 8, '戌': 9, '亥': 10 },
  '巳': { '子': 10, '丑': 11, '寅': 12, '卯': 1, '辰': 2, '巳': 3, '午': 4, '未': 5, '申': 6, '酉': 7, '戌': 8, '亥': 9 },
  '午': { '子': 9, '丑': 10, '寅': 11, '卯': 12, '辰': 1, '巳': 2, '午': 3, '未': 4, '申': 5, '酉': 6, '戌': 7, '亥': 8 },
  '未': { '子': 8, '丑': 9, '寅': 10, '卯': 11, '辰': 12, '巳': 1, '午': 2, '未': 3, '申': 4, '酉': 5, '戌': 6, '亥': 7 },
  '申': { '子': 7, '丑': 8, '寅': 9, '卯': 10, '辰': 11, '巳': 12, '午': 1, '未': 2, '申': 3, '酉': 4, '戌': 5, '亥': 6 },
  '酉': { '子': 6, '丑': 7, '寅': 8, '卯': 9, '辰': 10, '巳': 11, '午': 12, '未': 1, '申': 2, '酉': 3, '戌': 4, '亥': 5 },
  '戌': { '子': 5, '丑': 6, '寅': 7, '卯': 8, '辰': 9, '巳': 10, '午': 11, '未': 12, '申': 1, '酉': 2, '戌': 3, '亥': 4 },
  '亥': { '子': 4, '丑': 5, '寅': 6, '卯': 7, '辰': 8, '巳': 9, '午': 10, '未': 11, '申': 12, '酉': 1, '戌': 2, '亥': 3 },
  '子': { '子': 3, '丑': 4, '寅': 5, '卯': 6, '辰': 7, '巳': 8, '午': 9, '未': 10, '申': 11, '酉': 12, '戌': 1, '亥': 2 },
  '丑': { '子': 2, '丑': 3, '寅': 4, '卯': 5, '辰': 6, '巳': 7, '午': 8, '未': 9, '申': 10, '酉': 11, '戌': 12, '亥': 1 }
}

// 身宫计算表
const SHEN_GONG_TABLE = {
  '寅': { '子': 12, '丑': 11, '寅': 10, '卯': 9, '辰': 8, '巳': 7, '午': 6, '未': 5, '申': 4, '酉': 3, '戌': 2, '亥': 1 },
  '卯': { '子': 1, '丑': 12, '寅': 11, '卯': 10, '辰': 9, '巳': 8, '午': 7, '未': 6, '申': 5, '酉': 4, '戌': 3, '亥': 2 },
  '辰': { '子': 2, '丑': 1, '寅': 12, '卯': 11, '辰': 10, '巳': 9, '午': 8, '未': 7, '申': 6, '酉': 5, '戌': 4, '亥': 3 },
  '巳': { '子': 3, '丑': 2, '寅': 1, '卯': 12, '辰': 11, '巳': 10, '午': 9, '未': 8, '申': 7, '酉': 6, '戌': 5, '亥': 4 },
  '午': { '子': 4, '丑': 3, '寅': 2, '卯': 1, '辰': 12, '巳': 11, '午': 10, '未': 9, '申': 8, '酉': 7, '戌': 6, '亥': 5 },
  '未': { '子': 5, '丑': 4, '寅': 3, '卯': 2, '辰': 1, '巳': 12, '午': 11, '未': 10, '申': 9, '酉': 8, '戌': 7, '亥': 6 },
  '申': { '子': 6, '丑': 5, '寅': 4, '卯': 3, '辰': 2, '巳': 1, '午': 12, '未': 11, '申': 10, '酉': 9, '戌': 8, '亥': 7 },
  '酉': { '子': 7, '丑': 6, '寅': 5, '卯': 4, '辰': 3, '巳': 2, '午': 1, '未': 12, '申': 11, '酉': 10, '戌': 9, '亥': 8 },
  '戌': { '子': 8, '丑': 7, '寅': 6, '卯': 5, '辰': 4, '巳': 3, '午': 2, '未': 1, '申': 12, '酉': 11, '戌': 10, '亥': 9 },
  '亥': { '子': 9, '丑': 8, '寅': 7, '卯': 6, '辰': 5, '巳': 4, '午': 3, '未': 2, '申': 1, '酉': 12, '戌': 11, '亥': 10 },
  '子': { '子': 10, '丑': 9, '寅': 8, '卯': 7, '辰': 6, '巳': 5, '午': 4, '未': 3, '申': 2, '酉': 1, '戌': 12, '亥': 11 },
  '丑': { '子': 11, '丑': 10, '寅': 9, '卯': 8, '辰': 7, '巳': 6, '午': 5, '未': 4, '申': 3, '酉': 2, '戌': 1, '亥': 12 }
}

// 命理解读数据
const INTERPRETATION_DATA = {
  // 紫微星落宫解读
  '紫微': {
    '命宫': '紫微入命宫，主贵气，性格刚毅，有领导才能，但易刚愎自用。',
    '兄弟': '紫微入兄弟宫，主兄弟富贵，但易与兄弟争权夺利。',
    '夫妻': '紫微入夫妻宫，主配偶富贵，但易有婚姻波折。',
    '子女': '紫微入子女宫，主子女富贵，但易与子女有代沟。',
    '财帛': '紫微入财帛宫，主财运亨通，但易有破财之虞。',
    '疾厄': '紫微入疾厄宫，主身体康健，但易有头部疾病。',
    '迁移': '紫微入迁移宫，主外出顺利，但易有意外之虞。',
    '仆役': '紫微入仆役宫，主下属得力，但易与下属有矛盾。',
    '官禄': '紫微入官禄宫，主官运亨通，但易有官非之虞。',
    '田宅': '紫微入田宅宫，主房产丰厚，但易有房产纠纷。',
    '福德': '紫微入福德宫，主福气深厚，但易有精神压力。',
    '父母': '紫微入父母宫，主父母富贵，但易与父母有代沟。'
  },
  // 天机星落宫解读
  '天机': {
    '命宫': '天机入命宫，主聪明才智，思维敏捷，但易多疑。',
    '兄弟': '天机入兄弟宫，主兄弟聪明，但易与兄弟有分歧。',
    '夫妻': '天机入夫妻宫，主配偶聪明，但易有婚姻波折。',
    '子女': '天机入子女宫，主子女聪明，但易与子女有代沟。',
    '财帛': '天机入财帛宫，主财运多变，但易有投机之虞。',
    '疾厄': '天机入疾厄宫，主身体多变，但易有神经系统疾病。',
    '迁移': '天机入迁移宫，主外出多变，但易有意外之虞。',
    '仆役': '天机入仆役宫，主下属多变，但易与下属有矛盾。',
    '官禄': '天机入官禄宫，主官运多变，但易有官非之虞。',
    '田宅': '天机入田宅宫，主房产多变，但易有房产纠纷。',
    '福德': '天机入福德宫，主福气多变，但易有精神压力。',
    '父母': '天机入父母宫，主父母多变，但易与父母有代沟。'
  },
  // 太阳星落宫解读
  '太阳': {
    '命宫': '太阳入命宫，主光明磊落，性格开朗，但易刚愎自用。',
    '兄弟': '太阳入兄弟宫，主兄弟光明，但易与兄弟争权夺利。',
    '夫妻': '太阳入夫妻宫，主配偶光明，但易有婚姻波折。',
    '子女': '太阳入子女宫，主子女光明，但易与子女有代沟。',
    '财帛': '太阳入财帛宫，主财运光明，但易有破财之虞。',
    '疾厄': '太阳入疾厄宫，主身体光明，但易有心脏疾病。',
    '迁移': '太阳入迁移宫，主外出光明，但易有意外之虞。',
    '仆役': '太阳入仆役宫，主下属光明，但易与下属有矛盾。',
    '官禄': '太阳入官禄宫，主官运光明，但易有官非之虞。',
    '田宅': '太阳入田宅宫，主房产光明，但易有房产纠纷。',
    '福德': '太阳入福德宫，主福气光明，但易有精神压力。',
    '父母': '太阳入父母宫，主父母光明，但易与父母有代沟。'
  },
  // 武曲星落宫解读
  '武曲': {
    '命宫': '武曲入命宫，主财运亨通，性格刚毅，但易刚愎自用。',
    '兄弟': '武曲入兄弟宫，主兄弟财运，但易与兄弟争权夺利。',
    '夫妻': '武曲入夫妻宫，主配偶财运，但易有婚姻波折。',
    '子女': '武曲入子女宫，主子女财运，但易与子女有代沟。',
    '财帛': '武曲入财帛宫，主财运亨通，但易有破财之虞。',
    '疾厄': '武曲入疾厄宫，主身体康健，但易有骨骼疾病。',
    '迁移': '武曲入迁移宫，主外出顺利，但易有意外之虞。',
    '仆役': '武曲入仆役宫，主下属得力，但易与下属有矛盾。',
    '官禄': '武曲入官禄宫，主官运亨通，但易有官非之虞。',
    '田宅': '武曲入田宅宫，主房产丰厚，但易有房产纠纷。',
    '福德': '武曲入福德宫，主福气深厚，但易有精神压力。',
    '父母': '武曲入父母宫，主父母财运，但易与父母有代沟。'
  },
  // 天梁星落宫解读
  '天梁': {
    '命宫': '天梁入命宫，主贵人相助，性格温和，但易优柔寡断。',
    '兄弟': '天梁入兄弟宫，主兄弟贵人，但易与兄弟有分歧。',
    '夫妻': '天梁入夫妻宫，主配偶贵人，但易有婚姻波折。',
    '子女': '天梁入子女宫，主子女贵人，但易与子女有代沟。',
    '财帛': '天梁入财帛宫，主财运稳定，但易有破财之虞。',
    '疾厄': '天梁入疾厄宫，主身体稳定，但易有慢性疾病。',
    '迁移': '天梁入迁移宫，主外出稳定，但易有意外之虞。',
    '仆役': '天梁入仆役宫，主下属稳定，但易与下属有矛盾。',
    '官禄': '天梁入官禄宫，主官运稳定，但易有官非之虞。',
    '田宅': '天梁入田宅宫，主房产稳定，但易有房产纠纷。',
    '福德': '天梁入福德宫，主福气稳定，但易有精神压力。',
    '父母': '天梁入父母宫，主父母贵人，但易与父母有代沟。'
  }
}

// 紫微斗数计算器类
class ZiweiCalculator {
  constructor() {
    this.lunar = lunar
    this.palaces = Array(12).fill().map(() => ({ name: '', mainStar: '', stars: [] }))
  }

  // 计算紫微斗数
  calculate(birthInfo) {
    const { birthDate, birthTime, gender } = birthInfo
    
    // 计算命宫和身宫
    const mingGong = this.calculateMingGong(birthDate, birthTime)
    const shenGong = this.calculateShenGong(birthDate, birthTime)
    
    // 计算星耀
    const stars = this.calculateStars(birthDate, birthTime, gender)
    
    // 生成宫位数据
    const palaces = this.calculatePalaces(mingGong)
    
    // 分析十四主星组合
    const starCombinations = this.analyzeStarCombinations(stars)
    
    // 生成命宫星盘
    const mingGongChart = this.generateMingGongChart(mingGong, stars)
    
    // 获取解读
    const interpretation = this.getInterpretation(mingGong, shenGong, palaces, stars)
    
    return {
      mingGong,
      shenGong,
      palaces,
      stars,
      starCombinations,
      mingGongChart,
      interpretation
    }
  }

  // 计算命宫
  calculateMingGong(lunarDate, birthTime) {
    // 简化的命宫计算
    const hourNum = parseInt(birthTime.split(':')[0])
    return (lunarDate.day + hourNum) % 12 + 1
  }

  // 计算身宫
  calculateShenGong(lunarDate, birthTime) {
    // 简化的身宫计算
    const hourNum = parseInt(birthTime.split(':')[0])
    return (lunarDate.month + hourNum) % 12 + 1
  }

  // 计算宫位
  calculatePalaces(mingGong) {
    const palaces = []
    for (let i = 1; i <= 12; i++) {
      const position = ((mingGong + i - 1) % 12) || 12
      palaces.push({
        position: position,
        name: PALACES[position - 1],
        isMingGong: position === mingGong
      })
    }
    return palaces
  }

  // 计算星耀
  calculateStars(lunarDate, birthTime, gender) {
    const stars = []
    const baseStars = [
      { name: '紫微', type: '主星', element: '土', nature: '吉' },
      { name: '天机', type: '主星', element: '木', nature: '吉' },
      { name: '太阳', type: '主星', element: '火', nature: '吉' },
      { name: '武曲', type: '主星', element: '金', nature: '吉' },
      { name: '天同', type: '主星', element: '水', nature: '吉' },
      { name: '廉贞', type: '主星', element: '火', nature: '凶' },
      { name: '天府', type: '主星', element: '土', nature: '吉' },
      { name: '太阴', type: '主星', element: '水', nature: '吉' },
      { name: '贪狼', type: '主星', element: '木', nature: '凶' },
      { name: '巨门', type: '主星', element: '土', nature: '吉' },
      { name: '天相', type: '主星', element: '金', nature: '吉' },
      { name: '天梁', type: '主星', element: '木', nature: '吉' }
    ]
    
    // 根据农历日期和时间计算星耀位置
    const hourNum = parseInt(birthTime.split(':')[0])
    const offset = (lunarDate.day + hourNum) % 12
    
    baseStars.forEach((star, index) => {
      stars.push({
        ...star,
        position: (index + offset) % 12 + 1
      })
    })
    
    return stars
  }

  // 获取解读
  getInterpretation(mingGong, shenGong, palaces, stars) {
    return {
      general: this.getGeneralInterpretation(mingGong, shenGong),
      palaces: this.getPalacesInterpretation(palaces, stars),
      advice: this.getAdvice(mingGong, shenGong, palaces, stars)
    }
  }

  // 获取总体解读
  getGeneralInterpretation(mingGong, shenGong) {
    return `命宫位于第${mingGong}宫，身宫位于第${shenGong}宫。这表示您的基本性格和人生发展方向。`
  }

  // 获取宫位解读
  getPalacesInterpretation(palaces, stars) {
    return palaces.map(palace => {
      const palaceStars = stars.filter(star => star.position === palace.position)
      return {
        palace: palace.name,
        stars: palaceStars.map(star => star.name).join('、'),
        meaning: this.getPalaceMeaning(palace.position, palaceStars)
      }
    })
  }

  // 获取宫位含义
  getPalaceMeaning(position, stars) {
    const meanings = {
      1: '代表基本性格和人生发展方向',
      2: '代表兄弟姐妹关系和竞争',
      3: '代表婚姻和感情生活',
      4: '代表子女和创造力',
      5: '代表财运和物质生活',
      6: '代表健康状况',
      7: '代表变动和迁移',
      8: '代表人际关系和下属',
      9: '代表事业和地位',
      10: '代表家庭和不动产',
      11: '代表精神和享受',
      12: '代表父母和长辈关系'
    }
    return meanings[position] || '未知含义'
  }

  // 获取建议
  getAdvice(mingGong, shenGong, palaces, stars) {
    return {
      career: this.getCareerAdvice(mingGong, palaces, stars),
      relationship: this.getRelationshipAdvice(shenGong, palaces, stars),
      wealth: this.getWealthAdvice(palaces, stars),
      health: this.getHealthAdvice(palaces, stars)
    }
  }

  // 获取事业建议
  getCareerAdvice(mingGong, palaces, stars) {
    const careerPalace = palaces.find(p => p.position === 9)
    const careerStars = stars.filter(s => s.position === 9)
    
    if (!careerPalace) {
      return '在事业发展方面，建议根据个人兴趣和能力选择适合的职业。'
    }
    
    const starNames = careerStars.map(s => s.name).join('、')
    return `在事业发展方面，${careerPalace.name}${starNames ? `有${starNames}等星耀，` : ''}建议您${this.getCareerSuggestion(careerStars)}`
  }

  // 获取感情建议
  getRelationshipAdvice(shenGong, palaces, stars) {
    const relationshipPalace = palaces.find(p => p.position === 3)
    const relationshipStars = stars.filter(s => s.position === 3)
    
    if (!relationshipPalace) {
      return '在感情方面，建议保持真诚和包容的态度。'
    }
    
    const starNames = relationshipStars.map(s => s.name).join('、')
    return `在感情方面，${relationshipPalace.name}${starNames ? `有${starNames}等星耀，` : ''}建议您${this.getRelationshipSuggestion(relationshipStars)}`
  }

  // 获取财运建议
  getWealthAdvice(palaces, stars) {
    const wealthPalace = palaces.find(p => p.position === 5)
    const wealthStars = stars.filter(s => s.position === 5)
    
    if (!wealthPalace) {
      return '在理财方面，建议保持稳健和理性的态度。'
    }
    
    const starNames = wealthStars.map(s => s.name).join('、')
    return `在财运方面，${wealthPalace.name}${starNames ? `有${starNames}等星耀，` : ''}建议您${this.getWealthSuggestion(wealthStars)}`
  }

  // 获取健康建议
  getHealthAdvice(palaces, stars) {
    const healthPalace = palaces.find(p => p.position === 6)
    const healthStars = stars.filter(s => s.position === 6)
    
    if (!healthPalace) {
      return '在健康方面，建议注意保持规律作息，均衡饮食，适度运动。'
    }
    
    const starNames = healthStars.map(s => s.name).join('、')
    return `在健康方面，${healthPalace.name}${starNames ? `有${starNames}等星耀，` : ''}建议您${this.getHealthSuggestion(healthStars)}`
  }

  // 获取事业建议内容
  getCareerSuggestion(stars) {
    const suggestions = {
      '紫微': '适合担任领导职务，发挥管理才能。',
      '天机': '适合从事策划、研究等工作。',
      '太阳': '适合从事公职或服务性工作。',
      '武曲': '适合从事金融、财务等工作。',
      '天同': '适合从事艺术、创作等工作。',
      '廉贞': '适合从事法律、监察等工作。',
      '天府': '适合从事商业、贸易等工作。',
      '太阴': '适合从事文教、医疗等工作。',
      '贪狼': '适合从事销售、市场等工作。',
      '巨门': '适合从事技术、专业等工作。',
      '天相': '适合从事行政、管理等工作。',
      '天梁': '适合从事建筑、工程等工作。'
    }
    return stars.map(star => suggestions[star.name]).join('；') || '根据个人兴趣和能力选择适合的职业。'
  }

  // 获取感情建议内容
  getRelationshipSuggestion(stars) {
    const suggestions = {
      '紫微': '在感情中保持主导地位，但要注意包容对方。',
      '天机': '感情中要善于沟通，保持理性。',
      '太阳': '感情中要主动付出，保持热情。',
      '武曲': '感情中要注重实际，保持稳定。',
      '天同': '感情中要注重精神交流，保持浪漫。',
      '廉贞': '感情中要注重原则，保持正直。',
      '天府': '感情中要注重物质基础，保持务实。',
      '太阴': '感情中要注重情感交流，保持温柔。',
      '贪狼': '感情中要把握机会，保持主动。',
      '巨门': '感情中要注重专业态度，保持专注。',
      '天相': '感情中要注重平衡，保持和谐。',
      '天梁': '感情中要注重责任，保持担当。'
    }
    return stars.map(star => suggestions[star.name]).join('；') || '在感情中保持真诚和包容的态度。'
  }

  // 获取财运建议内容
  getWealthSuggestion(stars) {
    const suggestions = {
      '紫微': '适合投资理财，注意风险控制。',
      '天机': '适合稳健理财，注重长期收益。',
      '太阳': '适合正当收入，避免投机取巧。',
      '武曲': '适合金融投资，把握市场机会。',
      '天同': '适合艺术投资，注重精神价值。',
      '廉贞': '适合稳健理财，避免冒险。',
      '天府': '适合商业投资，把握商机。',
      '太阴': '适合稳健理财，注重安全。',
      '贪狼': '适合激进投资，把握机会。',
      '巨门': '适合专业投资，注重技术分析。',
      '天相': '适合平衡投资，注重风险分散。',
      '天梁': '适合实业投资，注重实体价值。'
    }
    return stars.map(star => suggestions[star.name]).join('；') || '在理财方面保持稳健和理性的态度。'
  }

  // 获取健康建议内容
  getHealthSuggestion(stars) {
    const suggestions = {
      '紫微': '注意心脏和头部健康，保持运动。',
      '天机': '注意神经系统健康，保持充足睡眠。',
      '太阳': '注意眼睛和心脏健康，保持适度运动。',
      '武曲': '注意骨骼和关节健康，保持正确姿势。',
      '天同': '注意内分泌系统健康，保持心情愉悦。',
      '廉贞': '注意消化系统健康，保持规律饮食。',
      '天府': '注意呼吸系统健康，保持空气清新。',
      '太阴': '注意妇科和内分泌健康，保持良好作息。',
      '贪狼': '注意肝脏和胆囊健康，避免过度劳累。',
      '巨门': '注意皮肤和免疫系统健康，保持卫生。',
      '天相': '注意全身健康，保持均衡饮食。',
      '天梁': '注意骨骼和肌肉健康，保持适度运动。'
    }
    return stars.map(star => suggestions[star.name]).join('；') || '注意保持规律作息，均衡饮食，适度运动。'
  }

  // 分析十四主星组合
  analyzeStarCombinations(stars) {
    const combinations = []
    
    // 紫微星系组合
    const ziweiStars = stars.filter(s => s.name === '紫微' || s.name === '天机' || s.name === '太阳')
    if (ziweiStars.length > 0) {
      combinations.push({
        type: '紫微星系',
        stars: ziweiStars.map(s => s.name),
        meaning: '主贵气，性格刚毅，有领导才能，但易刚愎自用。'
      })
    }
    
    // 武曲星系组合
    const wuquStars = stars.filter(s => s.name === '武曲' || s.name === '天同' || s.name === '廉贞')
    if (wuquStars.length > 0) {
      combinations.push({
        type: '武曲星系',
        stars: wuquStars.map(s => s.name),
        meaning: '主财运，性格刚毅，善于理财，但易固执。'
      })
    }
    
    // 天府星系组合
    const tianfuStars = stars.filter(s => s.name === '天府' || s.name === '太阴' || s.name === '贪狼')
    if (tianfuStars.length > 0) {
      combinations.push({
        type: '天府星系',
        stars: tianfuStars.map(s => s.name),
        meaning: '主富贵，性格温和，善于交际，但易优柔寡断。'
      })
    }
    
    // 天相星系组合
    const tianxiangStars = stars.filter(s => s.name === '天相' || s.name === '天梁' || s.name === '七杀')
    if (tianxiangStars.length > 0) {
      combinations.push({
        type: '天相星系',
        stars: tianxiangStars.map(s => s.name),
        meaning: '主官禄，性格正直，善于管理，但易过于严肃。'
      })
    }
    
    return combinations
  }

  // 生成命宫星盘
  generateMingGongChart(mingGong, stars) {
    // 获取命宫中的所有星耀
    const mingGongStars = stars.filter(s => s.position === mingGong)
    
    // 按类型分类星耀
    const mainStars = mingGongStars.filter(s => s.type === '主星')
    const luckyStars = mingGongStars.filter(s => s.nature === '吉' && s.type !== '主星')
    const evilStars = mingGongStars.filter(s => s.nature === '凶')
    
    const chart = {
      position: mingGong,
      mainStars,
      luckyStars,
      evilStars,
      interpretation: this.getMingGongInterpretation(mingGong, stars)
    }
    
    return chart
  }

  // 获取命宫解读
  getMingGongInterpretation(mingGong, stars) {
    const mainStars = stars.filter(s => s.type === '主星' && s.position === mingGong)
    const luckyStars = stars.filter(s => s.nature === '吉' && s.position === mingGong)
    const evilStars = stars.filter(s => s.nature === '凶' && s.position === mingGong)
    
    let interpretation = `命宫位于第${mingGong}宫，主星：${mainStars.map(s => s.name).join('、')}。`
    
    if (luckyStars.length > 0) {
      interpretation += `吉星：${luckyStars.map(s => s.name).join('、')}，主吉利。`
    }
    
    if (evilStars.length > 0) {
      interpretation += `煞星：${evilStars.map(s => s.name).join('、')}，需注意。`
    }
    
    return interpretation
  }

  // 预测人生轨迹
  predictLifeTrajectory(mingGong, shenGong, stars) {
    return {
      career: this.predictCareer(mingGong, stars),
      marriage: this.predictMarriage(shenGong, stars),
      wealth: this.predictWealth(stars),
      health: this.predictHealth(stars)
    }
  }

  // 预测事业
  predictCareer(mingGong, stars) {
    const careerStars = stars.filter(s => s.position === mingGong)
    const mainStars = careerStars.filter(s => s.type === '主星')
    
    let prediction = '事业方面：'
    if (mainStars.length > 0) {
      prediction += `命宫主星${mainStars.map(s => s.name).join('、')}，`
      if (mainStars.some(s => s.name === '紫微')) {
        prediction += '适合担任领导职务，有较强的管理能力。'
      } else if (mainStars.some(s => s.name === '天机')) {
        prediction += '适合从事策划、研究等工作，思维敏捷。'
      } else if (mainStars.some(s => s.name === '武曲')) {
        prediction += '适合从事金融、财务等工作，理财能力强。'
      }
    }
    return prediction
  }

  // 预测婚姻
  predictMarriage(shenGong, stars) {
    const marriageStars = stars.filter(s => s.position === shenGong)
    const luckyStars = marriageStars.filter(s => s.nature === '吉')
    const evilStars = marriageStars.filter(s => s.nature === '凶')
    
    let prediction = '婚姻方面：'
    if (luckyStars.length > 0) {
      prediction += `身宫有吉星${luckyStars.map(s => s.name).join('、')}，婚姻较为顺利。`
    }
    if (evilStars.length > 0) {
      prediction += `但需注意煞星${evilStars.map(s => s.name).join('、')}的影响，可能会有波折。`
    }
    return prediction
  }

  // 预测财运
  predictWealth(stars) {
    const wealthStars = stars.filter(s => s.position === 5) // 财帛宫
    const luckyStars = wealthStars.filter(s => s.nature === '吉')
    const evilStars = wealthStars.filter(s => s.nature === '凶')
    
    let prediction = '财运方面：'
    if (luckyStars.length > 0) {
      prediction += `财帛宫有吉星${luckyStars.map(s => s.name).join('、')}，财运较好。`
    }
    if (evilStars.length > 0) {
      prediction += `但需注意煞星${evilStars.map(s => s.name).join('、')}的影响，理财需谨慎。`
    }
    return prediction
  }

  // 预测健康
  predictHealth(stars) {
    const healthStars = stars.filter(s => s.position === 6) // 疾厄宫
    const luckyStars = healthStars.filter(s => s.nature === '吉')
    const evilStars = healthStars.filter(s => s.nature === '凶')
    
    let prediction = '健康方面：'
    if (luckyStars.length > 0) {
      prediction += `疾厄宫有吉星${luckyStars.map(s => s.name).join('、')}，身体状况较好。`
    }
    if (evilStars.length > 0) {
      prediction += `但需注意煞星${evilStars.map(s => s.name).join('、')}的影响，易有疾病。`
    }
    return prediction
  }
}

// 导出模块
module.exports = new ZiweiCalculator() 