// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { code } = event

  try {
    // 企业微信API调用
    // 这里需要根据实际的企业微信配置进行调用
    const result = {
      userInfo: {
        openid: wxContext.OPENID,
        appid: wxContext.APPID,
        unionid: wxContext.UNIONID,
      },
      wxworkUserInfo: {
        // 企业微信特有信息
        userId: 'wx_user_' + Date.now(),
        name: '用户',
        department: ['技术部'],
        position: '员工',
        mobile: '',
        email: '',
        avatar: ''
      }
    }

    return {
      success: true,
      data: result
    }
  } catch (error) {
    console.error('获取企业微信用户信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
} 