.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.form-section {
  margin-bottom: 30rpx;
}

.person-info {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.picker {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.analyze-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4a5568;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 40rpx 0;
}

.result-section {
  margin-top: 40rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.bazi-comparison {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.person-bazi {
  flex: 1;
}

.person-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.bazi-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.grid-item {
  text-align: center;
}

.pillar-name {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.pillar-content {
  background: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.pillar-content text {
  font-size: 26rpx;
  color: #333;
}

.wuxing-analysis {
  margin-top: 20rpx;
}

.wuxing-item {
  margin-bottom: 20rpx;
}

.element-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.marriage-score {
  text-align: center;
  margin: 30rpx 0;
}

.score {
  font-size: 72rpx;
  font-weight: bold;
  color: #4a5568;
}

.max-score {
  font-size: 32rpx;
  color: #666;
}

.score-details {
  margin-top: 30rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.aspect-name {
  font-size: 28rpx;
  color: #333;
}

.stars {
  color: #f0b90b;
  font-size: 28rpx;
}

.advice {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 顶部banner */
.banner {
  position: relative;
  width: 100%;
  height: 360rpx;
  overflow: hidden;
}

.banner-bg {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  left: 40rpx;
  bottom: 40rpx;
  color: #fff;
  z-index: 1;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 表单区域 */
.form-section {
  margin: 30rpx;
}

.male .section-title {
  color: #4A90E2;
}

.female .section-title {
  color: #FF69B4;
}

.submit-section {
  margin: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF69B4 0%, #4A90E2 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.submit-btn.disabled {
  opacity: 0.6;
}

.price {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  font-weight: normal;
}

/* 结果区域 */
.analysis-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 12rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 缘分指数 */
.score-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin: 30rpx 0;
}

.score-desc {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

/* 八字合婚 */
.bazi-analysis {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 五行相合 */
.wuxing-chart {
  width: 100%;
  height: 300rpx;
  margin: 20rpx 0;
}

.wuxing-canvas {
  width: 100%;
  height: 100%;
}

.wuxing-analysis {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 婚姻建议 */
.advice-list {
  margin-top: 20rpx;
}

.advice-item {
  margin-bottom: 24rpx;
}

.advice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.advice-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 分享按钮 */
.share-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
}

.share-btn {
  width: 100%;
  height: 88rpx;
  background: #F8F9FD;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.share-icon {
  width: 36rpx;
  height: 36rpx;
}

.share-btn text {
  font-size: 28rpx;
  color: #666;
} 