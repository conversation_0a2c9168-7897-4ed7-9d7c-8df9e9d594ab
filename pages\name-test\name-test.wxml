<!--姓名测试页面-->
<view class="page-container">
  <view class="header">
    <view class="title">姓名测试</view>
    <view class="subtitle">通过五行数理分析姓名</view>
  </view>

  <!-- 姓名输入区域 -->
  <view class="input-section">
    <view class="input-group">
      <view class="label">姓氏</view>
      <input 
        class="name-input" 
        type="text" 
        placeholder="请输入姓氏"
        value="{{surname}}"
        bindinput="onSurnameInput"
        maxlength="10"
      />
    </view>
    <view class="input-group">
      <view class="label">名字</view>
      <input 
        class="name-input" 
        type="text" 
        placeholder="请输入名字"
        value="{{givenName}}"
        bindinput="onGivenNameInput"
        maxlength="10"
      />
    </view>
    <view class="input-group">
      <view class="label">性别</view>
      <radio-group class="gender-group" bindchange="onGenderChange">
        <label class="radio-item">
          <radio value="male" checked="{{gender === 'male'}}"/>
          <text>男</text>
        </label>
        <label class="radio-item">
          <radio value="female" checked="{{gender === 'female'}}"/>
          <text>女</text>
        </label>
      </radio-group>
    </view>
    <button class="test-btn" bindtap="startNameTest" disabled="{{!canTest}}">
      开始测试
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{isLoading}}">
    <view class="loading-icon">
      <view class="spinner"></view>
    </view>
    <view class="loading-text">正在分析姓名...</view>
  </view>

  <!-- 测试结果 -->
  <view class="result-section" wx:if="{{testResult && !isLoading}}">
    <view class="result-header">
      <view class="test-name">{{testResult.fullName}}</view>
      <view class="overall-score">
        <text class="score-label">综合评分</text>
        <text class="score-value">{{testResult.totalScore}}分</text>
      </view>
    </view>

    <!-- 五格数理 -->
    <view class="wuge-section">
      <view class="section-title">五格数理</view>
      <view class="wuge-grid">
        <view class="wuge-item" wx:for="{{testResult.wuge}}" wx:key="type">
          <view class="wuge-name">{{item.name}}</view>
          <view class="wuge-number">{{item.number}}</view>
          <view class="wuge-level {{item.level}}">{{item.levelText}}</view>
        </view>
      </view>
    </view>

    <!-- 三才配置 -->
    <view class="sancai-section">
      <view class="section-title">三才配置</view>
      <view class="sancai-result">
        <view class="sancai-item">
          <text class="sancai-label">天格</text>
          <text class="sancai-value">{{testResult.sancai.tian}}</text>
        </view>
        <view class="sancai-item">
          <text class="sancai-label">人格</text>
          <text class="sancai-value">{{testResult.sancai.ren}}</text>
        </view>
        <view class="sancai-item">
          <text class="sancai-label">地格</text>
          <text class="sancai-value">{{testResult.sancai.di}}</text>
        </view>
      </view>
      <view class="sancai-analysis">
        <text>{{testResult.sancai.analysis}}</text>
      </view>
    </view>

    <!-- 详细分析 -->
    <view class="analysis-section">
      <view class="section-title">详细分析</view>
      <view class="analysis-item" wx:for="{{testResult.analysis}}" wx:key="type">
        <view class="analysis-title">{{item.title}}</view>
        <view class="analysis-content">{{item.content}}</view>
        <view class="analysis-score">
          <text>{{item.score}}分</text>
          <view class="score-bar">
            <view class="score-fill" style="width: {{item.score}}%"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 改名建议 -->
    <view class="suggestion-section" wx:if="{{testResult.suggestions}}">
      <view class="section-title">改名建议</view>
      <view class="suggestion-list">
        <view class="suggestion-item" wx:for="{{testResult.suggestions}}" wx:key="index">
          <view class="suggestion-name">{{item.name}}</view>
          <view class="suggestion-score">{{item.score}}分</view>
          <view class="suggestion-reason">{{item.reason}}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="save-btn" bindtap="saveResult">保存结果</button>
      <button class="share-btn" bindtap="shareResult">分享</button>
      <button class="retest-btn" bindtap="resetTest">重新测试</button>
    </view>
  </view>
</view> 