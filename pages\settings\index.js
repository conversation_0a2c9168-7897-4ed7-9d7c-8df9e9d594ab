const app = getApp()

Page({
  data: {
    // 通用设置
    generalSettings: {
      darkMode: false,
      language: 'zh_CN',
      fontSize: 'medium'
    },
    // 隐私设置
    privacySettings: {
      showOnline: true,
      allowSearch: true,
      showLastSeen: true
    },
    // 消息通知
    notificationSettings: {
      pushEnabled: true,
      sound: true,
      vibrate: true,
      showPreview: true
    },
    // 字体大小选项
    fontSizeOptions: [
      { value: 'small', label: '小' },
      { value: 'medium', label: '中' },
      { value: 'large', label: '大' }
    ],
    // 语言选项
    languageOptions: [
      { value: 'zh_CN', label: '简体中文' },
      { value: 'zh_TW', label: '繁体中文' },
      { value: 'en_US', label: 'English' }
    ],
    // 缓存大小
    cacheSize: '0MB'
  },

  onLoad() {
    this.loadSettings()
    this.calculateCacheSize()
  },

  // 加载设置
  loadSettings() {
    try {
      const settings = wx.getStorageSync('appSettings')
      if (settings) {
        this.setData({
          generalSettings: { ...this.data.generalSettings, ...settings.general },
          privacySettings: { ...this.data.privacySettings, ...settings.privacy },
          notificationSettings: { ...this.data.notificationSettings, ...settings.notification }
        })
      }
    } catch (err) {
      console.error('加载设置失败:', err)
    }
  },

  // 保存设置
  saveSettings() {
    try {
      const settings = {
        general: this.data.generalSettings,
        privacy: this.data.privacySettings,
        notification: this.data.notificationSettings
      }
      wx.setStorageSync('appSettings', settings)
      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })
    } catch (err) {
      console.error('保存设置失败:', err)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 切换开关设置
  toggleSetting(e) {
    const { type, key } = e.currentTarget.dataset
    const settingKey = `${type}Settings.${key}`
    this.setData({
      [settingKey]: !this.data.generalSettings[key]
    }, () => {
      this.saveSettings()
    })
  },

  // 选择语言
  changeLanguage(e) {
    const language = e.detail.value
    this.setData({
      'generalSettings.language': language
    }, () => {
      this.saveSettings()
      // 重启小程序以应用语言设置
      wx.showModal({
        title: '提示',
        content: '切换语言需要重启小程序，是否立即重启？',
        success: (res) => {
          if (res.confirm) {
            wx.reLaunch({
              url: '/pages/index/index'
            })
          }
        }
      })
    })
  },

  // 选择字体大小
  changeFontSize(e) {
    const fontSize = e.detail.value
    this.setData({
      'generalSettings.fontSize': fontSize
    }, () => {
      this.saveSettings()
      // 应用字体大小设置
      wx.setStorageSync('fontSize', fontSize)
    })
  },

  // 计算缓存大小
  async calculateCacheSize() {
    try {
      const res = await wx.getStorageInfo()
      const sizeInMB = (res.currentSize / 1024).toFixed(2)
      this.setData({
        cacheSize: `${sizeInMB}MB`
      })
    } catch (err) {
      console.error('获取缓存大小失败:', err)
    }
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '提示',
      content: '确定要清除所有缓存吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            this.setData({
              cacheSize: '0MB'
            })
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })
          } catch (err) {
            console.error('清除缓存失败:', err)
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 关于我们
  navigateToAbout() {
    wx.navigateTo({
      url: '/pages/about/index'
    })
  },

  // 用户协议
  navigateToTerms() {
    wx.navigateTo({
      url: '/pages/terms/index'
    })
  },

  // 隐私政策
  navigateToPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    })
  }
}) 