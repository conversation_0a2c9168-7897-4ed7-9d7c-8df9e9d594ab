<!--pages/divination/divination.wxml-->
<view class="container">
  <!-- 占卜方式选择 -->
  <view class="divination-types">
    <view class="type-item {{selectedType === 'yijing' ? 'active' : ''}}" bindtap="selectType" data-type="yijing">
      <image class="type-icon" src="/assets/icons/divination/yijing.png" mode="aspectFit"></image>
      <text class="type-name">易经占卜</text>
    </view>
    <view class="type-item {{selectedType === 'tarot' ? 'active' : ''}}" bindtap="selectType" data-type="tarot">
      <image class="type-icon" src="/assets/icons/divination/tarot.png" mode="aspectFit"></image>
      <text class="type-name">塔罗牌</text>
    </view>
    <view class="type-item {{selectedType === 'ziwei' ? 'active' : ''}}" bindtap="selectType" data-type="ziwei">
      <image class="type-icon" src="/assets/icons/divination/ziwei.png" mode="aspectFit"></image>
      <text class="type-name">紫微斗数</text>
    </view>
  </view>

  <!-- 问题输入区域 -->
  <view class="input-section">
    <view class="section-title">请描述您的问题</view>
    <textarea class="question-input" placeholder="请详细描述您想咨询的问题..." value="{{question}}" bindinput="onQuestionInput"></textarea>
    <button class="submit-btn" bindtap="startDivination" loading="{{isLoading}}">
      开始占卜
    </button>
  </view>

  <!-- 占卜结果展示 -->
  <view class="result-section" wx:if="{{divinationResult}}">
    <view class="section-title">占卜结果</view>
    
    <!-- 卦象/牌阵展示 -->
    <view class="divination-display">
      <block wx:if="{{selectedType === 'yijing'}}">
        <view class="gua-display">
          <view class="gua-title">本卦：{{divinationResult.benGua.name}}</view>
          <view class="gua-image">
            <view class="yao-line" wx:for="{{divinationResult.benGua.yao}}" wx:key="index">
              <view class="yao-content {{item.type === 'yang' ? 'yang' : 'yin'}}"></view>
            </view>
          </view>
        </view>
      </block>
      
      <block wx:elif="{{selectedType === 'tarot'}}">
        <view class="tarot-display">
          <view class="card-grid">
            <view class="card-item" wx:for="{{divinationResult.cards}}" wx:key="index">
              <image class="card-image" src="{{item.image}}" mode="aspectFit"></image>
              <text class="card-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 解释区域 -->
    <view class="interpretation-section">
      <view class="interpretation-item">
        <view class="item-title">总体解释</view>
        <view class="item-content">{{divinationResult.interpretation.general}}</view>
      </view>

      <view class="interpretation-item">
        <view class="item-title">事业运势</view>
        <view class="item-content">{{divinationResult.interpretation.career}}</view>
      </view>

      <view class="interpretation-item">
        <view class="item-title">感情运势</view>
        <view class="item-content">{{divinationResult.interpretation.love}}</view>
      </view>

      <view class="interpretation-item">
        <view class="item-title">财运分析</view>
        <view class="item-content">{{divinationResult.interpretation.wealth}}</view>
      </view>

      <view class="interpretation-item">
        <view class="item-title">健康提醒</view>
        <view class="item-content">{{divinationResult.interpretation.health}}</view>
      </view>

      <view class="interpretation-item">
        <view class="item-title">建议</view>
        <view class="item-content">{{divinationResult.interpretation.advice}}</view>
      </view>
    </view>
  </view>
</view>
