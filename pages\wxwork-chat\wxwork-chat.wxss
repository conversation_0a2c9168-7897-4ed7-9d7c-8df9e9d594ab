/* pages/wxwork-chat/wxwork-chat.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 用户信息栏样式 */
.user-info-bar {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  position: relative;
  margin-right: 30rpx;
}

.avatar-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.online-status {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  background: #52c41a;
}

.online-status.offline {
  background: #d9d9d9;
}

.online-status.busy {
  background: #ff4d4f;
}

.user-details {
  flex: 1;
  overflow: hidden;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-info {
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
  border: none;
  color: #666;
}

.action-btn:active {
  background: #e5e5e5;
}

/* 消息列表样式 */
.message-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;
}

.ai-message {
  justify-content: flex-start;
}

.user-message {
  justify-content: flex-end;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  margin: 0 20rpx;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f0f0f0;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 20rpx 25rpx;
  border-radius: 20rpx;
  position: relative;
  word-wrap: break-word;
  line-height: 1.4;
}

.ai-bubble {
  background: #fff;
  color: #333;
  border: 1rpx solid #e5e5e5;
  margin-left: 10rpx;
}

.ai-bubble::before {
  content: '';
  position: absolute;
  left: -10rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 10rpx solid transparent;
  border-right-color: #fff;
}

.user-bubble {
  background: #8a2be2;
  color: #fff;
  margin-right: 10rpx;
}

.user-bubble::after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 10rpx solid transparent;
  border-left-color: #8a2be2;
}

.message-text {
  font-size: 28rpx;
  white-space: pre-wrap;
}

.message-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}

.user-message .message-time {
  text-align: right;
}

/* AI消息操作按钮 */
.message-actions {
  display: flex;
  gap: 10rpx;
  margin-top: 15rpx;
  justify-content: flex-start;
}

.send-to-chat-btn {
  background: #8a2be2;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
}

.copy-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e5e5e5;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
}

/* 加载状态 */
.loading-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.typing-indicator {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 26rpx;
}

.dots {
  display: flex;
  margin-left: 10rpx;
}

.dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: #999;
  margin: 0 2rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 快速操作按钮 */
.quick-actions {
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx 0;
}

.quick-scroll {
  white-space: nowrap;
}

.quick-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  margin: 0 10rpx;
  background: #f9f9f9;
  border-radius: 15rpx;
  min-width: 120rpx;
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
}

.quick-item:first-child {
  margin-left: 20rpx;
}

.quick-item:last-child {
  margin-right: 20rpx;
}

.quick-item:active {
  background: #e5e5e5;
  transform: scale(0.95);
}

.quick-item icon {
  margin-bottom: 8rpx;
  color: #8a2be2;
}

/* 输入区域 */
.input-area {
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 10rpx 20rpx;
}

.message-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  min-height: 60rpx;
  max-height: 200rpx;
  padding: 10rpx 0;
  background: transparent;
  border: none;
}

.send-btn {
  margin-left: 20rpx;
  padding: 15rpx 30rpx;
  background: #d9d9d9;
  color: #999;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.send-btn.active {
  background: #8a2be2;
  color: #fff;
}

.send-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

/* 用户资料弹窗 */
.user-profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-profile-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #fff;
  border-radius: 20rpx;
  width: 85%;
  max-width: 650rpx;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.modal-close {
  color: #999;
  font-size: 20rpx;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.profile-section {
  margin-bottom: 30rpx;
}

.profile-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  font-size: 26rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modal-footer {
  border-top: 1rpx solid #f5f5f5;
  padding: 30rpx;
}

.modal-btn {
  width: 100%;
  padding: 25rpx 0;
  background: #8a2be2;
  color: #fff;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn:active {
  background: #7a1fb2;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 响应式适配 */
@media (max-width: 400px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .quick-item {
    min-width: 100rpx;
    font-size: 20rpx;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
} 