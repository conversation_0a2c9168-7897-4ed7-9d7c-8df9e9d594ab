# 卦里乾坤小程序前端实现

## 📋 项目概述

本项目是"卦里乾坤"小程序的前端实现代码，基于微信小程序原生框架开发，提供了完整的API接口调用、状态管理、页面组件等功能。

## 🏗️ 项目结构

```
frontend-implementation/
├── api/                    # API接口层
│   ├── auth.js            # 用户认证相关API
│   ├── birthInfo.js       # 出生信息管理API
│   ├── aiChat.js          # AI聊天相关API
│   └── analysis.js        # 命理分析相关API
├── pages/                 # 页面文件
│   ├── login/             # 登录页面
│   ├── birth-info/        # 出生信息页面
│   ├── ai-chat/           # AI聊天页面
│   ├── analysis/          # 分析页面
│   └── index/             # 首页
├── utils/                 # 工具函数
│   ├── request.js         # 网络请求工具
│   └── wx.js              # 微信API封装
├── store/                 # 状态管理
│   └── index.js           # 全局状态管理
├── mixins/                # 页面混入
│   └── basePage.js        # 基础页面混入
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 配置API地址

在 `utils/request.js` 中修改API基础地址：

```javascript
const BASE_URL = 'https://your-api-domain.com' // 替换为实际的API地址
```

### 2. 使用API接口

```javascript
// 导入API方法
import { wxLogin, getUserProfile } from '../../api/auth'
import { getBirthInfo, saveBirthInfo } from '../../api/birthInfo'
import { sendAIMessage, getChatHistory } from '../../api/aiChat'

// 使用示例
try {
  const result = await wxLogin(code, userInfo)
  if (result.status === 'success') {
    // 处理成功逻辑
  }
} catch (error) {
  // 处理错误
}
```

### 3. 使用状态管理

```javascript
import store from '../../store/index'

// 获取状态
const userInfo = store.getState('user.userInfo')
const isLogin = store.getState('user.isLogin')

// 设置状态
store.setState('user.userInfo', newUserInfo)
store.login(userInfo, token, refreshToken)

// 订阅状态变化
const unsubscribe = store.subscribe('user', (userState) => {
  console.log('用户状态变化:', userState)
})

// 取消订阅
unsubscribe()
```

### 4. 使用页面混入

```javascript
import { createPage } from '../../mixins/basePage'

createPage({
  // 页面需要登录
  requireLogin: true,
  
  data: {
    // 页面数据
  },

  // 页面生命周期
  pageOnLoad(options) {
    // 页面加载逻辑
  },

  pageOnShow() {
    // 页面显示逻辑
  },

  // 页面方法
  async loadData() {
    // 使用安全的API调用
    await this.safeApiCall(
      () => getUserProfile(),
      {
        loadingText: '加载用户信息...',
        onSuccess: (result) => {
          this.setData({ userInfo: result.data })
        },
        onError: (error) => {
          console.error('加载失败:', error)
        }
      }
    )
  }
})
```

## 📱 页面实现示例

### 登录页面

```javascript
// pages/login/index.js
import { wxLogin } from '../../api/auth'
import { createPage } from '../../mixins/basePage'

createPage({
  data: {
    loading: false
  },

  async wxLogin() {
    try {
      this.showLoading('登录中...')
      
      const loginRes = await wx.login()
      const result = await wxLogin(loginRes.code, this.data.userInfo)
      
      if (result.status === 'success') {
        // 保存登录信息到store
        store.login(result.data.user, result.data.token, result.data.refresh_token)
        
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    } catch (error) {
      this.showError(error.message || '登录失败')
    } finally {
      this.hideLoading()
    }
  }
})
```

### AI聊天页面

```javascript
// pages/ai-chat/index.js
import { sendAIMessage, getChatHistory } from '../../api/aiChat'
import { createPage } from '../../mixins/basePage'

createPage({
  requireLogin: true,
  
  data: {
    messages: [],
    inputText: '',
    sending: false
  },

  async sendMessage() {
    if (!this.data.inputText.trim()) return
    
    try {
      this.setData({ sending: true })
      
      const result = await sendAIMessage({
        message: this.data.inputText,
        session_id: this.data.currentSessionId
      })
      
      if (result.status === 'success') {
        // 添加消息到界面
        this.addMessage(result.data)
        this.setData({ inputText: '' })
      }
    } catch (error) {
      this.showError('发送失败')
    } finally {
      this.setData({ sending: false })
    }
  }
})
```

## 🔧 工具函数使用

### 网络请求

```javascript
import { request, get, post, put, del, upload } from '../utils/request'

// 基础请求
const result = await request({
  url: '/api/user/profile',
  method: 'GET'
})

// 便捷方法
const userData = await get('/api/user/profile')
const createResult = await post('/api/user/profile', { name: 'test' })
const updateResult = await put('/api/user/profile', { name: 'updated' })
const deleteResult = await del('/api/user/profile')

// 文件上传
const uploadResult = await upload('/api/upload', filePath, { type: 'avatar' })
```

### 微信API封装

```javascript
import { 
  showToast, 
  showModal, 
  getStorageSync, 
  setStorageSync,
  getUserProfile,
  chooseImage 
} from '../utils/wx'

// 显示提示
showToast({ title: '操作成功', icon: 'success' })

// 显示确认框
const res = await showModal({
  title: '确认',
  content: '确定要删除吗？'
})

if (res.confirm) {
  // 用户确认
}

// 存储数据
setStorageSync('userInfo', userInfo)
const savedUserInfo = getStorageSync('userInfo')

// 选择图片
const images = await chooseImage({ count: 1 })
```

## 📊 状态管理

### 全局状态结构

```javascript
{
  user: {
    isLogin: false,
    userInfo: null,
    token: null,
    refreshToken: null
  },
  birthInfo: {
    hasInfo: false,
    data: null
  },
  chat: {
    currentSessionId: null,
    sessions: [],
    quickActions: []
  },
  analysis: {
    history: [],
    currentAnalysis: null
  },
  app: {
    loading: false,
    networkStatus: 'online',
    systemInfo: null
  }
}
```

### 状态操作方法

```javascript
// 用户相关
store.login(userInfo, token, refreshToken)
store.logout()
store.updateUserInfo(userInfo)

// 出生信息
store.setBirthInfo(birthInfo)

// 聊天相关
store.setCurrentChatSession(sessionId)
store.addChatSession(session)
store.removeChatSession(sessionId)

// 分析相关
store.addAnalysisRecord(analysis)
store.setCurrentAnalysis(analysis)

// 应用状态
store.setLoading(true)
```

## 🎯 最佳实践

### 1. 错误处理

```javascript
// 使用safeApiCall进行安全的API调用
await this.safeApiCall(
  () => apiMethod(),
  {
    showLoading: true,
    loadingText: '处理中...',
    showError: true,
    onSuccess: (result) => {
      // 成功处理
    },
    onError: (error) => {
      // 错误处理
    }
  }
)
```

### 2. 防抖和节流

```javascript
// 防抖 - 用于搜索输入
onSearchInput() {
  this.debounce(() => {
    this.performSearch()
  }, 500)
}

// 节流 - 用于按钮点击
onButtonTap() {
  this.throttle(() => {
    this.handleButtonClick()
  }, 1000)
}
```

### 3. 数据格式化

```javascript
// 日期格式化
const formattedDate = this.formatDate(new Date(), 'YYYY-MM-DD HH:mm')

// 相对时间
const relativeTime = this.formatRelativeTime(date)

// 数字格式化
const formattedNumber = this.formatNumber(12345.67, 2) // "12,345.67"
```

## 🔐 安全注意事项

1. **Token管理**: 自动处理Token过期和刷新
2. **请求拦截**: 统一添加认证头和错误处理
3. **数据验证**: 在发送请求前验证数据格式
4. **错误处理**: 统一的错误处理和用户提示

## 📝 开发规范

1. **命名规范**: 使用驼峰命名法
2. **注释规范**: 为重要方法添加JSDoc注释
3. **错误处理**: 所有异步操作都要有错误处理
4. **状态管理**: 使用全局store管理共享状态
5. **代码复用**: 使用mixins和工具函数避免重复代码

## 🚀 部署说明

1. 修改API基础地址
2. 配置小程序AppID
3. 上传代码到微信开发者工具
4. 提交审核和发布

---

这套前端实现代码提供了完整的功能模块和最佳实践，可以直接用于小程序开发！
