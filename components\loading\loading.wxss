/* components/loading/loading.wxss */

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.loading-content {
  position: relative;
  background: white;
  border-radius: 25rpx;
  padding: 50rpx;
  box-shadow: 0 10rpx 40rpx rgba(149, 117, 205, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 240rpx;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.loading-spinner {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  margin-bottom: 30rpx;
}

.spinner-dot {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #9575cd;
  animation: spinner 1.2s linear infinite;
}

.dot1 {
  top: 0;
  left: 40rpx;
  animation-delay: 0s;
}

.dot2 {
  top: 10rpx;
  right: 10rpx;
  animation-delay: -0.15s;
}

.dot3 {
  right: 0;
  top: 40rpx;
  animation-delay: -0.3s;
}

.dot4 {
  right: 10rpx;
  bottom: 10rpx;
  animation-delay: -0.45s;
}

.dot5 {
  bottom: 0;
  left: 40rpx;
  animation-delay: -0.6s;
}

.dot6 {
  left: 10rpx;
  bottom: 10rpx;
  animation-delay: -0.75s;
}

.dot7 {
  left: 0;
  top: 40rpx;
  animation-delay: -0.9s;
}

.dot8 {
  left: 10rpx;
  top: 10rpx;
  animation-delay: -1.05s;
}

@keyframes spinner {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  80%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
} 