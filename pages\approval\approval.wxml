<!--pages/approval/approval.wxml-->
<view class="container">
  <!-- 顶部标签页 -->
  <view class="tabs">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
      我发起的
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
      待我审批
    </view>
    <view class="tab-item {{currentTab === 2 ? 'active' : ''}}" bindtap="switchTab" data-index="2">
      我已审批
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <picker mode="selector" range="{{statusOptions}}" value="{{statusIndex}}" bindchange="onStatusChange">
      <view class="filter-item">
        <text>状态：{{statusOptions[statusIndex]}}</text>
        <icon type="arrow_drop_down" size="16"></icon>
      </view>
    </picker>
    
    <picker mode="date" value="{{dateFilter}}" bindchange="onDateChange">
      <view class="filter-item">
        <text>日期：{{dateFilter || '全部'}}</text>
        <icon type="arrow_drop_down" size="16"></icon>
      </view>
    </picker>
  </view>

  <!-- 审批列表 -->
  <scroll-view class="approval-list" scroll-y="true" bindscrolltolower="loadMore">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading && !approvals.length}}">
      <icon type="loading" size="20"></icon>
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:elif="{{!approvals.length}}">
      <image class="empty-image" src="/assets/images/empty-approval.png"></image>
      <text class="empty-text">暂无审批记录</text>
      <button class="empty-btn" bindtap="createApproval" wx:if="{{currentTab === 0}}">
        发起审批
      </button>
    </view>

    <!-- 审批项目列表 -->
    <view class="approval-item" 
          wx:for="{{approvals}}" 
          wx:key="id"
          bindtap="viewApprovalDetail"
          data-approval="{{item}}">
      
      <!-- 审批头部 -->
      <view class="approval-header">
        <view class="approval-type">{{item.typeName}}</view>
        <view class="approval-status {{item.status}}">{{item.statusText}}</view>
      </view>

      <!-- 审批内容 -->
      <view class="approval-content">
        <view class="approval-title">{{item.title}}</view>
        <view class="approval-summary">{{item.summary}}</view>
        
        <!-- 申请人信息 -->
        <view class="applicant-info">
          <image class="applicant-avatar" src="{{item.applicant.avatar}}"></image>
          <view class="applicant-detail">
            <text class="applicant-name">{{item.applicant.name}}</text>
            <text class="apply-time">{{item.createTime}}</text>
          </view>
        </view>

        <!-- 审批金额 (如果有) -->
        <view class="approval-amount" wx:if="{{item.amount}}">
          <text class="amount-label">金额：</text>
          <text class="amount-value">¥{{item.amount}}</text>
        </view>
      </view>

      <!-- 审批操作 -->
      <view class="approval-actions" wx:if="{{currentTab === 1 && item.status === 'pending'}}">
        <button class="action-btn reject-btn" 
                bindtap="handleApproval" 
                data-id="{{item.id}}" 
                data-action="reject"
                catchtap="true">
          拒绝
        </button>
        <button class="action-btn approve-btn" 
                bindtap="handleApproval" 
                data-id="{{item.id}}" 
                data-action="approve"
                catchtap="true">
          通过
        </button>
      </view>

      <!-- 审批流程 -->
      <view class="approval-flow" wx:if="{{item.flowNodes && item.flowNodes.length}}">
        <view class="flow-title">审批流程</view>
        <view class="flow-nodes">
          <view class="flow-node {{node.status}}" 
                wx:for="{{item.flowNodes}}" 
                wx:for-item="node" 
                wx:key="id">
            <view class="node-avatar">
              <image src="{{node.avatar}}" wx:if="{{node.avatar}}"></image>
              <text wx:else>{{node.name ? node.name.charAt(0) : '?'}}</text>
            </view>
            <view class="node-info">
              <text class="node-name">{{node.name}}</text>
              <text class="node-time" wx:if="{{node.handleTime}}">{{node.handleTime}}</text>
            </view>
            <view class="node-status">
              <icon type="success" size="16" wx:if="{{node.status === 'approved'}}"></icon>
              <icon type="cancel" size="16" wx:elif="{{node.status === 'rejected'}}"></icon>
              <icon type="waiting" size="16" wx:elif="{{node.status === 'pending'}}"></icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <icon type="loading" size="16" wx:if="{{loading}}"></icon>
      <text>{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>
  </scroll-view>

  <!-- 发起审批浮动按钮 -->
  <view class="fab-container" wx:if="{{currentTab === 0}}">
    <view class="fab-btn" bindtap="createApproval">
      <icon type="plus" size="20" color="#fff"></icon>
    </view>
  </view>
</view>

<!-- 审批操作弹窗 -->
<view class="approval-modal {{showModal ? 'show' : ''}}" wx:if="{{showModal}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">{{modalAction === 'approve' ? '审批通过' : '审批拒绝'}}</text>
      <icon class="modal-close" type="cancel" size="20" bindtap="closeModal"></icon>
    </view>
    
    <view class="modal-body">
      <textarea 
        class="comment-input" 
        placeholder="请输入审批意见（选填）"
        value="{{comment}}"
        bindinput="onCommentInput"
        maxlength="200">
      </textarea>
      <view class="comment-count">{{comment.length}}/200</view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="closeModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="confirmApproval">确定</button>
    </view>
  </view>
</view>

<!-- 遮罩层 -->
<view class="mask" wx:if="{{showModal}}" bindtap="closeModal"></view> 