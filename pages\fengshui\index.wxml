<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 顶部banner -->
  <view class="banner">
    <image class="banner-bg" src="/assets/images/fengshui/banner-bg.jpg" mode="aspectFill"/>
    <view class="banner-title">
      <text class="main-title">风水布局</text>
      <text class="sub-title">趋吉避凶，改善运势</text>
    </view>
  </view>

  <!-- 信息输入表单 -->
  <view class="form-section">
    <view class="form-title">房屋信息</view>
    
    <!-- 房屋类型 -->
    <view class="form-item">
      <text class="label">房屋类型</text>
      <picker mode="selector" range="{{houseTypes}}" value="{{houseTypeIndex}}" bindchange="onHouseTypeChange">
        <view class="picker {{houseTypeIndex > -1 ? '' : 'placeholder'}}">
          {{houseTypeIndex > -1 ? houseTypes[houseTypeIndex] : '请选择房屋类型'}}
        </view>
      </picker>
    </view>

    <!-- 房屋朝向 -->
    <view class="form-item">
      <text class="label">房屋朝向</text>
      <picker mode="selector" range="{{directions}}" value="{{directionIndex}}" bindchange="onDirectionChange">
        <view class="picker {{directionIndex > -1 ? '' : 'placeholder'}}">
          {{directionIndex > -1 ? directions[directionIndex] : '请选择房屋朝向'}}
        </view>
      </picker>
    </view>

    <!-- 建筑年份 -->
    <view class="form-item">
      <text class="label">建筑年份</text>
      <picker mode="date" fields="year" value="{{buildYear}}" bindchange="onBuildYearChange">
        <view class="picker {{buildYear ? '' : 'placeholder'}}">
          {{buildYear || '请选择建筑年份'}}
        </view>
      </picker>
    </view>

    <!-- 房屋面积 -->
    <view class="form-item">
      <text class="label">房屋面积（平方米）</text>
      <input class="input" type="digit" placeholder="请输入房屋面积" model:value="{{area}}" bindinput="onAreaInput"/>
    </view>

    <!-- 户型选择 -->
    <view class="form-item">
      <text class="label">户型结构</text>
      <view class="room-picker">
        <view class="room-item">
          <text>室</text>
          <picker mode="selector" range="{{roomNums}}" value="{{roomNumIndex}}" bindchange="onRoomNumChange">
            <view class="number-picker">{{roomNumIndex > -1 ? roomNums[roomNumIndex] : '0'}}</view>
          </picker>
        </view>
        <view class="room-item">
          <text>厅</text>
          <picker mode="selector" range="{{hallNums}}" value="{{hallNumIndex}}" bindchange="onHallNumChange">
            <view class="number-picker">{{hallNumIndex > -1 ? hallNums[hallNumIndex] : '0'}}</view>
          </picker>
        </view>
        <view class="room-item">
          <text>卫</text>
          <picker mode="selector" range="{{bathNums}}" value="{{bathNumIndex}}" bindchange="onBathNumChange">
            <view class="number-picker">{{bathNumIndex > -1 ? bathNums[bathNumIndex] : '0'}}</view>
          </picker>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? '' : 'disabled'}}" bindtap="onAnalyze" disabled="{{!canSubmit}}">
      <text>开始分析</text>
      <text class="price" wx:if="{{price > 0}}">￥{{price}}</text>
    </button>
  </view>

  <!-- 分析结果 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 整体评分 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/score.png"/>
        <text>风水评分</text>
      </view>
      <view class="score-display">
        <view class="score">{{totalScore}}</view>
        <view class="max-score">/100</view>
      </view>
      <view class="score-desc">{{scoreDesc}}</view>
    </view>

    <!-- 八方位分析 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/bagua.png"/>
        <text>八方位分析</text>
      </view>
      <view class="bagua-chart">
        <canvas canvas-id="baguaCanvas" class="bagua-canvas"></canvas>
      </view>
      <view class="direction-analysis">{{directionAnalysis}}</view>
    </view>

    <!-- 五行分析 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/wuxing.png"/>
        <text>五行分析</text>
      </view>
      <view class="wuxing-chart">
        <canvas canvas-id="wuxingCanvas" class="wuxing-canvas"></canvas>
      </view>
      <view class="wuxing-analysis">{{wuxingAnalysis}}</view>
    </view>

    <!-- 布局建议 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/advice.png"/>
        <text>布局建议</text>
      </view>
      <view class="advice-list">
        <view class="advice-item" wx:for="{{adviceList}}" wx:key="title">
          <view class="advice-title">{{item.title}}</view>
          <view class="advice-content">{{item.content}}</view>
        </view>
      </view>
    </view>

    <!-- 化解方案 -->
    <view class="analysis-card">
      <view class="card-title">
        <image class="title-icon" src="/assets/icons/solution.png"/>
        <text>化解方案</text>
      </view>
      <view class="solution-list">
        <view class="solution-item" wx:for="{{solutionList}}" wx:key="type">
          <view class="solution-type">{{item.type}}</view>
          <view class="solution-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部分享按钮 -->
  <view class="share-section" wx:if="{{showResult}}">
    <button class="share-btn" open-type="share">
      <image class="share-icon" src="/assets/icons/share.png"/>
      <text>分享分析结果</text>
    </button>
  </view>
</view> 