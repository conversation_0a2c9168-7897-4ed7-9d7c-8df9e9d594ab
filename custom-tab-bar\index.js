Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#9575cd",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/home.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/home.png"
      },
      {
        pagePath: "/pages/ai-chat/ai-chat",
        text: "AI问答",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/artificial-intelligence.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/artificial-intelligence.png",
        isSpecial: true
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/user-male-circle.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/user-male-circle.png"
      }
    ]
  },
  attached() {
    // 延迟执行，确保页面已经完全加载
    setTimeout(() => {
      this.setSelected()
    }, 100)
  },
  show() {
    // 页面显示时更新选中状态
    this.setSelected()
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    },
    setSelected() {
      try {
        const pages = getCurrentPages()
        if (!pages || pages.length === 0) {
          console.warn('No pages found, using default selected index')
          return
        }

        const currentPage = pages[pages.length - 1]
        if (!currentPage || !currentPage.route) {
          console.warn('Current page or route not found, using default selected index')
          return
        }

        const url = '/' + currentPage.route
        const selected = this.data.list.findIndex(item => {
          if (url.includes('ai-chat') && item.pagePath.includes('ai-chat')) {
            return true
          }
          return item.pagePath === url
        })

        if (selected !== -1) {
          this.setData({ selected })
        }
      } catch (error) {
        console.error('Error in setSelected:', error)
        // 使用默认选中状态
        this.setData({ selected: 0 })
      }
    }
  }
}) 