// yijing.js
const { checkBirthInfo, navigateToBirthInfo, getBirthInfo } = require('../../utils/util.js')

Page({
  data: {
    question: '',
    name: '',
    gender: '男',
    loading: false,
    result: null,
    birthInfo: null
  },

  onLoad() {
    // 检查是否有出生信息
    if (!checkBirthInfo()) {
      navigateToBirthInfo('/pages/yijing/yijing')
      return
    }
    
    // 从本地存储读取出生信息
    this.loadBirthInfo()
  },

  onShow() {
    // 每次页面显示时重新加载出生信息，确保数据是最新的
    this.loadBirthInfo()
  },

  // 加载出生信息
  loadBirthInfo() {
    // 使用工具函数获取出生信息，确保即使某些字段缺失也能正常显示
    const birthInfo = getBirthInfo()
    this.setData({
      name: birthInfo.name,
      gender: birthInfo.gender,
      birthInfo: birthInfo
    })
  },

  // 处理问题输入
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    })
  },

  onSubmit() {
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入问题',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    // 这里添加您的易经占卜逻辑
    // 示例结果
    setTimeout(() => {
      this.setData({
        loading: false,
        result: {
          name: '乾卦',
          lines: [1, 1, 1, 1, 1, 1],
          interpretation: '乾卦代表天，象征着强健、刚毅、充满活力。暗示着事业正处于上升期，充满机遇。',
          advice: '建议把握机会，积极进取，但需谨记刚柔并济，不可过于刚强。'
        }
      })
    }, 1500)
  },

  // 处理出生信息组件保存事件
  onBirthInfoSave(e) {
    // 重新加载出生信息
    this.loadBirthInfo()
  },

  // 计算易经结果
  calculateYijing() {
    try {
      const { birthDate, birthTime, gender, question } = this.data
      
      if (!birthDate || !birthTime || !gender) {
        wx.showToast({
          title: '请填写完整的出生信息',
          icon: 'none'
        })
        return
      }

      if (!question) {
        wx.showToast({
          title: '请输入您的问题',
          icon: 'none'
        })
        return
      }

      // 显示加载状态
      this.setData({ loading: true })
      wx.showLoading({
        title: '正在占卜...',
        mask: true
      })

      // 调用易经计算器
      const result = yijing.calculate({
        birthDate,
        birthTime,
        question
      })

      // 隐藏加载状态
      this.setData({ 
        result: result,
        loading: false
      })
      wx.hideLoading()

      // 保存结果到历史记录
      this.saveToHistory(result)
      
      // 滚动到结果区域
      wx.pageScrollTo({
        scrollTop: 1000,
        duration: 300
      })
    } catch (error) {
      console.error('易经计算出错：', error)
      this.setData({ loading: false })
      wx.hideLoading()
      wx.showToast({
        title: '计算出错，请重试',
        icon: 'none'
      })
    }
  },

  // 保存到历史记录
  saveToHistory(result) {
    try {
      const history = wx.getStorageSync('yijingHistory') || []
      const newRecord = {
        ...result,
        timestamp: new Date().toISOString(),
        question: this.data.question,
        birthDate: this.data.birthDate,
        birthTime: this.data.birthTime,
        gender: this.data.gender
      }
      
      history.unshift(newRecord)
      
      // 只保留最近50条记录
      if (history.length > 50) {
        history.pop()
      }
      
      wx.setStorageSync('yijingHistory', history)
      this.setData({ history })
    } catch (error) {
      console.error('保存历史记录失败：', error)
    }
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 重置表单
  resetForm() {
    this.setData({
      question: '',
      result: null,
      loading: false
    })
  },

  // 分享小程序
  onShareAppMessage() {
    const { result, question } = this.data
    let title = '易经卦象占卜'
    
    if (result && question) {
      title = `我的易经卦象：${result.name}卦 - ${question.substring(0, 10)}...`
    }
    
    return {
      title,
      path: '/pages/yijing/yijing',
      imageUrl: '/images/share/yijing.png'
    }
  }
}) 