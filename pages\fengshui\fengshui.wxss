/* pages/fengshui/fengshui.wxss */
page {
  background-color: var(--background-color) !important;
}

.container {
  padding: 24rpx;
  background-color: var(--background-color) !important;
  min-height: 100vh;
}

/* 上传区域样式 */
.upload-section {
  background: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.upload-title {
  font-size: 34rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 24rpx;
  text-align: center;
}

.upload-area {
  height: 320rpx;
  border: 2rpx dashed var(--primary-light);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--primary-lightest);
  transition: all 0.3s ease;
}

.upload-area:active {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.upload-icon {
  width: 88rpx;
  height: 88rpx;
  margin-bottom: 24rpx;
  opacity: 0.8;
}

.upload-text {
  font-size: 30rpx;
  color: var(--primary-color);
}

.image-preview {
  width: 100%;
  background: var(--primary-lightest);
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid var(--border-color);
}

.image-preview image {
  width: 100%;
  height: 420rpx;
  border-radius: 12rpx;
}

.image-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  background: var(--primary-color);
  color: #FFFFFF;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
  text-align: center;
  border: none;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

.action-btn:last-child {
  background: var(--primary-light);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 0;
  background: var(--card-background);
  border-radius: 16rpx;
  margin: 24rpx 0;
  border: 2rpx solid var(--border-color);
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid var(--primary-lightest);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 30rpx;
  color: var(--primary-color);
}

/* 错误提示样式 */
.error-container {
  background: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.error-text {
  color: var(--error-color);
  font-size: 30rpx;
  margin-bottom: 24rpx;
}

.retry-button {
  background: var(--primary-color);
  color: #FFFFFF;
  font-size: 30rpx;
  padding: 20rpx 48rpx;
  border-radius: 8rpx;
  display: inline-block;
  border: none;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

/* 分析结果样式 */
.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin: 32rpx 0 24rpx;
  padding-left: 24rpx;
  border-left: 8rpx solid var(--primary-color);
}

.bagua-section,
.furniture-section,
.color-section,
.optimization-section {
  background: var(--card-background);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.bagua-item,
.furniture-item,
.color-item,
.optimization-item {
  padding: 24rpx;
  border-bottom: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
  background: var(--primary-lightest);
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}

.bagua-item:active,
.furniture-item:active,
.color-item:active,
.optimization-item:active {
  background: var(--primary-light);
  transform: translateY(2rpx);
}

.bagua-item:last-child,
.furniture-item:last-child,
.color-item:last-child,
.optimization-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.item-title {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-bottom: 12rpx;
  font-weight: bold;
}

.item-content {
  font-size: 30rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

.color-text {
  margin-right: 24rpx;
}

.color-preview {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  margin-left: 12rpx;
  border: 2rpx solid var(--border-color);
}

/* 空状态提示样式 */
.empty-tip {
  text-align: center;
  padding: 120rpx 0;
  background: var(--card-background);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.tip-text {
  font-size: 30rpx;
  color: var(--text-light);
  line-height: 1.6;
}

/* 输入区域样式 */
.input-section,
.result-section,
.analysis-section,
.advice-section {
  display: none;
}